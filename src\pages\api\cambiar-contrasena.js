import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function cambiarContrasenaRoute(req, res) {
  try {
    const { id } = req.query;
    const { data } = await fetchApi.put(
      `/registro/usuario/pwd/${id}`,
      req.body,
      {
        headers: getDefaultHeaders(req),
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(cambiarContrasenaRoute);

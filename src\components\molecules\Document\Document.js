import React, { useMemo } from 'react';
import dayjs from 'dayjs';
import Card, { options } from '@/components/atoms/Card';
import Paragraph from '@/components/atoms/Paragraph';
import Spacer from '@/components/layout/Spacer';
import Check from '@/components/atoms/Check';
import PropTypes from 'prop-types';
import { SIGN_TYPES } from '@/utils/constants';
import styles from './Document.module.css';

export function Document({
  nombreArchivo,
  fechaRegistro,
  tipoFirma,
  descripcion,
  onClick,
  isChecked,
  hasCheck,
  moreInfo,
  border,
  color,
  esSolicitud,
  firmasRequeridas,
}) {
  const textColor = useMemo(() => (color !== 'base' ? 'inverted' : 'base'), [color]);

  const signType = useMemo(() => {
    if (esSolicitud === 'SI' && tipoFirma === SIGN_TYPES.OTHERS_AND_ME) {
      return SIGN_TYPES.OTHERS_AND_OWNER;
    }

    if (esSolicitud === 'SI' && tipoFirma === SIGN_TYPES.OTHERS && +firmasRequeridas === 1) {
      return SIGN_TYPES.ME;
    }

    return tipoFirma;
  }, [esSolicitud, tipoFirma, firmasRequeridas]);

  return (
    <Card border={border} isClickable onClick={onClick} color={color} style={{ position: 'relative' }}>
      <div>
        <Paragraph
          size="xs"
          weight="semibold"
          isTruncate={color === 'base'}
          color={textColor}
        >
          {nombreArchivo}
        </Paragraph>
        <Paragraph size="xs" color={textColor}>{dayjs(fechaRegistro).format('DD/MM/YYYY')}</Paragraph>
        {Object.keys(moreInfo || {})?.map((key) => (
          typeof moreInfo[key] === 'string'
            ? <Paragraph key={key} size="xs" color={textColor}>{moreInfo[key]}</Paragraph>
            : moreInfo[key]
        ))}
        {descripcion && (
        <>
          <Spacer.Vertical size="xs" />
          <Paragraph
            size="xs"
            color={textColor}
            className={styles.descripcionTruncate}
            title={descripcion}
          >
            {descripcion}
          </Paragraph>
        </>
        )}
      </div>
      {hasCheck && (
        <div style={{ bottom: '10px', position: 'absolute' }}>
          <Spacer.Vertical size="xs" />
          <div className={styles['card-actions']}>
            <Check isChecked={isChecked} />
            {tipoFirma !== 'Sin firmar' && (
            <i className={styles['sign-type']}>
                {signType}
            </i>
            )}
          </div>
        </div>
      )}
    </Card>
  );
}

Document.propTypes = {
  nombreArchivo: PropTypes.string.isRequired,
  fechaRegistro: PropTypes.string.isRequired,
  descripcion: PropTypes.string,
  tipoFirma: PropTypes.string,
  onClick: PropTypes.func,
  isChecked: PropTypes.bool,
  hasCheck: PropTypes.bool,
  // eslint-disable-next-line react/forbid-prop-types
  moreInfo: PropTypes.object,
  border: PropTypes.oneOf(options.colors),
  color: PropTypes.oneOf(options.colors),
};

Document.defaultProps = {
  tipoFirma: 'Sin firmar',
  onClick: () => { /* */
  },
  isChecked: false,
  hasCheck: true,
  moreInfo: {},
  border: 'primary',
  color: 'base',
  descripcion: '',
};

export default Document;

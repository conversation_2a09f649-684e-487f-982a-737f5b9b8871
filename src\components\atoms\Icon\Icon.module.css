.icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  user-select: none;
}

.size-sm {
  padding: 5px;
}

.size-md {
  padding: 8px;
}

.size-lg {
  padding: 12px;
}

.size-xl {
  padding: 15px;
}

.color-base path,
.color-base circle {
  fill: var(--color-font-base);
}

.color-base line,
.color-base rect {
  stroke: var(--color-font-base);
}
.color-highlight path,
.color-highlight circle {
  fill: var(--color-font-highlight);
}

.color-highlight line,
.color-highlight rect {
  stroke: var(--color-font-highlight);
}

.color-shinyShamrock path,
.color-shinyShamrock circle {
  fill: var(--color-shiny-shamrock);
}

.color-shinyShamrock line,
.color-shinyShamrock rect {
  stroke: var(--color-shiny-shamrock);
}

.color-muted path,
.color-muted circle {
  fill: var(--color-font-muted);
}

.color-muted line,
.color-muted rect {
  stroke: var(--color-font-muted);
}

.color-primary path,
.color-primary circle {
  fill: var(--color-primary);
}

.color-primary line,
.color-primary rect {
  stroke: var(--color-primary);
}

.color-secondary path,
.color-secondary circle {
  fill: var(--color-secondary);
}

.color-secondary line,
.color-secondary rect {
  stroke: var(--color-secondary);
}

.color-inverted path,
.color-inverted circle {
  fill: var(--color-primary-inverted);
}

.color-inverted line,
.color-inverted rect {
  stroke: var(--color-primary-inverted);
}


.color-danger path,
.color-danger circle {
  fill: var(--color-red-500);
}

.color-danger line,
.color-danger rect {
  stroke: var(--color-red-500);
}

.icon rect:global(.transparent) {
  fill: var(--color-base-transparent) !important;
}

.background-base {
  background: var(--color-gray-100);
  border-radius: var(--border-radius-full);
}

.background-transparent {
  background: var(--color-base-transparent);
}

.background-highlight {
  background: var(--color-primary-highlight);
  border-radius: var(--border-radius-full);
}

.background-shinyShamrock {
  background: var(--color-shiny-shamrock);
  border-radius: var(--border-radius-full);
}

.background-muted {
  background: var(--color-primary-muted);
  border-radius: var(--border-radius-full);
}

.background-inverted {
  border: var(--picture-border);
  background: var(--color-font-inverted);
  border-radius: var(--border-radius-full);
  box-shadow: var(--box-shadow-sm);
}

.background-spotlight {
  background: var(--color-yellow-100);
  border-radius: var(--border-radius-full);
  box-shadow: var(--box-shadow-sm);
}

.background-danger {
  background: var(--color-red-500);
  border-radius: var(--border-radius-full);
  box-shadow: var(--box-shadow-sm);
}

.background-brand {
  background: var(--color-primary);
  transform: rotate(68deg);
  border-radius: var(--border-radius-sm);
}

.background-brand svg {
  transform: rotate(-68deg);
}


.background-brand svg[stroke="currentColor"] {
  stroke: var(--color-base-white);
}

.background-brand svg path {
  fill: var(--color-base-white);
}

.background-brand svg[stroke="currentColor"] path {
  fill: none;
}
.background-brand.size-lg {
  padding: 5px;
}

.is-clickable {
  cursor: pointer;
}

.is-disable.is-clickable {
  filter: opacity(.5);
  cursor: not-allowed;
}

.border-none {
  border: var(--border-width-thin) solid var(--color-base-transparent);
}

.border-primary {
  border: var(--border-width-thin) solid var(--color-primary);
  border-radius: var(--border-radius-full);
}

.border-secondary {
  border: var(--border-width-thin) solid var(--color-secondary);
  border-radius: var(--border-radius-full);
}

.border-danger {
  border: var(--border-width-thin) solid var(--color-red-500);
  border-radius: var(--border-radius-full);
}

.border-base {
  border: var(--border-width-thin) solid var(--color-font-base);
  border-radius: var(--border-radius-full);
}

.border-shinyShamrock {
  border: var(--border-width-thin) solid var(--color-shiny-shamrock);
  border-radius: var(--border-radius-full);
}

.is-centered {
  margin: 0 auto;
}

.not-padded {
  padding: 0;
}
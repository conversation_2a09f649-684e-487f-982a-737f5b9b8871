import React, { useState, useEffect } from 'react';
// eslint-disable-next-line import/no-extraneous-dependencies
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import Heading from '@/components/atoms/Heading';
import Spacer from '@/components/layout/Spacer';
import Paragraph from '@/components/atoms/Paragraph';
import PropTypes from 'prop-types';
import Modal from '@/components/atoms/Modal';
import Button from '@/components/atoms/Button';

function OrisSignersTable({
  signers,
  handleDelete,
  handleMoveUp,
  handleMoveDown,
  handleDragEnd,
  handleRoleUpdate,
  tipoOrdenFirma,
  isOrisModule = true,
  onTableConfirmationChange, // Nueva prop para comunicar cambios de confirmación
}) {
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedSigner, setSelectedSigner] = useState(null);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [isTableConfirmed, setIsTableConfirmed] = useState(false);
  const [editingRoles, setEditingRoles] = useState({});

  // Efecto para notificar cambios en el estado de confirmación
  useEffect(() => {
    if (onTableConfirmationChange) {
      onTableConfirmationChange(isTableConfirmed);
    }
  }, [isTableConfirmed, onTableConfirmationChange]);

  // Función para actualizar el rol de un firmante
  const handleRoleChange = (numeroDocumento, nuevoRol) => {
    // Resetear confirmación al cambiar roles
    setIsTableConfirmed(false);

    // Crear función de actualización similar a las existentes
    const signerIndex = signers.findIndex((s) => s.numeroDocumento === numeroDocumento);
    if (signerIndex !== -1) {
      const updatedSigners = [...signers];
      updatedSigners[signerIndex] = {
        ...updatedSigners[signerIndex],
        rol: nuevoRol,
      };

      // Llamar a la función de actualización del componente padre
      if (handleRoleUpdate) {
        handleRoleUpdate(updatedSigners);
      }
    }
  };

  // Función para entrar en modo edición de rol
  const startEditingRole = (numeroDocumento) => {
    setEditingRoles((prev) => ({
      ...prev,
      [numeroDocumento]: true,
    }));
  };

  // Función para salir del modo edición
  const stopEditingRole = (numeroDocumento) => {
    setEditingRoles((prev) => {
      const newState = { ...prev };
      delete newState[numeroDocumento];
      return newState;
    });
  };

  // Opciones de roles
  const roleOptions = [
    { text: 'Proyectó', value: 'Proyectó' },
    { text: 'Revisó', value: 'Revisó' },
    { text: 'Aprobó', value: 'Aprobó' },
    { text: 'Firmante', value: 'Firmante' },
  ];

  // Función para saber si el rol es editable
  const isRoleEditable = () => {
    // En ORIS: Solo editable en modo SECUENCIAL
    if (isOrisModule) {
      return tipoOrdenFirma === 'SECUENCIAL';
    }
    // En "Otros y yo": Siempre editable en modo SECUENCIAL,
    // independientemente de si es el usuario solicitante o no
    return tipoOrdenFirma === 'SECUENCIAL';
  };

  const onDelete = (signer) => {
    setSelectedSigner(signer);
    setShowDeleteModal(true);
  };

  const onConfirmDelete = () => {
    handleDelete(selectedSigner.numeroDocumento);
    setShowDeleteModal(false);
    setIsTableConfirmed(false); // Resetear confirmación al eliminar
  };

  const onDragEnd = (result) => {
    if (!result.destination) return;

    if (result.source.index === result.destination.index) return;

    handleDragEnd(result.source.index, result.destination.index);
    setIsTableConfirmed(false); // Resetear confirmación al reorganizar
  };

  const handleConfirmTable = () => {
    setShowConfirmModal(true);
  };

  // Wrappers para los manejadores de movimiento
  const handleMoveUpWithReset = (index) => {
    handleMoveUp(index);
    setIsTableConfirmed(false);
  };

  const handleMoveDownWithReset = (index) => {
    handleMoveDown(index);
    setIsTableConfirmed(false);
  };

  const getOrderValidationQuestions = () => {
    const questions = [];
    // Definir jerarquía de roles (de menor a mayor)
    const roleHierarchy = {
      Proyectó: 1,
      Revisó: 2,
      Aprobó: 3,
      Firmante: 4,
    };

    for (let i = 0; i < signers.length - 1; i += 1) {
      const currentSigner = signers[i];
      const nextSigner = signers[i + 1];

      const currentRoleLevel = roleHierarchy[currentSigner.rol] || 0;
      const nextRoleLevel = roleHierarchy[nextSigner.rol] || 0;

      // Si el rol actual tiene menor jerarquía que el siguiente (está mal ordenado)
      if (currentRoleLevel > nextRoleLevel && currentRoleLevel > 0 && nextRoleLevel > 0) {
        questions.push(`¿Está seguro que "${currentSigner.nombreCompleto}" (${currentSigner.rol}) debe firmar antes que "${nextSigner.nombreCompleto}" (${nextSigner.rol})? Normalmente ${nextSigner.rol} debería firmar antes que ${currentSigner.rol}.`);
      }
    }

    return questions;
  };

  if (signers.length === 0) {
    return (
      <div>
        <Heading size="md">Tabla de firmantes</Heading>
        <Spacer.Vertical size="sm" />
        <Paragraph color="muted">No hay firmantes agregados aún.</Paragraph>
      </div>
    );
  }

  const validationQuestions = getOrderValidationQuestions();

  return (
    <>
      {showDeleteModal && (
        <Modal
          onClose={() => setShowDeleteModal(false)}
          title="Eliminar firmantes"
          isInline
          type="secondary"
        >
          <Paragraph>
            ¿Está seguro que desea eliminar el firmante
            {' '}
            {selectedSigner?.nombreCompleto}
            ?
          </Paragraph>
          <Spacer.Vertical size="sm" />
          <div style={{ textAlign: 'right' }}>
            <Button
              type="base"
              onClick={() => setShowDeleteModal(false)}
              isInline
            >
              Cancelar
            </Button>
            <Spacer.Horizontal size="sm" />
            <Button
              onClick={onConfirmDelete}
              type="danger"
              size="sm"
              isInline
            >
              Eliminar
            </Button>
          </div>
        </Modal>
      )}

      {showConfirmModal && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            zIndex: 9999,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onClick={() => setShowConfirmModal(false)}
        >
          <div
            style={{
              backgroundColor: '#ffffff',
              borderRadius: '8px',
              boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
              maxWidth: '800px',
              width: '90%',
              maxHeight: '80vh',
              overflow: 'hidden',
              position: 'relative',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header del modal */}
            <div style={{
              padding: '16px 20px',
              borderBottom: '1px solid #e0e0e0',
              backgroundColor: '#ffffff',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
            >
              <h2 style={{
                margin: 0,
                fontSize: '18px',
                fontWeight: '600',
                color: '#333',
              }}
              >
                Confirmar tabla de firmantes
              </h2>
              <button
                type="button"
                onClick={() => setShowConfirmModal(false)}
                style={{
                  background: 'none',
                  border: 'none',
                  fontSize: '20px',
                  cursor: 'pointer',
                  color: '#666',
                  padding: '4px',
                }}
              >
                ×
              </button>
            </div>

            {/* Contenido del modal */}
            <div style={{
              maxHeight: '60vh',
              overflowY: 'auto',
              backgroundColor: '#ffffff',
              padding: '16px 20px',
            }}
            >
              <Paragraph size="sm" weight="semibold">
                Resumen de firmantes en orden de firma:
              </Paragraph>
              <Spacer.Vertical size="sm" />

              {/* Tabla de resumen */}
              <div style={{
                border: '1px solid #e0e0e0',
                borderRadius: '6px',
                overflow: 'hidden',
                marginBottom: '16px',
                backgroundColor: '#ffffff',
              }}
              >
                <div style={{
                  display: 'grid',
                  gridTemplateColumns: '60px 1fr 120px',
                  backgroundColor: '#ffffff',
                  padding: '8px 12px',
                  fontSize: '12px',
                  fontWeight: '600',
                  color: '#495057',
                  borderBottom: '1px solid #e0e0e0',
                }}
                >
                  <div>Orden</div>
                  <div>Nombre</div>
                  <div>Rol</div>
                </div>
                {signers.map((signer, index) => (
                  <div
                    key={signer.numeroDocumento}
                    style={{
                      display: 'grid',
                      gridTemplateColumns: '60px 1fr 120px',
                      padding: '8px 12px',
                      fontSize: '13px',
                      borderBottom: index === signers.length - 1 ? 'none' : '1px solid #f0f0f0',
                      backgroundColor: '#ffffff',
                    }}
                  >
                    <div style={{ fontWeight: 'bold', color: '#33CC99' }}>{index + 1}</div>
                    <div>{signer.nombreCompleto}</div>
                    <div>{signer.rol}</div>
                  </div>
                ))}
              </div>

              {/* Preguntas de validación */}
              {validationQuestions.length > 0 && (
                <div style={{ backgroundColor: '#ffffff' }}>
                  <Paragraph size="sm" weight="semibold" style={{ color: '#dc3545', backgroundColor: '#ffffff' }}>
                    ⚠️ Advertencias sobre el orden:
                  </Paragraph>
                  <Spacer.Vertical size="xs" />
                  {validationQuestions.map((question, index) => (
                    <div
                      key={index}
                      style={{
                        backgroundColor: '#fff3cd',
                        border: '1px solid #ffeaa7',
                        borderRadius: '4px',
                        padding: '8px 12px',
                        marginBottom: '8px',
                      }}
                    >
                      <Paragraph size="xs">{question}</Paragraph>
                    </div>
                  ))}
                  <Spacer.Vertical size="sm" />
                </div>
              )}

              <div style={{ backgroundColor: '#ffffff' }}>
                <Paragraph size="sm" weight="semibold">
                  ¿Confirmas el orden de la tabla de firmantes?
                </Paragraph>
                <Spacer.Vertical size="xs" />
                <Paragraph size="xs" color="muted">
                  Una vez confirmado, este será el orden en que los firmantes deberán
                  firmar el documento.
                </Paragraph>
              </div>
            </div>

            {/* Footer con botones */}
            <div style={{
              padding: '16px 20px',
              borderTop: '1px solid #e0e0e0',
              backgroundColor: '#ffffff',
              textAlign: 'right',
            }}
            >
              <Button
                type="base"
                onClick={() => setShowConfirmModal(false)}
                isInline
              >
                Revisar orden
              </Button>
              <Spacer.Horizontal size="sm" />
              <Button
                onClick={() => {
                  setShowConfirmModal(false);
                  setIsTableConfirmed(true); // Confirmar tabla
                }}
                type="primary"
                isInline
              >
                Confirmar orden
              </Button>
            </div>
          </div>
        </div>
      )}

      <div>
        <h3 style={{
          fontSize: '18px',
          fontWeight: '500',
          color: '#333',
          marginBottom: '16px',
          fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
        }}
        >
          Tabla de firmantes
        </h3>

        {/* Mensaje de confirmación */}
        {isTableConfirmed && (
          <div style={{
            padding: '10px 16px',
            backgroundColor: '#e6f7f0',
            borderRadius: '6px',
            border: '1px solid #33CC99',
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
          }}
          >
            <div style={{ color: '#33CC99', fontSize: '16px' }}>✓</div>
            <p style={{
              margin: '0',
              color: '#2a6d5d',
              fontSize: '14px',
              fontWeight: '500',
              fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
            }}
            >
              Tabla actual confirmada
            </p>
          </div>
        )}

        <div style={{
          backgroundColor: '#ffffff',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          overflow: 'hidden',
          boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        }}
        >
          {/* Header */}
          <div style={{
            display: 'grid',
            gridTemplateColumns: '80px 1fr 1fr 120px 100px',
            backgroundColor: '#f8f9fa',
            padding: '12px 16px',
            fontSize: '14px',
            fontWeight: '600',
            color: '#495057',
            borderBottom: '1px solid #e0e0e0',
            fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
          }}
          >
            <div>Orden</div>
            <div>Nombre</div>
            <div>Correo Electrónico</div>
            <div>Rol</div>
            <div>Acciones</div>
          </div>

          {/* Drag and Drop Container */}
          <DragDropContext onDragEnd={onDragEnd}>
            <Droppable droppableId="signers-table">
              {(droppableProvided, droppableSnapshot) => (
                <div
                  {...droppableProvided.droppableProps}
                  ref={droppableProvided.innerRef}
                  style={{
                    backgroundColor: droppableSnapshot.isDraggingOver ? '#f0f8ff' : 'transparent',
                  }}
                >
                  {signers.map((signer, index) => (
                    <Draggable
                      key={signer.numeroDocumento || `signer-${index}`}
                      draggableId={signer.numeroDocumento || `signer-${index}`}
                      index={index}
                    >
                      {(draggableProvided, draggableSnapshot) => (
                        <div
                          ref={draggableProvided.innerRef}
                          {...draggableProvided.draggableProps}
                          style={{
                            ...draggableProvided.draggableProps.style,
                            transform: draggableSnapshot.isDragging
                              ? draggableProvided.draggableProps.style?.transform
                              : 'none',
                          }}
                        >
                          <div style={{
                            display: 'grid',
                            gridTemplateColumns: '80px 1fr 1fr 120px 100px',
                            padding: '12px 16px',
                            alignItems: 'center',
                            borderBottom: index === signers.length - 1 ? 'none' : '1px solid #f0f0f0',
                            backgroundColor: draggableSnapshot.isDragging ? '#fff3cd' : '#ffffff',
                            fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                            cursor: draggableSnapshot.isDragging ? 'grabbing' : 'grab',
                            boxShadow: draggableSnapshot.isDragging ? '0 4px 8px rgba(0,0,0,0.15)' : 'none',
                            borderRadius: draggableSnapshot.isDragging ? '4px' : '0',
                          }}
                          >
                            {/* Columna de orden con drag handle */}
                            <div style={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: '8px',
                              width: '100%',
                            }}
                            >
                              {/* Drag Handle */}
                              <div
                                {...draggableProvided.dragHandleProps}
                                style={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  alignItems: 'center',
                                  cursor: 'grab',
                                  padding: '4px',
                                  color: '#666',
                                  fontSize: '12px',
                                }}
                                title="Arrastra para reordenar"
                              >
                                ⋮⋮
                              </div>

                              <span style={{
                                fontWeight: 'bold',
                                fontSize: '16px',
                                color: '#333',
                                minWidth: '20px',
                              }}
                              >
                                {signer.orden || index + 1}
                              </span>
                            </div>

                            {/* Nombre */}
                            <div style={{
                              fontSize: '14px',
                              color: '#333',
                              fontWeight: '400',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                            >
                              {signer.nombreCompleto}
                            </div>

                            {/* Email */}
                            <div style={{
                              fontSize: '14px',
                              color: '#333',
                              fontWeight: '400',
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap',
                            }}
                            >
                              {signer.email}
                            </div>

                            {/* Rol - Editable solo en secuencial */}
                            <div style={{
                              fontSize: '14px',
                              color: '#333',
                              fontWeight: '400',
                              textAlign: 'center',
                              minWidth: '120px',
                            }}
                            >
                              {editingRoles[signer.numeroDocumento] && isRoleEditable() ? (
                                <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
                                  <select
                                    value={signer.rol}
                                    onChange={(e) => handleRoleChange(
                                      signer.numeroDocumento,
                                      e.target.value,
                                    )}
                                    onBlur={() => stopEditingRole(signer.numeroDocumento)}
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        stopEditingRole(signer.numeroDocumento);
                                      }
                                    }}
                                    style={{
                                      fontSize: '12px',
                                      padding: '2px 4px',
                                      border: '1px solid #ddd',
                                      borderRadius: '3px',
                                      backgroundColor: '#fff',
                                      width: '100px',
                                    }}
                                    autoFocus
                                  >
                                    {roleOptions.map((option) => (
                                      <option key={option.value} value={option.value}>
                                        {option.text}
                                      </option>
                                    ))}
                                  </select>
                                  <button
                                    type="button"
                                    onClick={() => stopEditingRole(signer.numeroDocumento)}
                                    style={{
                                      background: 'none',
                                      border: 'none',
                                      color: '#33CC99',
                                      cursor: 'pointer',
                                      fontSize: '12px',
                                      padding: '2px',
                                    }}
                                    title="Confirmar cambio"
                                  >
                                    ✓
                                  </button>
                                </div>
                              ) : (
                                <div
                                  style={{
                                    cursor: isRoleEditable() ? 'pointer' : 'default',
                                    padding: '4px',
                                    borderRadius: '3px',
                                    transition: 'background-color 0.2s',
                                    opacity: isRoleEditable() ? 1 : 0.7,
                                  }}
                                  onClick={() => {
                                    if (isRoleEditable()) {
                                      startEditingRole(signer.numeroDocumento);
                                    }
                                  }}
                                  onMouseEnter={(e) => {
                                    if (isRoleEditable()) {
                                      e.target.style.backgroundColor = '#f5f5f5';
                                    }
                                  }}
                                  onMouseLeave={(e) => {
                                    if (isRoleEditable()) {
                                      e.target.style.backgroundColor = 'transparent';
                                    }
                                  }}
                                  title={(() => {
                                    if (isRoleEditable()) {
                                      return signer.esUsuarioSolicitante
                                        ? 'Clic para editar tu rol'
                                        : 'Clic para editar rol';
                                    }
                                    if (tipoOrdenFirma === 'PARALELO') {
                                      return 'Los roles no se pueden editar en firma paralelo';
                                    }
                                    return 'Rol no editable en este modo';
                                  })()}
                                >
                                  {signer.rol}
                                  {isRoleEditable() && (
                                    <span style={{
                                      marginLeft: '4px',
                                      fontSize: '10px',
                                      color: '#666',
                                      opacity: 0.7,
                                    }}
                                    >
                                      ✏️
                                    </span>
                                  )}
                                </div>
                              )}
                            </div>

                            {/* Acciones */}
                            <div style={{
                              textAlign: 'center',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              gap: '8px',
                            }}
                            >
                              {/* Flechas de reordenamiento */}
                              {signers.length > 1 && (
                                <div style={{
                                  display: 'flex',
                                  flexDirection: 'column',
                                  gap: '2px',
                                }}
                                >
                                  <button
                                    type="button"
                                    onClick={() => handleMoveUpWithReset
                                    && handleMoveUpWithReset(index)}
                                    disabled={index === 0}
                                    style={{
                                      backgroundColor: index === 0 ? '#e9ecef' : '#33CC99',
                                      border: '1px solid #ddd',
                                      borderRadius: '4px',
                                      cursor: index === 0 ? 'not-allowed' : 'pointer',
                                      opacity: index === 0 ? 0.5 : 1,
                                      padding: '4px 6px',
                                      fontSize: '10px',
                                      color: index === 0 ? '#6c757d' : '#ffffff',
                                      lineHeight: '1',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      width: '22px',
                                      height: '18px',
                                      fontWeight: 'bold',
                                      transition: 'all 0.2s ease',
                                    }}
                                    title="Mover hacia arriba"
                                    onMouseEnter={(e) => {
                                      if (index !== 0) {
                                        e.target.style.backgroundColor = '#2BB389';
                                      }
                                    }}
                                    onMouseLeave={(e) => {
                                      if (index !== 0) {
                                        e.target.style.backgroundColor = '#33CC99';
                                      }
                                    }}
                                  >
                                    ▲
                                  </button>

                                  <button
                                    type="button"
                                    onClick={() => handleMoveDownWithReset
                                      && handleMoveDownWithReset(index)}
                                    disabled={index === signers.length - 1}
                                    style={{
                                      backgroundColor: index === signers.length - 1 ? '#e9ecef' : '#33CC99',
                                      border: '1px solid #ddd',
                                      borderRadius: '4px',
                                      cursor: index === signers.length - 1 ? 'not-allowed' : 'pointer',
                                      opacity: index === signers.length - 1 ? 0.5 : 1,
                                      padding: '4px 6px',
                                      fontSize: '10px',
                                      color: index === signers.length - 1 ? '#6c757d' : '#ffffff',
                                      lineHeight: '1',
                                      display: 'flex',
                                      alignItems: 'center',
                                      justifyContent: 'center',
                                      width: '22px',
                                      height: '18px',
                                      fontWeight: 'bold',
                                      transition: 'all 0.2s ease',
                                    }}
                                    title="Mover hacia abajo"
                                    onMouseEnter={(e) => {
                                      if (index !== signers.length - 1) {
                                        e.target.style.backgroundColor = '#2BB389';
                                      }
                                    }}
                                    onMouseLeave={(e) => {
                                      if (index !== signers.length - 1) {
                                        e.target.style.backgroundColor = '#33CC99';
                                      }
                                    }}
                                  >
                                    ▼
                                  </button>
                                </div>
                              )}

                              <button
                                type="button"
                                onClick={() => onDelete(signer)}
                                style={{
                                  background: 'none',
                                  border: 'none',
                                  cursor: 'pointer',
                                  fontSize: '14px',
                                  color: '#dc3545',
                                  transition: 'color 0.2s ease',
                                }}
                                title="Eliminar firmante"
                                onMouseEnter={(e) => {
                                  e.target.style.color = '#c33CC99';
                                }}
                                onMouseLeave={(e) => {
                                  e.target.style.color = '#dc3545';
                                }}
                              >
                                Eliminar
                              </button>
                            </div>
                          </div>
                        </div>
                      )}
                    </Draggable>
                  ))}
                  {droppableProvided.placeholder}
                </div>
              )}
            </Droppable>
          </DragDropContext>
        </div>

        {/* Texto informativo actualizado */}
        {signers.length > 1 && (
          <div style={{
            marginTop: '16px',
            padding: '12px 16px',
            backgroundColor: '#f8f9fa',
            borderRadius: '6px',
            border: '1px solid #e9ecef',
            borderLeft: '4px solid #33CC99',
          }}
          >
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '4px',
            }}
            >
              <p style={{
                fontSize: '13px',
                color: '#495057',
                margin: '0',
                fontWeight: '500',
                fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
              }}
              >
                Usa las flechas ▲▼ para reordenar o arrastra desde los puntos ⋮⋮ en la columna&nbsp;
                &quot;Orden&quot;.
              </p>
              {tipoOrdenFirma === 'SECUENCIAL' && (
                <p style={{
                  fontSize: '12px',
                  color: '#666',
                  margin: '0',
                  fontStyle: 'italic',
                  fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                }}
                >
                  💡 En firma secuencial, puedes hacer clic en cualquier rol para editarlo.
                </p>
              )}
              {tipoOrdenFirma === 'PARALELO' && (
                <p style={{
                  fontSize: '12px',
                  color: '#666',
                  margin: '0',
                  fontStyle: 'italic',
                  fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                }}
                >
                  ℹ️ En firma paralelo, todos los firmantes tienen rol &quot;Firmante&quot;
                  <br />
                  (no editable).
                </p>
              )}
            </div>
          </div>
        )}

        {/* Botón de confirmación */}
        {signers.length > 0 && (
          <div style={{
            marginTop: '20px',
            textAlign: 'center',
          }}
          >
            <Button
              type="primary"
              onClick={handleConfirmTable}
              style={{
                minWidth: '200px',
                fontSize: '16px',
                fontWeight: '600',
              }}
            >
              Confirmar tabla de firmantes
            </Button>
          </div>
        )}
      </div>
    </>
  );
}

OrisSignersTable.propTypes = {
  signers: PropTypes.arrayOf(PropTypes.shape({
    numeroDocumento: PropTypes.string.isRequired,
    nombreCompleto: PropTypes.string.isRequired,
    email: PropTypes.string.isRequired,
    rol: PropTypes.string.isRequired,
    esUsuarioSolicitante: PropTypes.bool,
  })).isRequired,
  handleDelete: PropTypes.func.isRequired,
  handleMoveUp: PropTypes.func.isRequired,
  handleMoveDown: PropTypes.func.isRequired,
  handleDragEnd: PropTypes.func.isRequired,
  handleRoleUpdate: PropTypes.func.isRequired,
  tipoOrdenFirma: PropTypes.oneOf(['SECUENCIAL', 'PARALELO']).isRequired,
  isOrisModule: PropTypes.bool,
};

OrisSignersTable.defaultProps = {
  isOrisModule: true,
};

export default OrisSignersTable;

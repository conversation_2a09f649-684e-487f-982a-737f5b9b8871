// Crear src/hooks/useTokenManager.js
import { useEffect, useCallback, useRef } from 'react';
import { useRecoilState } from 'recoil';
import { useSnackbar } from 'notistack';
import { userState } from '@/recoil/atoms';
import {
  isTokenExpired,
  getTokenTimeRemaining,
  TOKEN_CHECK_INTERVAL,
  TOKEN_WARNING_TIME,
  shouldRefreshToken,
  canRefreshToken,
  clearTokenInfo,
} from '@/utils/tokenManager';
import { useTokenRefresh } from '@/hooks/useTokenRefresh';
import { useUserActivity } from '@/hooks/useUserActivity';

export const useTokenManager = () => {
  const [user, setUser] = useRecoilState(userState);
  const { enqueueSnackbar } = useSnackbar();
  const intervalRef = useRef(null);
  const warningShownRef = useRef(false);
  const checkCountRef = useRef(0);
  const refreshAttemptedRef = useRef(false);

  // Hooks para refresh y actividad
  const { executeRefresh, isRefreshing } = useTokenRefresh();
  const { updateActivity, isUserActive } = useUserActivity();

  // Registrar eventos de actividad del usuario
  useEffect(() => {
    const events = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

    const activityHandler = () => {
      updateActivity();
    };

    // Agregar listeners
    events.forEach((event) => {
      document.addEventListener(event, activityHandler, true);
    });

    return () => {
      // Limpiar listeners
      events.forEach((event) => {
        document.removeEventListener(event, activityHandler, true);
      });
    };
  }, [updateActivity]);

  const executeLogout = useCallback(async () => {
    console.log('Ejecutando logout automático por expiración de token...');
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }

    try {
      // 1. Limpiar estado inmediatamente
      setUser({
        email: null,
        expires_in: null,
        idUsuarioC: null,
        idUsuarioA: null,
        access_token: null,
        refresh_token: null,
        enableOris: false,
      });

      // 2. Limpiar almacenamiento local
      clearTokenInfo();
      localStorage.clear();
      sessionStorage.clear();

      // 3. Llamar al endpoint de logout
      const Axios = (await import('axios')).default;
      await Axios.post('/api/logout');

      // 4. Mostrar notificación solo si es logout automático
      enqueueSnackbar('Tu sesión ha expirado', {
        variant: 'warning',
        autoHideDuration: 3000,
      });

      // 5. Redirigir con parámetro para identificar logout automático
      window.location.href = '/login?reason=token_expired';
    } catch (error) {
      console.error('Error durante logout automático:', error);
      // Asegurar limpieza aunque falle
      localStorage.clear();
      sessionStorage.clear();
      window.location.href = '/login?reason=token_expired';
    }
  }, [setUser, enqueueSnackbar]);

  const showTokenWarning = useCallback(() => {
    if (warningShownRef.current) return;

    warningShownRef.current = true;
    const timeRemaining = Math.ceil(getTokenTimeRemaining() / 60000); // En minutos

    enqueueSnackbar(`Tu sesión expirará en ${timeRemaining} minutos`, {
      variant: 'warning',
      autoHideDuration: 8000,
    });
  }, [enqueueSnackbar]);

  const checkTokenExpiration = useCallback(async () => {
    // Si no hay usuario, no hacer nada (evita restaurar sesión después de logout)
    if (!user?.email || !user?.access_token) {
      return;
    }

    const timeRemaining = getTokenTimeRemaining();
    const isExpired = isTokenExpired();
    const shouldRefresh = shouldRefreshToken();
    const canRefresh = canRefreshToken();
    const userIsActive = isUserActive();

    // Si el token ya expiró
    if (isExpired) {
      executeLogout();
      return;
    }

    // Solo refrescar si el usuario está activo Y faltan 5 minutos
    if (shouldRefresh && canRefresh && !isRefreshing && !refreshAttemptedRef.current) {
      if (userIsActive) {
        const refreshSuccess = await executeRefresh(true);
        if (refreshSuccess) {
          warningShownRef.current = false;
          refreshAttemptedRef.current = true;
          // Reset después de 5 minutos para permitir futuros refreshes
          setTimeout(() => {
            refreshAttemptedRef.current = false;
          }, 5 * 60 * 1000);
          return;
        }
      } else {
        showTokenWarning();
        return;
      }
    }

    // Si queda poco tiempo y no se puede refrescar, mostrar advertencia
    if (timeRemaining <= TOKEN_WARNING_TIME && timeRemaining > 0) {
      if (canRefresh && userIsActive && !refreshAttemptedRef.current) {
        const refreshSuccess = await executeRefresh(true);
        if (!refreshSuccess) {
          showTokenWarning();
        }
      } else {
        showTokenWarning();
      }
    }
  }, [user, executeLogout, showTokenWarning, executeRefresh, isRefreshing, isUserActive]);

  useEffect(() => {
    if (!user?.email || !user?.access_token) {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
      warningShownRef.current = false;
      checkCountRef.current = 0;
      refreshAttemptedRef.current = false;
      return;
    }

    // Reset counters
    warningShownRef.current = false;
    checkCountRef.current = 0;
    refreshAttemptedRef.current = false;

    // Chequeo inicial
    setTimeout(() => {
      checkTokenExpiration();
    }, 1000);

    // Configurar intervalo de chequeo
    intervalRef.current = setInterval(() => {
      checkTokenExpiration();
    }, TOKEN_CHECK_INTERVAL);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    };
  }, [user?.email, user?.access_token, checkTokenExpiration]);

  // Reset warning cuando cambia el usuario
  useEffect(() => {
    warningShownRef.current = false;
    refreshAttemptedRef.current = false;
  }, [user?.email]);

  return {
    isTokenExpired: isTokenExpired(),
    timeRemaining: getTokenTimeRemaining(),
    executeLogout,
    isRefreshing,
    userIsActive: isUserActive(),
  };
};

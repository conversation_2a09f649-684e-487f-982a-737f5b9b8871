import React from 'react';
import DocsPendingToSign from '@/components/organisms/DocsPendingToSign';
import DocsToSign from '@/components/organisms/DocsToSign';
import Tabs from '@/components/atoms/Tabs';
import PropTypes from 'prop-types';

function DocsPendingToSignHome({ tab }) {
  return (
    <Tabs
      selected={tab}
      tabs={[
        {
          label: 'Documentos para firmar',
          content: <DocsPendingToSign />,
        },
        // {
        //   label: 'Solicitudes de firma',
        //   content: <DocsToSign />,
        // },
      ]}
    />
  );
}

DocsPendingToSignHome.propTypes = {
  tab: PropTypes.oneOf([0, 1]),
};

DocsPendingToSignHome.defaultProps = {
  tab: 0,
};

export default DocsPendingToSignHome;

import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import catchApiError from '@/utils/catchApiError';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
  },
};

async function subirMultipleB64Route(req, res) {
  try {
    const { user } = req.session;
    if (!user?.access_token) {
      return res.status(401).send();
    }
    const { data } = await fetchApi.post(
      '/firma/manager/subir-multiple/b64',
      req.body,
      {
        headers: {
          Authorization: `Bearer ${user.access_token}`,
          ...getDefaultHeaders(req),
        },
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(subirMultipleB64Route);

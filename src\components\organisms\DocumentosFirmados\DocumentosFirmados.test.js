import React from 'react';
import { render, screen } from '@testing-library/react';
import { RecoilRoot } from 'recoil';
import DocumentosFirmados from './DocumentosFirmados';

jest.mock('@/components/atoms/Tabs', () => ({
  __esModule: true,
  default: ({ tabs }) => tabs[0].content,
}));

jest.mock('@/components/organisms/TablaDocumentosPropietario', () => ({
  __esModule: true,
  default: () => <div>TablaDocumentosPropietario mock</div>,
}));

describe('Tests DocumentosFirmados', () => {
  beforeEach(() => {
    const component = (
      <RecoilRoot>
        <DocumentosFirmados />
      </RecoilRoot>
    );
    render(component);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show SignedDocs', () => {
    expect(screen.getByText(/TablaDocumentosPropietario mock/)).toBeInTheDocument();
  });
});

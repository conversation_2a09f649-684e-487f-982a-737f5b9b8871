{"env": {"browser": true, "es2021": true, "node": true, "jest": true}, "extends": ["plugin:react/recommended", "airbnb", "next"], "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 12, "sourceType": "module"}, "plugins": ["react"], "rules": {"react/jsx-props-no-spreading": "off", "no-restricted-exports": "off", "react/jsx-filename-extension": "off", "jsx-a11y/anchor-is-valid": "off", "jsx-a11y/label-has-associated-control": "off", "jsx-a11y/click-events-have-key-events": "off", "jsx-a11y/no-static-element-interactions": "off", "react/no-array-index-key": "off", "jsx-a11y/no-autofocus": "off", "no-new": "off", "consistent-return": "off", "react/jsx-no-bind": "off", "react/require-default-props": "off", "react/no-unused-prop-types": "off", "import/no-named-as-default": "off", "react/default-props-match-prop-types": "off", "import/prefer-default-export": "off", "react/display-name": "off", "linebreak-style": "off"}, "settings": {"import/resolver": {"alias": {"map": [["@/assets", "./src/assets"], ["@/components", "./src/components"], ["@/config", "./src/config"], ["@/helpers", "./src/helpers"], ["@/hocs", "./src/hocs"], ["@/hooks", "./src/hooks"], ["@/lib", "./src/lib"], ["@/pages", "./src/pages"], ["@/recoil", "./src/recoil"], ["@/services", "./src/services"], ["@/styles", "./src/styles"], ["@/tokens", "./src/tokens"], ["@/utils", "./src/utils"]], "extensions": [".js", ".jsx"]}}}}
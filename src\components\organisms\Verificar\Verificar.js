import React, { useMemo, useRef, useState } from 'react';
import Grid from '@mui/material/Grid';
import { fileToBase64String } from '@/utils/fileAdmin';
import { verifyDocument } from '@/services/user';
import icon from '@/assets/images/Logo3.png';
import { useSnackbar } from 'notistack';
import StatesHandler from '@/components/organisms/StateHandler';
import DocumentInfo from '@/components/organisms/DocumentInfo';
import Button from '@/components/atoms/Button';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import Card from '@/components/atoms/Card';
import Icon from '@/components/atoms/Icon';
import CenteredContent from '@/components/layout/CenteredContent';
import Spacer from '@/components/layout/Spacer';

function Verificar() {
  const [state, setState] = useState({
    haveError: false,
    isLoading: false,
    haveResponse: false,
    loadMessage: 'Verificando documento ...',
    code: '',
    errorMessage: {
      header: '',
      content: 'este mensaje es automático',
      icon,
    },
  });

  const [signedDoc, setSignedDoc] = useState({
    nombreArchivo: '',
    hashArchivo: '',
    cantidadFirmas: 0,
    cantidadFirmado: 0,
    fechaRegistro: '',
    cantidadConsultas: 0,
  });

  const [doc, setDoc] = useState(null);

  const { enqueueSnackbar } = useSnackbar();

  const selectDoc = async (file) => {
    const base64 = await fileToBase64String(file).then((b64) => b64.substring(b64.indexOf(',') + 1));

    setDoc({
      nombreArchivo: file.name,
      cantidadFirmas: 1,
      idUsuario: 0,
      archivo64: base64,
    });
  };

  const parseFile = (files) => {
    if (!files.length) {
      return false;
    }

    if (files.length > 1) {
      enqueueSnackbar('Solo se permite un documento a la vez', { variant: 'error' });
      return false;
    }

    const file = files[0];
    if (file.type !== 'application/pdf') {
      enqueueSnackbar('Solo se permite archivos PDF', { variant: 'error' });
      return false;
    }

    return file;
  };

  const handleFileSelect = async (event) => {
    const file = parseFile(event.target.files);
    if (file) {
      await selectDoc(file);
    }
  };

  const handleVerificarClick = async () => {
    if (doc === null || doc.nombreArchivo === '') {
      enqueueSnackbar('seleccione un documento', { variant: 'error' });
    } else {
      setState({
        ...state, isLoading: true, haveError: false,
      });
      verifyDocument(doc).then((response) => {
        if (response.status === 200) {
          setSignedDoc({
            ...signedDoc,
            showInfo: true,
            nombreArchivo: response.data.data.nombreArchivo,
            fechaRegistro: response.data.data.fechaRegistroStr,
            cantidadConsultas: response.data.data.cantidadConsultas,
            firmas: response.data.data.firmas,
            ip: response.data.data.ip,
            hashArchivo: response.data.data.hashArchivo,
            propietario: response.data.data.propietario,
            idArchivo: response.data.data.idArchivo,
          });
          setState({
            ...state,
            isVerified: true,
            isLoading: false,
            haveResponse: true,
          });
        } else if (response.status !== 200) {
          console.log(response.data.data);
          setState({
            ...state,
            isLoading: false,
            haveError: true,
            errorMessage: {
              ...state.errorMessage,
              content: '',
              header: 'Verificación de documento',
              title: response.data.mensaje,
            },
          });
        }
      }).catch((err) => console.log(err));
    }
  };

  const handleBack = () => {
    setSignedDoc({
      ...signedDoc,
      showInfo: false,
    });
    setState({
      ...state,

      haveResponse: false,
    });
    console.log('state.haveResponse ', state.haveResponse);
  };

  const handleError = () => {
    setState({
      ...state,
      isLoading: false,
      haveError: false,
    });
  };

  const preventDefault = (event) => {
    event.preventDefault();
    event.stopPropagation();
  };

  const fileInputRef = useRef();

  const fileDrop = async (e) => {
    preventDefault(e);
    const { files } = e.dataTransfer;
    const file = parseFile(files);
    if (file) {
      await selectDoc(file);
    }
  };

  const helperText = useMemo(
    () => (doc ? `Documento seleccionado: ${doc.nombreArchivo}` : 'Arrastre o adjunte el documento que quiere verificar'),
    [doc],
  );

  return (
    <div>
      <StatesHandler handleErrorButton={handleError} state={state}>
        {state.haveResponse ? <DocumentInfo handleClick={handleBack} documentInfo={signedDoc} /> : (
          <div>
            <Grid container>
              <Grid item xs={12}>
                <Heading size="sm">Verificar documento</Heading>
              </Grid>
            </Grid>
            <Spacer size="md" />
            <div
              style={{ width: '100%', cursor: 'pointer' }}
              onDragOver={preventDefault}
              onDragEnter={preventDefault}
              onDragLeave={preventDefault}
              onDrop={fileDrop}
              onClick={() => fileInputRef.current.click()}
            >
              <Card
                color="secondary"
              >
                <CenteredContent>
                  <Spacer.Vertical size="lg" />
                  <Icon name="verifyDocument" size="2xl" color="inverted" />
                  <Spacer.Vertical size="sm" />
                  <Paragraph size="sm" color="inverted" isCentered>
                    {helperText}
                  </Paragraph>
                  <Spacer.Vertical size="lg" />
                </CenteredContent>
                <input
                  ref={fileInputRef}
                  style={{ display: 'none' }}
                  type="file"
                  onChange={handleFileSelect}
                  data-testid="file-input"
                />
              </Card>
            </div>
            <Spacer.Vertical size="sm" />
            <Button
              isInline
              onClick={handleVerificarClick}
            >
              Verificar
            </Button>
          </div>
        )}

      </StatesHandler>
    </div>

  );
}

export default Verificar;

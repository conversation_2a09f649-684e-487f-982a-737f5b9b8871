import React, { useState, useEffect } from 'react';
import filterIp from '@/utils/filterIp';
import {
  seeSigners, reenviarSolicitudFirma, signDocuments, validarOrdenFirma, consultarEstadoSolicitud,
} from '@/services/user';
import { useSnackbar } from 'notistack';
import { useRouter } from 'next/router';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';
import Card from '@/components/atoms/Card';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import Icon from '@/components/atoms/Icon';
import dayjs from 'dayjs';
import toCapitalize from '@/utils/toCapitalize';
import ButtonIcon from '@/components/molecules/ButtonIcon';
import Container from '@/components/layout/Container';
import CenteredContent from '@/components/layout/CenteredContent';
import StateHandler from '@/components/organisms/StateHandler';
import withAuthentication from '@/lib/withAuthentication';
import icon from '@/assets/images/document-error-flat.png';
import Table from '@mui/material/Table';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import TableBody from '@mui/material/TableBody';
import Paper from '@mui/material/Paper';
import { saveStorage, loadItemStorage } from '@/utils/utils';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import { TableHead } from '@mui/material';
import styles from './InformationDocToSign.module.css';

import 'dayjs/locale/es';
import DocPreview from '@/components/organisms/DocPreview';

dayjs.locale('es');

export const getServerSideProps = withAuthentication;

// Si existe una fecha de firma, significa que el firmante ya firmó el documento
// Si no existe fecha de firma, el firmante aún no ha firmado
export const statusRequest = (fechaFirma) => {
  return fechaFirma ? 'Firma realizada' : 'Pendiente';
};

function InformationDocToSign() {
  const { enqueueSnackbar } = useSnackbar();
  const [user] = useRecoilState(userState);
  const router = useRouter();

  const [state, setState] = useState({
    file: '',
    response: '',
    isLoading: true,
  });

  const handleErrorButton = async () => {
    saveStorage('docs', '');
    saveStorage('idDoc', '');
    setState({
      ...state,
      isLoading: false,
    });
    await router.replace('/');
  };

  useEffect(() => {
    if (!user?.access_token) {
      return;
    }
    const cod = loadItemStorage('idDoc');
    const loadedState = loadItemStorage('docs');

    console.log('=== DEBUGGING INFORMACIÓN DEL DOCUMENTO ===');
    console.log('ID del documento:', cod);
    console.log('Estado cargado:', loadedState);

    // Usar el nuevo endpoint para obtener información más detallada
    consultarEstadoSolicitud(cod, user?.access_token)
      .then(async (response) => {
        console.log('=== RESPUESTA DEL ENDPOINT ===');
        console.log('Status:', response.status);
        console.log('Data completa:', response.data);
        if (response.status === 200) {
          // Adaptar los datos del nuevo formato al formato esperado por el componente
          const solicitudes = response.data.solicitudes || [];
          console.log('Solicitudes encontradas:', solicitudes);

          const datosAdaptados = {
            data: {
              firmantes: solicitudes.map((solicitud, index) => {
                console.log(`Procesando solicitud ${index}:`, solicitud);
                return {
                  idDocument: `${response.data.idArchivoFirma}-${solicitud.emailFirmante}-${index}`,
                  nombreFirmante: solicitud.emailFirmante.split('@')[0], // Temporal hasta obtener nombre real
                  correoFirmante: solicitud.emailFirmante,
                  rol: solicitud.rolFirmante,
                  fechaSolicitud: solicitud.fechaRegistro,
                  detalleSolicitud: solicitud.firmado ? 'Firmado por interviniente' : 'Pendiente de firma',
                  fechaFirma: solicitud.firmado ? solicitud.fechaRegistro : null,
                  ordenFirma: solicitud.ordenFirma,
                  tipoOrdenFirma: solicitud.tipoOrdenFirma,
                  fechaVencimiento: solicitud.fechaVencimiento,
                };
              }),
            },
          };

          console.log('Datos adaptados:', datosAdaptados);

          return setState({
            ...state,
            response: datosAdaptados,
            file: {
              ...loadedState,
              descripcion: response.data.descripcion,
              nombreArchivo: response.data.nombreArchivo,
              estado: response.data.estado === 1 ? 'Activo' : 'Inactivo',
              hashArchivo: response.data.hashArchivo,
              progreso: response.data.progreso,
              totalSolicitudes: response.data.totalSolicitudes,
              solicitudesPendientes: response.data.solicitudesPendientes,
              solicitudesFirmadas: response.data.solicitudesFirmadas,
              fechaRegistro: response.data.fechaRegistro || loadedState.fechaRegistro,
              idArchivo: response.data.idArchivoFirma || loadedState.idArchivo,
            },
            isLoading: false,
          });
        }
        
        console.log('Error en respuesta, usando fallback');
        setState({
          ...state,
          haveError: true,
          errorMessage: {
            ...state.errorMessage,
            header: 'Error al traer información',
            content:
              'Lo sentimos ocurrió un error al traer la información del documento, por favor, intenta más tarde.',
            icon,
          },
        });
      })
      .catch((err) => {
        console.error('Error al consultar estado:', err);
        // Fallback al endpoint anterior si falla
        console.log('Usando endpoint fallback...');
        seeSigners(cod, user?.access_token)
          .then(async (fallbackResponse) => {
            console.log('Respuesta fallback:', fallbackResponse);
            if (fallbackResponse.status === 200) {
              return setState({
                ...state,
                response: fallbackResponse.data,
                file: loadedState,
                isLoading: false,
              });
            }
            setState({
              ...state,
              haveError: true,
              errorMessage: {
                ...state.errorMessage,
                header: 'Error al traer información',
                content:
                  'Lo sentimos ocurrió un error al traer la información del documento, por favor, intenta más tarde.',
                icon,
              },
            });
          })
          .catch((fallbackErr) => console.error('Error en fallback:', fallbackErr));
      });
  }, [user]);

  const handleBack = async () => {
    saveStorage('docs', '');
    saveStorage('idDoc', '');
    saveStorage('docsToSign', '');
    await router.push('/');
  };

  const sendRequestSignature = async (email, type) => {
    const idDocument = loadItemStorage('idDoc');
    const array = {
      signatory: email,
      code: idDocument,
      mode: type,
    };

    reenviarSolicitudFirma(array, user?.access_token)
      .then((response) => {
        if (response.status === 200) {
          setState({
            ...state,
          });
          if (type === 'eml') {
            enqueueSnackbar('Se ha enviado solicitud de firma electrónica a la cuenta de correo.', { variant: 'success' });
          } else {
            enqueueSnackbar('Se ha enviado solicitud de firma electrónica por medio de Whatsapp.', { variant: 'success' });
          }
        } else {
          setState({
            ...state,
          });
          enqueueSnackbar('Lo sentimos, ocurrió un error al enviar el correo.', { variant: 'error' });
        }
      })
      .catch((error) => console.log(error));
  };

  // NUEVA FUNCIÓN: Validar orden antes de firmar
  const validarOrdenAntesDeFirmar = async () => {
    const document = loadItemStorage('docs');
    if (!document?.idArchivo || !user?.email || !user?.access_token) {
      return true; // Si faltan datos, permitir continuar
    }

    try {
      console.log('Validando orden antes de firmar documento:', document.idArchivo);
      
      const response = await validarOrdenFirma(
        document.idArchivo,
        user.email,
        user.access_token,
      );

      console.log('Resultado validación orden:', response);

      if (response.status !== 200 || !response.data?.data?.puedeFirmar) {
        console.log('No puede firmar - redirigiendo a no-es-tu-turno');
        await router.push('/no-es-tu-turno');
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error al validar orden antes de firmar:', error);
      return true; // En caso de error, permitir continuar
    }
  };

  // la función signFile para incluir validación
  const signFile = async () => {
    // Verificar orden antes de proceder
    const puedeFiremar = await validarOrdenAntesDeFirmar();
    if (!puedeFiremar) {
      return;
    }

    setState({
      ...state,
      isLoading: true,
    });

    const document = loadItemStorage('docs');
    const array = [
      {
        idArchivo: document.idArchivo,
        idUsuario: user.idUsuarioC,
      },
    ];

    signDocuments(array, user?.access_token)
      .then(async (response) => {
        if (response.status === 200) {
          saveStorage('docsToSign', array);
          return router.push('/multiple-firma');
        }
        return setState({
          ...state,
          haveError: true,
          errorMessage: {
            ...state.errorMessage,
            header: 'Error en firma de documentos',
            content: response.data.mensaje,
            icon,
          },
        });
      })
      .catch((error) => console.log(error));
  };

  // función de formateo al inicio del componente
  const formatPercentage = (value) => {
    if (value == null || value === undefined) return '0,00';

    // Si viene como decimal (0.3333), multiplicar por 100
    const percentage = value > 1 ? value : value * 100;

    return percentage.toLocaleString('es-CO', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  return (
    <Container>
      <StateHandler handleErrorButton={handleErrorButton} state={state}>
        <div>
          <div style={{ textAlign: 'right' }}>
            <Button
              type="tertiary"
              isInline
              onClick={handleBack}
            >
              Volver al inicio
            </Button>
          </div>
          <Spacer.Vertical size="sm" />
          <Heading>
            Información detallada del archivo a firmar
          </Heading>
          <Spacer.Vertical size="lg" />
          <div className={styles.grid}>
            <div>
              <div className={styles['preview-info-container']}>
                <Paper className={styles.containerTable}>
                  <Table size="small" aria-label="customized table">
                    <TableBody>
                      <TableRow key={state.file?.idArchivo}>
                        <TableCell>
                          <Paragraph size="xs" weight="semibold">
                            Nombre documento:
                          </Paragraph>
                        </TableCell>
                        <TableCell>
                          <Paragraph size="xs">
                            {toCapitalize(state.file?.nombreArchivo || '')}
                          </Paragraph>
                        </TableCell>
                      </TableRow>
                      <TableRow key={state.file?.idArchivo}>
                        <TableCell>
                          <Paragraph size="xs" weight="semibold">
                            Descripción:
                          </Paragraph>
                        </TableCell>
                        <TableCell>
                          <Paragraph size="xs">
                            {state.file?.descripcion || 'Sin descripción'}
                          </Paragraph>
                        </TableCell>
                      </TableRow>
                      <TableRow key={state.file?.idArchivo}>
                        <TableCell>
                          <Paragraph size="xs" weight="semibold">
                            Progreso:
                          </Paragraph>
                        </TableCell>
                        <TableCell>
                          <Paragraph size="xs">
                            {state.file?.solicitudesFirmadas || 0} de {state.file?.totalSolicitudes || 0} firmantes ({formatPercentage(state.file?.progreso)}%)
                          </Paragraph>
                        </TableCell>
                      </TableRow>
                      <TableRow key={state.file?.idArchivo}>
                        <TableCell>
                          <Paragraph size="xs" weight="semibold">
                            Fecha:
                          </Paragraph>
                        </TableCell>
                        <TableCell>
                          <Paragraph size="xs">
                            {toCapitalize(dayjs(state.file.fechaRegistro).format('dddd'))}
                            {', '}
                            {' '}
                            {' '}
                            {dayjs(state.file.fechaRegistro).format('DD MMMM YYYY')}
                            {' '}
                            {' '}
                            {dayjs(state.file.fechaRegistro).format('hh:mm a')}
                          </Paragraph>
                        </TableCell>
                      </TableRow>
                      <TableRow key={state.file?.idArchivo}>
                        <TableCell>
                          <Paragraph size="xs" weight="semibold">
                            Tipo de firma:
                          </Paragraph>
                        </TableCell>
                        <TableCell>
                          <Paragraph size="xs">
                            {toCapitalize(state.file?.tipoFirma || '')}
                          </Paragraph>
                        </TableCell>
                      </TableRow>
                      <TableRow key={state.file?.idArchivo}>
                        <TableCell>
                          <Paragraph size="xs" weight="semibold">
                            Estado:
                          </Paragraph>
                        </TableCell>
                        <TableCell>
                          <Paragraph size="xs">
                            {toCapitalize(state.file?.estado || '')}
                          </Paragraph>
                        </TableCell>
                      </TableRow>
                      <TableRow key={state.file?.idArchivo}>
                        <TableCell>
                          <Paragraph size="xs" weight="semibold">
                            Ip:
                          </Paragraph>
                        </TableCell>
                        <TableCell>
                          <Paragraph size="xs">
                            {filterIp(state.file?.ip || '')}
                          </Paragraph>
                        </TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </Paper>
              </div>
              <Spacer.Vertical size="lg" />
              {state.response.data?.firmantes.length === 0
                ? (
                  <div>
                    <h2 className={styles.title}>Este documento no tiene un proceso de firma.</h2>
                  </div>
                )
                : (
                  <div>
                    <h2 className={styles.title}>Avance de firma de los firmantes:</h2>
                    <Spacer.Vertical size="md" />
                    <div>
                      <Paper className={styles.containerTable}>
                        <Table size="small" aria-label="customized table" className={styles.table}>
                          <TableHead>
                            <TableRow>
                              <TableCell>
                                <strong className={styles.tableHead}>Nombre</strong>
                              </TableCell>
                              <TableCell>
                                <strong className={styles.tableHead}>Correo electrónico</strong>
                              </TableCell>
                              <TableCell>
                                <strong className={styles.tableHead}>Rol</strong>
                              </TableCell>
                              <TableCell>
                                <strong className={styles.tableHead}>Fecha de solicitud</strong>
                              </TableCell>
                              <TableCell>
                                <strong className={styles.tableHead}>Detalle de solicitud</strong>
                              </TableCell>
                              <TableCell>
                                <strong className={styles.tableHead}>Estado de solicitud</strong>
                              </TableCell>
                              <TableCell>
                                <strong className={styles.tableHead}>Reenviar solicitud</strong>
                              </TableCell>
                            </TableRow>
                          </TableHead>
                          <TableBody>
                            {state.response.data?.firmantes.map((element) => (
                              <TableRow key={element.idDocument}>
                                <TableCell>
                                  {element.nombreFirmante.length > 25 ? `${element.nombreFirmante.substring(0, 25)}...` : element.nombreFirmante}
                                </TableCell>
                                <TableCell>
                                  {element.correoFirmante.length > 25 ? `${element.correoFirmante.substring(0, 25)}...` : element.correoFirmante}
                                </TableCell>
                                <TableCell>
                                  {element.rol || 'Firmante'}
                                </TableCell>
                                <TableCell>
                                  <div>
                                    <Paragraph size="xs">
                                      {dayjs(element.fechaSolicitud).format('DD/MM/YYYY')}
                                    </Paragraph>
                                    {element.tipoOrdenFirma === 'SECUENCIAL' && (
                                      <Paragraph size="xs" color="muted">
                                        Orden: {element.ordenFirma}
                                      </Paragraph>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  {element.detalleSolicitud.length > 25 ? `${element.detalleSolicitud.substring(0, 25)}...` : element.detalleSolicitud}
                                </TableCell>
                                <TableCell>
                                  <div>
                                    {statusRequest(element.fechaFirma || null)}
                                    {element.fechaVencimiento && (
                                      <Paragraph size="xs" color="muted">
                                        Vence: {dayjs(element.fechaVencimiento).format('DD/MM/YYYY')}
                                      </Paragraph>
                                    )}
                                  </div>
                                </TableCell>
                                {element.detalleSolicitud !== 'Firmado por interviniente'
                                  ? (
                                    <TableCell>
                                      <ButtonIcon icon="envelope" type="primary" onClick={() => sendRequestSignature(element.correoFirmante, 'eml')} size="xxs" />
                                      <Spacer.Vertical size="sm" />
                                      {element.nombreFirmante !== 'No registrado'
                                        ? <ButtonIcon color="secondary" icon="whatsapp" onClick={() => sendRequestSignature(element.correoFirmante, 'wta')} size="xxs" />
                                        : ''}
                                    </TableCell>
                                  )
                                  : <TableCell />}
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </Paper>
                    </div>
                    <Spacer.Vertical size="md" />
                    {toCapitalize(state.file?.tipoFirma || '') === 'Otros y yo'
                      ? <div className={styles.divBtn}><ButtonIcon icon="pen" type="primary" onClick={() => signFile()} size="xxs">Firmar</ButtonIcon></div>
                      : <div> </div>}
                  </div>
                )}

            </div>

            <div className={styles['preview-container']}>
              <DocPreview docId={state.file?.idArchivo} />
            </div>
          </div>
        </div>
      </StateHandler>
    </Container>
  );
}

export default InformationDocToSign;

import { useCallback, useRef } from 'react';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import { refreshToken } from '@/services/user';
import { setTokenInfo } from '@/utils/tokenManager';

export const useTokenRefresh = () => {
  const [user, setUser] = useRecoilState(userState);
  const refreshingRef = useRef(false);
  const lastRefreshAttemptRef = useRef(0);

  const executeRefresh = useCallback(async () => {
    // Evitar múltiples refreshes simultáneos
    if (refreshingRef.current) {
      return false;
    }

    // Evitar refresh muy frecuentes (mínimo 30 segundos entre intentos)
    const timeSinceLastAttempt = Date.now() - lastRefreshAttemptRef.current;
    if (timeSinceLastAttempt < 30000) {
      return false;
    }

    if (!user?.refresh_token) {
      return false;
    }

    refreshingRef.current = true;
    lastRefreshAttemptRef.current = Date.now();

    try {
      const response = await refreshToken();

      if (response.status === 200) {
        const newUserData = response.data;

        // Actualizar estado de usuario con nueva información
        setUser((prevUser) => ({
          ...prevUser,
          ...newUserData,
        }));

        // Actualizar información de tiempo del token
        if (newUserData.expires_in) {
          setTokenInfo(newUserData.expires_in);
        }

        refreshingRef.current = false;
        return true;
      }
      if (response.status === 401) {
        refreshingRef.current = false;
        return false;
      }
      refreshingRef.current = false;
      return false;
    } catch (error) {
      refreshingRef.current = false;
      return false;
    }
  }, [user, setUser]);

  return {
    executeRefresh,
    isRefreshing: refreshingRef.current,
  };
};

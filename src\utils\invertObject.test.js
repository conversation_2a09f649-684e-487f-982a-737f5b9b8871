import invertObject from '@/utils/invertObject';

describe('[ utils / invertObject ]', () => {
  describe('when the param is a empty object', () => {
    it('should return a empty object', () => {
      const result = invertObject({});
      const expected = {};

      expect(result).toEqual(expected);
    });
  });

  describe('when the param is not a empty object', () => {
    describe('when the param is a simple object', () => {
      it('should return the object inverted', () => {
        const result = invertObject({ key1: 'val1', key2: 'val2' });
        const expected = { val1: 'key1', val2: 'key2' };

        expect(result).toEqual(expected);
      });
    });

    describe('when some value is an array', () => {
      it('should return an object without these value', () => {
        const result = invertObject({ key1: 'val1', key2: 'val2', key3: ['arrayVal1', 'arrayVal2'] });
        const expected = { val1: 'key1', val2: 'key2' };

        expect(result).toEqual(expected);
      });
    });

    describe('when some value is an object', () => {
      it('should return an object without these value', () => {
        const result = invertObject({ key1: 'val1', key2: 'val2', key3: { innerKey1: 'innerValue1', innerKey2: 'innerValue2' } });
        const expected = { val1: 'key1', val2: 'key2' };

        expect(result).toEqual(expected);
      });
    });
  });

  describe('when the param is not an object', () => {
    it('should return the same param', () => {
      const param = 'asasas';
      const result = invertObject(param);

      expect(result).toBe(param);
    });
  });
});

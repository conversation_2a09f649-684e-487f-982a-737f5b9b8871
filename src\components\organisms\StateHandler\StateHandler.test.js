import React from 'react';
import { render } from '@testing-library/react';
import StatesHandler from './StateHandler';

jest.mock('@/components/organisms/Loader', () => ({
  __esModule: true,
  default: jest.fn(() => 'Loader mock'),
}));

jest.mock('@/components/organisms/Message', () => ({
  __esModule: true,
  default: jest.fn(() => 'Message mock'),
}));

describe('Tests StatesHandler', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call Message if have error', () => {
    const state = {
      haveError: true,
    };
    const { getByText } = render(<StatesHandler state={state} />);
    expect(getByText(/Message mock/i)).toBeInTheDocument();
  });

  it('should call Loader if not have error and isLoading is true', () => {
    const state = {
      haveError: false,
      isLoading: true,
    };
    const { getByText } = render(<StatesHandler state={state} />);
    expect(getByText(/Loader mock/i)).toBeInTheDocument();
  });

  it('should render children if not have error and isLoading is false', () => {
    const state = {
      haveError: false,
      isLoading: false,
    };
    const { getByText } = render(<StatesHandler state={state}>Children</StatesHandler>);
    expect(getByText(/Children/i)).toBeInTheDocument();
  });
});

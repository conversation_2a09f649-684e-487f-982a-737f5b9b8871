import React from 'react';
import TablaDocumentosPropietario from '@/components/organisms/TablaDocumentosPropietario';
import Tabs from '@/components/atoms/Tabs';
import SignedDocs from '@/components/organisms/SignedDocs';
import PropTypes from 'prop-types';

function DocumentosFirmados({ tab }) {
  return (
    <Tabs
      selected={tab}
      tabs={[
        {
          label: 'Documentos Firmados - Propietario',
          content: <TablaDocumentosPropietario />,
        },
        {
          label: 'Documentos Firmados',
          content: <SignedDocs />,
        },
      ]}
    />
  );
}

DocumentosFirmados.propTypes = {
  tab: PropTypes.oneOf([0, 1]),
};

DocumentosFirmados.defaultProps = {
  tab: 0,
};

export default DocumentosFirmados;

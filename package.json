{"name": "firmese", "version": "0.1.22", "private": true, "dependencies": {"@emotion/cache": "^11.10.5", "@emotion/react": "^11.10.5", "@emotion/server": "^11.10.0", "@emotion/styled": "^11.10.5", "@fontsource/roboto": "^4.5.8", "@mui/icons-material": "^5.11.0", "@mui/material": "^5.11.3", "@types/react-beautiful-dnd": "^13.1.8", "axios": "^1.6.8", "bootstrap": "^5.3.3", "bowser": "^2.11.0", "clsx": "^1.1.1", "cross-env": "^7.0.3", "dayjs": "^1.11.7", "express": "^4.18.2", "flexboxgrid": "^6.3.1", "formik": "^2.1.5", "iron-session": "^6.3.1", "next": "^14.2.10", "notistack": "^2.0.8", "pdf-image": "github:jaramirez1052/node-pdf-image#master", "pdf-page-counter": "^1.0.2", "pdfjs-dist": "^4.6.82", "prop-types": "^15.8.1", "query-string": "^6.13.1", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-bootstrap": "^2.7.0", "react-dom": "^18.3.1", "react-ga": "^3.1.2", "react-hook-form": "^7.41.3", "react-query": "^3.38.0", "recoil": "^0.7.6", "sharp": "^0.33.5", "styled-components": "^5.3.3", "yup": "^0.32.11"}, "scripts": {"start:watch": "cp .env.development .env.local && next dev", "start:watch-dev": "next dev", "build": "next build", "build:prod": "cp .env.production .env.local && next build", "build:test": "cp .env.test .env.local && next build", "build:dev": "cp .env.development .env.local && next build", "start": "cross-env PORT=3191 NODE_ENV=production next start", "lint": "next lint", "test": "jest --silent", "export": "npm run build:prod && next export", "build:tokens": "node ./src/scripts/build-tokens.js"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "eslint": "^8.31.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "^13.1.1", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-react": "^7.29.3", "eslint-plugin-react-hooks": "^4.3.0", "jest": "^29.3.1", "jest-environment-jsdom": "^29.3.1"}}
import React, { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import withAuthentication from '@/lib/withAuthentication';
import OrisModule from '@/components/organisms/OrisModule';

export const getServerSideProps = withAuthentication;
export default function Oris({ user }) {
  const [, setUser] = useRecoilState(userState);

  useEffect(() => {
    setUser(user);
  }, [user]);

  return <OrisModule />;
}

import React, { useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import withAuthentication from '@/lib/withAuthentication';
import { getServicioUsuario, saveProfileSettings } from '@/services/user';
import { useQuery } from 'react-query';
import Card from '@/components/atoms/Card';
import { useForm } from 'react-hook-form';
import { useSnackbar } from 'notistack';
import Input from '@/components/atoms/Input';
import Spacer from '@/components/layout/Spacer';
import Button from '@/components/atoms/Button';
import CenteredContent from '@/components/layout/CenteredContent';
import Loading from '@/components/atoms/Loading';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import Check from '@/components/atoms/Check';
import Divider from '@/components/atoms/Divider';

export const getServerSideProps = withAuthentication;

const initialState = {
  idServicio: '',
  cantidadFirmas: '',
  fechaVencimiento: '',
  fechaActualizacion: '',
  tipoServicio: '',
  endpointCBack: '',
  notificarFirma: false,
  showData: false,

};
export default function Firmados({ user }) {
  const [, setUser] = useRecoilState(userState);
  const { enqueueSnackbar } = useSnackbar();

  const {
    register, handleSubmit, formState: { errors }, setValue, getValues, watch,
  } = useForm({
    defaultValues: {
      callback: '',
      notifySignature: false,
      endpointCBackHabilitado: false,
    },
  });

  useEffect(() => {
    setUser(user);
  }, [user]);

  const [state, setState] = useState(initialState);
  const {
    data: response, isLoading,
  } = useQuery(
    ['getUser', user.idUsuarioC],
    () => getServicioUsuario(user.access_token),
    {
      enabled: !!user?.idUsuarioC,
    },
  );

  const handleService = () => {
    if (!user?.email || user.idUsuarioC === null) {
      setState({
        ...state,
        cantidadFirmas: '0',
        idServicio: '',
        fechaActualizacion: '',
        fechaVencimiento: '',
        tipoServicio: 'ND',
        endpointCBack: '',
        endpointCBackHabilitado: false,
        notificarFirma: false,
        showData: false,
      });
    } else if (response.status === 200) {
      const {
        data: {
          data: {
            cantidadFirmas,
            idServicio,
            fechaActualizacion,
            fechaVencimiento,
            tipoServicio,
            endpointCBack,
            cantOtros,
            detalleTipoValidacion,
            notificarFirma,
            endpointCBackHabilitado,
          },
        },
      } = response;
      setState({
        ...state,
        cantidadFirmas,
        idServicio,
        fechaActualizacion,
        fechaVencimiento,
        tipoServicio,
        endpointCBack,
        endpointCBackHabilitado,
        cantOtros,
        detalleTipoValidacion,
        showData: true,
        notificarFirma,
      });
      setValue('callback', endpointCBack);
      setValue('notifySignature', notificarFirma);
      setValue('endpointCBackHabilitado', endpointCBackHabilitado);
    }
  };

  const sendProfileSettings = async (idServicio, settings) => {
    const { status } = await saveProfileSettings(idServicio, settings, user?.access_token);

    if (status !== 200) {
      return enqueueSnackbar('Oops, Ha ocurrido un error', {
        variant: 'error',
      });
    }

    return enqueueSnackbar('Guardado correctamente', {
      variant: 'success',
    });
  };

  const onSubmit = async (values) => {
    const { callback, notifySignature, endpointCBackHabilitado } = values;

    if (endpointCBackHabilitado && !callback) {
      return enqueueSnackbar('Debe ingresar un callback', {
        variant: 'error',
      });
    }

    const b64 = callback ? Buffer.from(callback, 'utf8').toString('base64') : '';

    await sendProfileSettings(state.idServicio, {
      endpointCBack: b64,
      notificarFirma: notifySignature,
      endpointCBackHabilitado,
    });
  };

  useEffect(() => {
    if (!isLoading) {
      handleService();
    }
  }, [isLoading]);

  return (
    <Loading isShown={isLoading}>
      {state.showData ? (
        <div>
          <Card>
            <Heading size="sm">Información</Heading>
            <Spacer.Vertical size="sm" />
            <div>
              <Paragraph size="xs" weight="semibold">
                Fecha Inicio:
              </Paragraph>
              <Paragraph size="xs">
                {state.fechaActualizacion}
              </Paragraph>
            </div>
            <Spacer.Vertical size="xs" />
            <div>
              <Paragraph size="xs" weight="semibold">
                Fecha Vencimiento:
              </Paragraph>
              <Paragraph size="xs">
                {state.fechaVencimiento}
              </Paragraph>
            </div>
            <Spacer.Vertical size="xs" />
            <div>
              <Paragraph size="xs" weight="semibold">
                Tipo de Servicio:
              </Paragraph>
              <Paragraph size="xs">
                {state.tipoServicio}
              </Paragraph>
            </div>
            <Spacer.Vertical size="xs" />
            <div>
              <Paragraph size="xs" weight="semibold">
                Firmas disponibles:
              </Paragraph>
              <Paragraph size="xs">
                {state.cantidadFirmas}
              </Paragraph>
            </div>
            <Spacer.Vertical size="xs" />
            <div>
              <Paragraph size="xs" weight="semibold">
                Limite de firma para otros firmantes:
              </Paragraph>
              <Paragraph size="xs">
                {state.cantOtros}
              </Paragraph>
            </div>
            <Spacer.Vertical size="xs" />
            <div>
              <Paragraph size="xs" weight="semibold">
                Tipo de validación:
              </Paragraph>
              <Paragraph size="xs">
                {state.detalleTipoValidacion}
              </Paragraph>
            </div>
          </Card>
          <Spacer.Horizontal size="xs" />
          <Card>
            <Heading size="sm">Configuración</Heading>
            <Spacer.Vertical size="sm" />
            <div>
              <form onSubmit={handleSubmit(onSubmit)}>
                <Paragraph size="sm" weight="semibold">
                  Callback
                </Paragraph>
                <Spacer.Vertical size="xs" />
                <Paragraph size="xs" weight="semibold">
                  Url callback:
                </Paragraph>
                <Input
                  {
                      ...register('callback', {
                        pattern: {
                          value: /https?:\/\/(www\.)?[-a-zA-Z\d@:%._+~#=]{1,256}\.[a-zA-Z\d()]{1,6}\b([-a-zA-Z\d()!@:%_+.~#?&/=]*)/,
                          message: 'La URL no es válida',
                        },
                        validate: (value) => ((!value && watch('endpointCBackHabilitado')) ? 'Debe ingresar un callback si está habilitado' : true),
                      })
                  }
                  hasError={Object.keys(errors).includes('callback')}
                  helperText={errors.callback && errors.callback.message}
                />
                <Paragraph size="xs">
                  Se enviará una petición POST a la URL indicada
                </Paragraph>

                <Spacer.Vertical size="sm" />

                <Check
                  size="sm"
                  hasError={Object.keys(errors).includes('endpointCBackHabilitado')}
                  helperText={
                    errors.endpointCBackHabilitado && errors.endpointCBackHabilitado.message
                  }
                  label={(
                    <>
                      Habilitar Callback
                    </>
                    )}
                  {...register('endpointCBackHabilitado')}
                  isChecked={getValues().endpointCBackHabilitado || false}
                />

                <Spacer.Vertical size="sm" />
                <Divider />
                <Spacer.Vertical size="sm" />

                <Paragraph size="sm" weight="semibold">
                  Email
                </Paragraph>
                <Spacer.Vertical size="xs" />
                <Check
                  size="sm"
                  hasError={Object.keys(errors).includes('notifySignature')}
                  helperText={errors.terms && errors.terms.message}
                  label={(
                    <>
                      Enviar email al completar el proceso de firma
                    </>
                    )}
                  {...register('notifySignature')}
                  isChecked={getValues().notifySignature || false}
                />

                <Spacer.Vertical size="sm" />

                <Button
                  buttonType="submit"
                  type="primary"
                  isInline
                >
                  Guardar
                </Button>
              </form>
            </div>
          </Card>
        </div>
      ) : (
        <CenteredContent>
          <div style={{ width: '100%', maxWidth: 640 }}>
            <Card>
              <h5>
                Completa tu registro en la app y así obtendrás
                <strong> 5 firmas gratis</strong>
              </h5>
              <p>
                Descarga la app en las plataformas de
                {' '}
                {' '}
                <strong>
                  <a
                    target="_blank"
                    href="https://play.google.com/store/apps/details?id=se.firme.app.android.firmese&hl=es_CO&gl=US"
                    rel="noopener noreferrer"
                  >
                    Android
                  </a>
                </strong>
                {' '}
                {' '}
                y
                {' '}
                <strong>
                  <a
                    target="_blank"
                    href="https://apps.apple.com/co/app/id1568786537"
                    rel="noopener noreferrer"
                  >
                    IOS
                  </a>
                </strong>
              </p>
            </Card>
          </div>
        </CenteredContent>
      )}
    </Loading>
  );
}

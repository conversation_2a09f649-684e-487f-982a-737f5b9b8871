import * as Recoil from 'recoil';
import DocPreview from '@/components/organisms/DocPreview/DocPreview';
import * as userService from '@/services/user';
import * as Router from 'next/router';
import {
  act, render, screen, waitFor,
} from '@testing-library/react';

jest.mock('@/components/organisms/Loader', () => ({
  __esModule: true,
  default: ({ message }) => <div>{message}</div>,
}));

describe('Tests DocPreview', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show loader when the component is mount', async () => {
    const userMock = {
      idUsuarioC: 'idUsuarioC',
      access_token: 'access_token',
    };

    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);
    const getPreviewMock = jest.fn().mockImplementation(() => new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          status: 200,
          data: 'data',
        });
      }, 1000);
    }));

    jest.spyOn(userService, 'getPreview').mockImplementation(getPreviewMock);

    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ query: {} }));

    const component = (
      <Recoil.RecoilRoot>
        <DocPreview docId={1} />
      </Recoil.RecoilRoot>
    );

    await act(() => {
      render(component);
    });

    expect(screen.getByText('Cargando vista previa del documento')).toBeInTheDocument();
  });

  it('should show error message if doesn\t have access_token and token', async () => {
    const userMock = {
      idUsuarioC: 'idUsuarioC',
      access_token: 'access_token',
    };

    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);
    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ query: {} }));
    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [{}, jest.fn()]);
    const getPreviewMock = jest.fn();
    jest.spyOn(userService, 'getPreview').mockImplementation(getPreviewMock);

    const component = (
      <Recoil.RecoilRoot>
        <DocPreview docId={1} />
      </Recoil.RecoilRoot>
    );

    await act(() => {
      render(component);
    });

    await waitFor(() => {
      expect(screen.getByText('No se pudo obtener la vista previa del documento.')).toBeInTheDocument();
      expect(getPreviewMock).not.toHaveBeenCalled();
    });
  });

  it('should call getPreview if only have access_token', async () => {
    const userMock = {
      idUsuarioC: 'idUsuarioC',
      access_token: 'access_token',
    };

    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);
    const getPreviewMock = jest.fn().mockResolvedValue({ status: 200, data: 'data' });

    jest.spyOn(userService, 'getPreview').mockImplementation(getPreviewMock);

    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ query: {} }));

    const docId = 1;

    const component = (
      <Recoil.RecoilRoot>
        <DocPreview docId={docId} />
      </Recoil.RecoilRoot>
    );

    await act(() => {
      render(component);
    });

    await waitFor(() => {
      expect(getPreviewMock).toHaveBeenCalledTimes(1);
      expect(getPreviewMock).toHaveBeenCalledWith(docId, userMock.access_token, undefined);
    });
  });

  it('should call getPreview if only have token', async () => {
    const getPreviewMock = jest.fn().mockResolvedValue({ status: 200, data: 'data' });
    jest.spyOn(userService, 'getPreview').mockImplementation(getPreviewMock);

    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [{}, jest.fn()]);

    const tokenMock = 'token';

    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ query: { token: tokenMock } }));

    const docId = 1;
    const component = (
      <Recoil.RecoilRoot>
        <DocPreview docId={docId} />
      </Recoil.RecoilRoot>
    );

    await act(() => {
      render(component);
    });

    await waitFor(() => {
      expect(getPreviewMock).toHaveBeenCalled();
      expect(getPreviewMock).toHaveBeenCalledWith(docId, undefined, tokenMock);
    });
  });

  it('should show error message if getPreview response status is not 200', async () => {
    const userMock = {
      idUsuarioC: 'idUsuarioC',
      access_token: 'access_token',
    };

    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);
    const getPreviewMock = jest.fn().mockResolvedValue({ status: 500 });

    jest.spyOn(userService, 'getPreview').mockImplementation(getPreviewMock);

    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ query: {} }));

    const docId = 1;

    const component = (
      <Recoil.RecoilRoot>
        <DocPreview docId={docId} />
      </Recoil.RecoilRoot>
    );

    await act(() => {
      render(component);
    });

    await waitFor(() => {
      expect(screen.getByText('No se pudo obtener la vista previa del documento.')).toBeInTheDocument();
    });
  });

  it('should show img if getPreview response status is 200', async () => {
    const userMock = {
      idUsuarioC: 'idUsuarioC',
      access_token: 'access_token',
    };

    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);
    const getPreviewMock = jest.fn().mockResolvedValue({ status: 200, data: 'data' });
    jest.spyOn(userService, 'getPreview').mockImplementation(getPreviewMock);

    const b64Mock = 'ZGF0YQ==';

    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ query: {} }));

    const docId = 1;
    const component = (
      <Recoil.RecoilRoot>
        <DocPreview docId={docId} />
      </Recoil.RecoilRoot>
    );

    await act(() => {
      render(component);
    });

    const img = screen.getByAltText('Preview');

    await waitFor(() => {
      expect(img).toBeInTheDocument();
      expect(img.src).toBe(`data:image/png;base64,${b64Mock}`);
    });
  });

  it('should set className to img', async () => {
    const userMock = {
      idUsuarioC: 'idUsuarioC',
      access_token: 'access_token',
    };

    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);
    const getPreviewMock = jest.fn().mockResolvedValue({ status: 200, data: 'data' });
    jest.spyOn(userService, 'getPreview').mockImplementation(getPreviewMock);

    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ query: {} }));

    const docId = 1;
    const className = 'className';
    const component = (
      <Recoil.RecoilRoot>
        <DocPreview docId={docId} className={className} />
      </Recoil.RecoilRoot>
    );

    await act(() => {
      render(component);
    });

    await waitFor(() => {
      const img = screen.getByAltText('Preview');
      expect(img.className).toBe(className);
    });
  });
});

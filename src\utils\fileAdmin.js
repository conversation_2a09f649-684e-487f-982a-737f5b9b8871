export const fileToBase64String = (files) => new Promise((resolve, reject) => {
  const file = files;
  const reader = new FileReader();
  reader.readAsDataURL(file);
  reader.onload = () => resolve(reader.result);
  reader.onerror = (error) => reject(error);
});

export const CopyToClipboard = (containerId) => {
  const copyText = document.getElementById(containerId);

  navigator.clipboard.writeText(copyText.innerText).then(() => {
    alert(`Texto copiado con exito: ${copyText.value}`);
  }, (err) => {
    console.error('Could not copy text: ', err);
  });
};

export const humanizeSize = (bytes) => {
  if (!bytes || Number.isNaN(bytes)) return '-';
  if (bytes === 0) {
    return '0 Bytes';
  }

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / k ** i).toFixed(2))} ${sizes[i]}`;
};

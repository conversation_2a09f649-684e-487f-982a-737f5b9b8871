import React from 'react';
import {
  screen, render, fireEvent, waitFor,
} from '@testing-library/react';
import * as userService from '@/services/user';
import * as Notistack from 'notistack';
import NuevaContrasena from './NuevaContrasena';

jest.mock('@/components/organisms/StateHandler', () => ({
  __esModule: true,
  // eslint-disable-next-line no-nested-ternary
  default: jest.fn().mockImplementation(({ children, state }) => (state.haveError
    ? state.errorMessage?.content
    : state.isLoading
      ? state.loadMessage
      : children)),
}));

const getInputs = async () => {
  const newPasswd = await screen.findByPlaceholderText(/Nueva contraseña/i);
  const confirmPasswd = await screen.findByPlaceholderText(/Confirmar contraseña/i);
  return { newPasswd, confirmPasswd };
};

const token = '123456789';
describe('Test NuevaContrasena', () => {
  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should check token when component is mount', async () => {
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: {
          codigoTransaccion: '123456789',
          idUsuario: '123456789',
        },
      },
    }));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);

    render(<NuevaContrasena token={token} />);
    expect(getTokenCodeMock).toHaveBeenCalledTimes(1);
    expect(getTokenCodeMock).toHaveBeenCalledWith(token);
  });

  it('should show loading until the token is validated', async () => {
    const getTokenCodeMock = jest
      .fn()
      .mockImplementation(
        () => Promise.resolve({
          status: 200,
          data: {
            data: {
              codigoTransaccion: '123456789',
              idUsuario: '123456789',
            },
          },
        }),
      );
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);

    render(<NuevaContrasena token={token} />);
    expect(await screen.findByText(/Estamos generando tu código/i)).toBeInTheDocument();

    expect(await screen.findByText(/Cambio de contraseña de usuario/i)).toBeInTheDocument();
  });

  it('should not send request if passwords is not equals', async () => {
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: {
          codigoTransaccion: '123456789',
          idUsuario: '123456789',
        },
      },
    }));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);
    render(<NuevaContrasena token={token} />);

    const { newPasswd, confirmPasswd } = await getInputs();

    fireEvent.change(newPasswd, { target: { value: 'asasdsdsdsd' } });
    fireEvent.change(confirmPasswd, { target: { value: '123456' } });

    const btn = screen.getByText(/Cambiar contraseña/i);

    fireEvent.click(btn);
    await waitFor(() => {
      expect(enqueueSnackbarMock).toHaveBeenCalledTimes(1);
      expect(enqueueSnackbarMock).toHaveBeenCalledWith('Contraseñas no coinciden', { variant: 'error' });
    });
  });

  it('should not send request if any password is empty', async () => {
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: {
          codigoTransaccion: '123456789',
          idUsuario: '123456789',
        },
      },
    }));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);
    render(<NuevaContrasena token={token} />);

    const { newPasswd, confirmPasswd } = await getInputs();

    fireEvent.change(newPasswd, { target: { value: 'asasdsdsdsd' } });
    fireEvent.change(confirmPasswd, { target: { value: '' } });

    const btn = screen.getByText(/Cambiar contraseña/i);

    fireEvent.click(btn);
    expect(await screen.findByText(/La confirmación de contraseña es requerida/i)).toBeInTheDocument();
  });

  it('should send request', async () => {
    const getTokenCodeMock = jest.fn().mockResolvedValue({
      status: 200,
      data: {
        data: {
          codigoTransaccion: '123456789',
          idUsuario: '123456789',
        },
      },
    });
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);
    render(<NuevaContrasena token={token} />);

    const { newPasswd, confirmPasswd } = await getInputs();

    const password = '123456';
    fireEvent.change(newPasswd, { target: { value: password } });
    fireEvent.change(confirmPasswd, { target: { value: password } });

    const btn = screen.getByText(/Cambiar contraseña/i);

    const cambiarContrasenaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        mensaje: 'Contraseña cambiada con éxito',
      },
    }));
    jest.spyOn(userService, 'cambiarContrasena').mockImplementation(cambiarContrasenaMock);

    fireEvent.click(btn);

    await waitFor(() => {
      expect(cambiarContrasenaMock).toHaveBeenCalledTimes(1);
    });
  });

  it('should catch request getTokenCode exception', async () => {
    const error = new Error('Error');
    const consoleErrorMock = jest.fn();
    jest.spyOn(console, 'error').mockImplementation(consoleErrorMock);
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);
    render(<NuevaContrasena token={token} />);

    await waitFor(() => {
      expect(consoleErrorMock).toHaveBeenCalledTimes(1);
      expect(consoleErrorMock).toHaveBeenCalledWith(error);
    });
  });
});

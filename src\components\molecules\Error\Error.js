import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';

import Spacer from '@/components/layout/Spacer';
import Paragraph from '@/components/atoms/Paragraph';
import Heading from '@/components/atoms/Heading';
import Icon from '@/components/atoms/Icon';
import styles from './Error.module.css';

export function Error({ title, children, getStyles }) {
  return (
    <div className={getStyles('error')}>
      <Icon className="icon-warning" name="warning" size="md" />
      <Spacer.Horizontal size="sm" />
      <div>
        {title && <Heading>{title}</Heading>}
        <Paragraph>{children}</Paragraph>
      </div>
    </div>
  );
}

Error.propTypes = {
  children: PropTypes.node.isRequired,
  getStyles: PropTypes.func.isRequired,
  title: PropTypes.string,
};

Error.defaultProps = {
  getStyles: () => ({}),
};

export default withStyles(styles)(Error);

import React from 'react';
import {
  screen, render, fireEvent, waitFor,
} from '@testing-library/react';
import * as downloadPDF from '@/utils/downloadPDF';
import { checkAllValuesInDom, documentInfoMock } from '@/utils/forTesting';
import * as userService from '@/services/user';
import * as Notistack from 'notistack';
import DocumentInfo from './DocumentInfo';

jest.mock('@/components/organisms/DocPreview', () => ({
  __esModule: true,
  default: jest.fn((...props) => `DocPreview mock ${props.map((prop) => (typeof prop === 'object' ? JSON.stringify(prop) : prop)).join(' ')}`),
}));

describe('Tests DocumentInfo', () => {
  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show children if documentInfo.showInfo is false', () => {
    const component = (
      <DocumentInfo handleClick={jest.fn()} documentInfo={{ showInfo: false }}>
        <div>Test</div>
      </DocumentInfo>
    );
    render(component);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  it('should not show children if documentInfo.showInfo is false', () => {
    const component = (
      <DocumentInfo handleClick={jest.fn()} documentInfo={documentInfoMock}>
        <div>Test</div>
      </DocumentInfo>
    );
    render(component);
    expect(screen.queryAllByText('Test')).toEqual([]);
  });

  it('should show document info', () => {
    render(<DocumentInfo handleClick={jest.fn()} documentInfo={documentInfoMock} />);
    const excludedKeys = [
      'showInfo',
      'idArchivo',
      'propietario',
      'fechaRegistro',
      'cantidadConsultas',
      'hashArchivo',
      'fechaFirmaStr',
      'ipFirma',
      'agenteNavegador',
    ];
    checkAllValuesInDom(documentInfoMock, excludedKeys, { insensitive: true });
  });

  it('should call handleClick on click in back button', () => {
    const handleClick = jest.fn();
    render(<DocumentInfo documentInfo={documentInfoMock} handleClick={handleClick} />);
    fireEvent.click(screen.getByText(/Volver al inicio/i));
    expect(handleClick).toHaveBeenCalled();
  });

  it('should call downloadFileB64 on click in download button', () => {
    const downloadFileB64Mock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: 'Amsdsmkdsksdmkm==',
      },
    }));
    jest.spyOn(userService, 'downloadFileB64').mockImplementation(downloadFileB64Mock);
    render(<DocumentInfo handleClick={jest.fn()} documentInfo={documentInfoMock} />);
    fireEvent.click(screen.getByText(/Descargar/i));
    expect(downloadFileB64Mock).toHaveBeenCalled();
    expect(downloadFileB64Mock).toHaveBeenCalledWith(documentInfoMock.idArchivo);
  });

  it('should call downloadPDF if server response status is 200', async () => {
    const b64 = 'Amsdsmkdsksdmkm==';
    const downloadFileB64Mock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: b64,
      },
    }));
    jest.spyOn(userService, 'downloadFileB64').mockImplementation(downloadFileB64Mock);
    const downloadPDFMock = jest.fn();
    jest.spyOn(downloadPDF, 'default').mockImplementation(downloadPDFMock);

    render(<DocumentInfo handleClick={jest.fn()} documentInfo={documentInfoMock} />);
    fireEvent.click(screen.getByText(/Descargar/i));

    await waitFor(() => {
      expect(downloadPDFMock).toHaveBeenCalled();
      expect(downloadPDFMock).toHaveBeenCalledWith(b64, documentInfoMock.nombreArchivo);
    });
  });

  it('should show snackbar if server response status is not 200', async () => {
    const message = 'Error';
    const downloadFileB64Mock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 500,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'downloadFileB64').mockImplementation(downloadFileB64Mock);

    render(<DocumentInfo handleClick={jest.fn()} documentInfo={documentInfoMock} />);
    fireEvent.click(screen.getByText(/Descargar/i));

    await waitFor(() => {
      expect(enqueueSnackbarMock).toHaveBeenCalled();
      expect(enqueueSnackbarMock).toHaveBeenCalledWith(message, { variant: 'error' });
    });
  });

  it('should catch request exception', async () => {
    const error = new Error('Error');
    const downloadFileB64Mock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'downloadFileB64').mockImplementation(downloadFileB64Mock);

    const logErrorMock = jest.fn();
    jest.spyOn(console, 'error').mockImplementation(logErrorMock);

    render(<DocumentInfo handleClick={jest.fn()} documentInfo={documentInfoMock} />);
    fireEvent.click(screen.getByText(/Descargar/i));

    await waitFor(() => {
      expect(logErrorMock).toHaveBeenCalled();
      expect(logErrorMock).toHaveBeenCalledWith(error);
    });
  });
});

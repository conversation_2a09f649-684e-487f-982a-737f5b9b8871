.grid-card {
    display: grid;
    gap: var(--doc-card-gap);
    grid-auto-rows: var(--doc-card-height);
    grid-template-columns: repeat(auto-fill, minmax(min(100%, var(--doc-card-min-width)), 1fr));
    margin-bottom: var(--doc-card-margin-bottom);
}

.preview {
    width: 100%;
    height: 300px;
    object-fit: cover;
    object-position: top;
    background: var(--color-base-white);
}

.modal-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-content-container {
    padding: 16px 32px;
}


.actions-container {
    border: var(--border-width-thin) solid var(--color-base-transparent);
    height: 3rem;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: .5rem;
}

.search-container {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.search-input-wrapper {
    flex: 0 0 90%;
    width: 90%;
}

.refresh-button-wrapper {
    flex: 0 0 auto;
    display: flex;
    justify-content: center;
    align-items: center;
}

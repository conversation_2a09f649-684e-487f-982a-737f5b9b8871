import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Container from '@/components/layout/Container';
import { getTokenCode, cambiarContrasena } from '@/services/user';
import icon from '@/assets/images/logo_principal.png';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import StateHandler from '@/components/organisms/StateHandler';
import Button from '@/components/atoms/Button';
import Input from '@/components/atoms/Input';
import { useForm } from 'react-hook-form';
import Heading from '@/components/atoms/Heading';
import Spacer from '@/components/layout/Spacer';
import Paragraph from '@/components/atoms/Paragraph';

function NuevaContrasena({ token }) {
  const router = useRouter();
  const [state, setState] = useState({
    haveError: false,
    isLoading: true,
    loadMessage: 'Estamos generando tu código',
    code: '',
    id: '',
    errorMessage: {
      header: 'Ha ocurrido un error la obtención del código',
      content: 'Este mensaje es automático',
      icon,
    },
  });

  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      npasswd: '',
      cpasswd: '',
    },
  });

  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    if (!token) {
      return;
    }
    getTokenCode(token)
      .then((response) => {
        if (response.status === 200) {
          setState({
            ...state,
            isLoading: false,
            code: response.data.data.codigoTransaccion,
            id: response.data.data.idUsuario,
          });
        } else if (response.status !== 200) {
          setState({
            ...state,
            isLoading: false,
            haveError: true,
            content: response.data.mensaje,
            header: response.data.mensaje,
            errorMessage: {
              content: response.data.mensaje,
              header: 'No es posible cambiar contraseña',
              icon,
            },
          });
        }
      })
      .catch((err) => console.error(err));
  }, [token]);

  const handleErrorButton = async () => {
    await router.replace('/login');
  };

  const onSubmit = (values) => {
    const { npasswd, cpasswd } = values;
    if (npasswd !== cpasswd) {
      enqueueSnackbar('Contraseñas no coinciden', { variant: 'error' });
    } else {
      setState({
        ...state,
        isLoading: true,
      });
      cambiarContrasena( {
        passwd: cpasswd,
        ctc: token,
      }).then((response) => {
        if (response.status === 200) {
          setState({
            ...state,
            isLoading: false,
            haveError: true,
            errorMessage: {
              content: response.data.mensaje,
              header: 'Contraseña cambiada',
              icon,
            },
          });
        } else {
          setState({
            ...state,
            isLoading: false,
            haveError: true,
            content: response.data.mensaje,
          });
        }
      })
        .catch((err) => console.error(err));
    }
  };

  return (
    <div>
      <Container>
        <StateHandler handleErrorButton={handleErrorButton} state={state}>
          <Container maxWidth="md">
            <Heading>
              Cambio de contraseña de usuario
            </Heading>
            <Spacer.Vertical size="lg" />
            <Paragraph isCentered size="sm">A continuación ingresa la nueva contraseña de usuario</Paragraph>
            <form onSubmit={handleSubmit(onSubmit)}>
              <Input
                type="password"
                placeholder="Nueva contraseña"
                {
                  ...register('npasswd', {
                    required: 'La nueva contraseña es requerida',
                    minLength: {
                      value: 6,
                      message: 'La nueva contraseña debe tener al menos 6 caracteres',
                    },
                  })
                }
                hasError={Object.keys(errors).includes('npasswd')}
                helperText={errors.npasswd && errors.npasswd.message}
              />
              <Input
                type="password"
                placeholder="Confirmar contraseña"
                {
                  ...register('cpasswd', {
                    required: 'La confirmación de contraseña es requerida',
                    minLength: {
                      value: 6,
                      message: 'La confirmación de contraseña debe tener al menos 6 caracteres',
                    },
                  })
                  }
                hasError={Object.keys(errors).includes('cpasswd')}
                helperText={errors.cpasswd && errors.cpasswd.message}
              />
              <Button
                isInline
                buttonType="submit"
              >
                Cambiar contraseña
              </Button>
            </form>
          </Container>
        </StateHandler>
      </Container>
    </div>
  );
}

NuevaContrasena.propTypes = {
  token: PropTypes.string.isRequired,
};

export default NuevaContrasena;

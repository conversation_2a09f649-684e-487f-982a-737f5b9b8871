import '@testing-library/jest-dom/extend-expect';

jest.mock('next/image', () => ({
  __esModule: true,
  default: () => 'Next image stub' // whatever
  ,
}));

jest.mock('next/dynamic', () => ({
  __esModule: true,
  default: (...props) => {
    const dynamicModule = jest.requireActual('next/dynamic');
    const dynamicActualComp = dynamicModule.default;
    const RequiredComponent = dynamicActualComp(props[0]);
    // eslint-disable-next-line no-unused-expressions
    RequiredComponent?.preload
      ? RequiredComponent.preload()
      : RequiredComponent.render.preload();
    return RequiredComponent;
  },
}));

window.matchMedia = jest.fn().mockImplementation((query) => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(), // deprecated
  removeListener: jest.fn(), // deprecated
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
}));

jest.mock('@/utils/fileAdmin', () => ({
  __esModule: true,
  ...jest.requireActual('@/utils/fileAdmin'),
}));

jest.mock('@/utils/utils', () => ({
  __esModule: true,
  ...jest.requireActual('@/utils/utils'),
}));

jest.mock('@/utils/downloadPDF', () => ({
  __esModule: true,
  ...jest.requireActual('@/utils/downloadPDF'),
}));

jest.mock('@/services/user', () => ({
  __esModule: true,
  ...jest.requireActual('@/services/user'),
}));

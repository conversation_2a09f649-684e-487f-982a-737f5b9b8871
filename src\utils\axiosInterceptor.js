import axios from 'axios';
import { refreshToken } from '@/services/user';

let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach((prom) => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

// Interceptor para respuestas
axios.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest.isRetry) {
      if (isRefreshing) {
        // Si ya se está refrescando, agregar a la cola
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then((token) => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return axios(originalRequest);
        }).catch((err) => Promise.reject(err));
      }

      originalRequest.isRetry = true;
      isRefreshing = true;

      try {
        const response = await refreshToken();
        if (response.status === 200) {
          const { access_token: accessToken } = response.data;
          // Actualizar el header de la petición original
          originalRequest.headers.Authorization = `Bearer ${accessToken}`;
          processQueue(null, accessToken);
          return axios(originalRequest);
        }
      } catch (refreshError) {
        processQueue(refreshError, null);
        // Redirigir al login si el refresh falla
        window.location.href = '/login?reason=token_expired';
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }

    return Promise.reject(error);
  },
);

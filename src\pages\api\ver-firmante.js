import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import path from 'path';
import fs from 'fs';
import catchApiError from '@/utils/catchApiError';
import getPDFPreview from '@/utils/getPDFPreview';

async function verFirmanteRoute(req, res) {
  try {
    const { user } = req.session;
    if (!user?.access_token) {
      return res.status(401).send();
    }

    const { idArchivo } = req.query;
    const { data } = await fetchApi.post(
      `/firma/manager/ver-firmante/?idArchivo=${idArchivo}`,
      undefined,
      {
        headers: {
          Authorization: `Bearer ${user.access_token}`,
          ...getDefaultHeaders(req),
        },
      },
    );

    const pdfDir = path.join(
      __dirname,
      '../../../../tmp/',
    );

    if (!fs.existsSync(pdfDir)) {
      fs.mkdirSync(pdfDir);
    }

    const pdfPath = path.join(
      pdfDir,
      `${Math.floor(new Date().getTime() * Math.random())}.pdf`,
    );
    console.log({ pdfPath });
    let docs;

    try {
      await fs.writeFileSync(pdfPath, data?.data?.b64, { encoding: 'base64' });
      const previewB64 = await getPDFPreview(pdfPath);
      docs = previewB64;
    } catch (e) {
      console.error(e);
      docs = null;
    }

    if (fs.existsSync(pdfPath)) {
      fs.unlinkSync(pdfPath);
    }

    return res.send({
      ...data,
      data: {
        ...(data?.data || {}),
        b64: docs || data?.data?.b64 || null,
      },
    });
  } catch (error) {
    return catchApiError(error, res);
  }
}
export default withSessionRoute(verFirmanteRoute);

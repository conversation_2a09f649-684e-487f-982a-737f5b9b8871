// estrategia para la obtención y busqueda de documentos firmados
export const signedDocsStrategy = {
  async fetchData({ page, pageSize, user, searchName }) {
    const { getDocsFirmadosBusqueda } = await import('@/services/user');
    try {
      const result = await getDocsFirmadosBusqueda(page, pageSize, user.access_token, searchName);
      console.log('Respuesta de getDocsFirmadosBusqueda:', result);
      console.log(`Estrategia: firmados llamando a getDocsFirmadosBusqueda para la página: ${page} con búsqueda: "${searchName}"`);
      return result;
    } catch (error) {
      console.error(`Estrategia: firmados fetch fallido:`, error.message);
      throw error;
    }
  },

  getQueryKey(searchName, userId) {
    return ['docs', 'signed-docs', userId, searchName];
  },
};

// estrategia para la obtención y busqueda de documentos unificados propietario y firmados
export const unificadosDocsStrategy = {
  async fetchData({ page, pageSize, user, searchName }) {
    const { getDocsUnificadosBusqueda } = await import('@/services/user');
    try {
      const result = await getDocsUnificadosBusqueda(page, pageSize, user.access_token, searchName);
      console.log(`Estrategia: unificados llamando a getDocsUnificadosBusqueda para la página: ${page} con búsqueda: "${searchName}`);
      return result;
    } catch (error) {
      console.error(`Estrategia unificados: busqueda fallida con:`, error.message);
      throw error;
    }
  },

  getQueryKey(searchName, userId) {
    return ['docs', 'signed-docs-uni', userId, searchName];
  },
};

openapi: '3.0.0'
info:
  title: Fírmese-ApiClients
  version: '1.0'
servers:
  - url: https://test.firme.se:9999/firma/api/v1
paths:
  /security/oauth/token:
    post:
      description: Autenticación de usuario
      operationId: getSessionToken
      tags:
        - security
          - oauth
      security:
        - basicAuth: [Basic ZmlybWVzZTpWbktmaXJtM1Nl]
      parameters:
        - in: query
          name: username
          required: true
          schema:
            type: string
        - in: query
          name: password
          required: true
          schema:
            type: string
        - in: query
          name: grant_type
          required: true
          schema:
            default: "password"
            type: string
        - in: header
          name: Content_Type
          required: true
          schema:
            type: string
            default: "application/x-www-form-urlencoded" 
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/x-www-form-urlencoded:
              schema:
                $ref: '#/components/schemas/securityResponse'
  /firma/manager/reenviar-solicitud-firmante:
    post:
      description: 
        Este servicio es consumido para subir los archivos
        al sistema para comenzar el proceso de firma
      operationId: ResendRequestSigners
      tags:
        - firma
          - manager
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ResendRequestRequest'
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/standardResponse'               
  /firma/manager/subir-multiple/b64:
    post:
      description: 
        Este servicio es consumido para subir los archivos
        al sistema para comenzar el proceso de firma
      operationId: uploadDocument
      tags:
        - firma
          - manager
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/uploadDocumentRequest'
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/standardResponse'
  /firma/manager/docs-pendiente:
    post:
      description: 
        Este servicio lista los documentos que se han subido al
        sistema y están pendientes por firmar
      operationId: getPendingDocuments
      tags:
        - firma
          - manager
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: idUsuario
          schema:
            type: integer
        - in: query
          name: page
          schema: 
            type: integer
        - in: query
          name: per_page
          schema:  
            type: integer
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/x-www-form-urlencoded:
              schema:
                $ref: '#/components/schemas/getPendingDocumentsResponse'
  /firma/manager/fimar-documentos-multiple-firmante: 
    #TODO: fimar está mal escrito así en el postman y en producción 
    post:
      description: 
        Servicio que envía un correo electrónico a cada una de las 
        personas a las que se les solicitóque firmaran el documento
      operationId: signDocumentsMutipleSigners
      tags:
        - firma
          - manager
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/signDocumentsMutipleSignersRequest'
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/standardResponse'          
  /firma/manager/fimar-multiple:
    post:
      description: 
        Servicio que firma multiples documentos por un usuario
      operationId: signMultipleDocuments
      tags:  
        - firma
          - manager
      security:
        - bearerAuth: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/signMultipleDocumentsRequest'
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/standardResponse'   
  /firma/manager/validarsms-multiple:
    post:
      description: 
        Validación del código OTP enviado a sms o al whatsapp 
      operationId: validateOTPCode
      tags:
        - firma
          - manager
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: csms
          required: true
          schema:
            type: string
        - in: query
          name: usms
          required: true
          schema:
            type: string
          description: El ID del usuario
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/standardResponse'
  /firma/manager/docs-firmado:
    post:
      description: 
        Llama la lista de los documento firmados por páginas
      operationId: getSignedDocuments
      tags:
        - firma
          - manager
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: idUsuario
          required: true
          schema:
            type: string
        - in: query
          name: page
          required: true
          schema:
            type: integer
        - in: query
          name: per_page
          required: true
          schema:
            type: integer
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/documentStructure'
  /firma/manager/ver-firmante:
    post:
      description: Función para traer la data de un documeto en específico por idArchivo
      operationId: getSignDetails
      tags:
        - firma
          - manager
      security:
        - bearerAuth: []
      parameters:
        - in: query
          name: idUsuario
          required: true
          schema:
            type: string
        - in: query
          name: idArchivo
          required: true
          schema:
            type: integer
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/getSignDetailsResponse'
  /firma/manager/v2/solicitar-firma:
    post:
      description: 
        servicio para solicitar firmas a multiples personas en multiples documentos 
      operationId: solicitSignMultiple
      tags:
        - firma
          - manager
            - v2
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/solicitSignMultipleRequest'
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/standardResponse'
  /firma/manager/v2/solicitar-firma-interesado:  
    post:
      description: 
        servicio para solicitar firmas a multiples personas en multiples documentos 
      operationId: solicitSignInterested
      tags:
        - firma
          - manager
            - v2
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/solicitSignMultipleRequest'
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/solicitSignMultipleInterestedResponse' #Este es el que se usa para enrole.se
  /firma/manager/v2/fimar-documentos-multiple-firmante:
    post:
      description: 
        servicio para solicitar firmas a multiples personas en multiples documentos 
      operationId: solicitSignMultipleInterested
      tags:
        - firma
          - manager
            - v2
      security:
        - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/solicitSignMultipleRequest'
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/standardResponse'
  /validacion/registro/downloadB64/{idArchivo}:
    post:
      description: Trae el documento en b64 para poder descargarlo
      operationId: getDocumentbyId
      tags:
        - validacion
          - registro
      security:
        - bearerAuth: []
      parameters:
        - in: path
          name: idArchivo
          required: true
          schema:
            type: integer
      responses:
        '404':
          description: NOT FOUND
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/notFoundResponses'
        '200':
          description: OK
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/getDocumentbyIdResponse'  
components:
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic
    bearerAuth:
      type: http 
      scheme: bearer
      bearerFormat: JWT
  schemas:
    firmanteStructure:
      type: object
      properties:
        email:
          type: string
        numeroDocumento:
          type: string
        fechaExpedicion:
          type: string
        numeroCelular:
          type: string
    archivoStructure:
      type: object
      properties:
        nombreArchivo:
          type: string
        tipoFirma:
          type: string
        b64:
          type: string
          format: b64
        estado:
          type: string
        detalleEstado:
          type: string
        firmantes:
          type: array
          items:
            $ref: '#/components/schemas/firmanteStructure'
        propietario:
          type: string     
    documentStructure:
      type: object
      properties:
        idArchivo:
          type: integer
        nombreArchivo:
          type: string
        hashArchivo:
          type: string
        ipOrigen:
          type: string
        cantidadFirmas:
          type: integer
        cantidadFirmado:
          type: integer
        fechaRegistro:
          type: string
        fechaRegistroStr:
          type: string
        cantidadConsultas:
          type: integer
        ip:
          type: string
        observaciones:
          type: string
        resultadoFirma:
          type: string
        estado:
          type: string
        firmas:
          nullable: true
          type: array
          items:
            type: object
            properties:
              fechaFirma:
                type: string
                format: date
              subioArchivo:
                type: boolean
              firma:
                type: object
                properties:
                  numeroDocumento:
                    type: string
                  correoElectronico:
                    type: string
                  nombreCompleto:
                    type: string
                  nombreTipoDocumento:
                    type: string
              hashFirma:
                type: string
              ipFirma:
                type: string
              fechaFirmaSTR:
                type: string
                format: date
              agenteNavegador:
                type: string
              hashfirma:
                type: string #TODO: ES EXACTAMENTE LO MISMO QUE EL DE ARRIBA PERO CON UNA MINUSCULA DISTINTA
        b64:
          type: string
          format: b64
        emailFirmantes:
          type: string
        tipoFirma:  
          type: string
        propietario:
          type: string
        descripcion:
          type: string
    securityResponse:
      type: object
      properties:  
        access_token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZFVzdWFya..."
          description: 
            (JWT) Token que se deben almacenar en la sesión para el consumo de
            los servicios que los requieran. Este tiene un periodo de vigencia
            el cual es configurado de acuerdo al cliente. En el caso que el 
            token caduque, puede usar el refresh_token para solicitar un nuevo
            token sin necesidad de enviar los parámetros iniciales. 
        token_type:
          type: string
          default: "bearer"
        refresh_token:
          type: string
          example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZFVzdWFya..."
          description: 
            (JWT) Token que se deben almacenar en la sesión para el consumo de
            los servicios que los requieran. Este tiene un periodo de vigencia 
            el cual es configurado de acuerdo al cliente. En el caso que el 
            token caduque, puede usar el refresh_token para solicitar un nuevo
            token sin necesidad de enviar los parámetros iniciales.
        expires_in:
          type: number
          example: 11999
          description: 
            Tiempo de expiración del access_token   en segundos.
        scope:
          type: string
          example: "foo read write"
          description: 
            No tiene documentación.
        idUsuarioA:
          type: string
          example: "<EMAIL>"
          description: 
            Id del usuario logueado
        nombreUsuario:
          type: string
          example: "LEONARDO MACHADO"
          description: 
            Nombre del usuario logueado.
        email:
          type: string
          example: "<EMAIL>"
          description: 
            Email del usuario logueado.
        idUsuarioC:
          type: number
          example: 1
          description: 
            Id del Usuario de la aplicación Firmese. Este Id debe almacenarse
            en sesión y es el que se debe enviar en todos los servicios que 
            requieran el parámetro idUsuario.
    standardResponse:
      type: object
      properties:
        codigo:
          type: string
          description: 000 OK 001 Error
        mensaje:
          type: string
          description: 000 "OK" 001 "Error"
        data:
          oneOf:
            - type: string
            - type: integer
          nullable: true
          example: Paquete agregado
    ResendRequestRequest:
      type: object
      properties:
          signatory:
            type: string
            example: "<EMAIL>"
          code:
            type: string
            description: It is type string but it is a numeric code 
            example: 20329
          mode: 
            type: string
            enum:
              - "wta" #whatsapp
              - "eml" #email
    uploadDocumentRequest:
      type: object
      properties:
        nombreArchivo:
          type: string
          example: "contrato_001.pdf"
          description: 
            Nombre del archivo que se sube al proceso de firma
        cantidadFirmas:
          type: integer
          example: 1
          minimum: 1
          description: 
            cantidad de firmas que va a requerir el archivo
        idUsuario:
          type: integer
          example: 51
          description: 
            ID de registro del usuario que sube el archivo
            (se obtiene con la autenticación de usuario)
        archivo64:
          type: string
          format: byte
          description: 
            Archivo que se va a subir en base 64
    getPendingDocumentsResponse:
      type: object
      properties:
        content:
          type: array
          description: 
            Nodo con lista de todos los documentos que tiene el usuario
            pendiente de firma
          items:
            type: object
            properties:
              codigoTransaccion:
                type: string
                description: 
                  Codigo OTP del documento
              firmantes:
                type: array
                items:
                  type: string
              idArchivo:
                type: string
                description: 
                  Id del archivo subido
              tipoFirma:
                type: string
                enum:
                  - Multiple
                  - Single
                description: 
                  Tipo de firma del documento
              descripcion:
                type: string
              fechaRegistro:
                type: string
                format: date
                description: 
                  Fecha de registro del archivo subido
              fechaVencimiento:
                type: string
                format: date
              propietario:
                type: string
              estado:
                type: string 
                enum:
                  - 1 
                  - 2
                description: 
                  estado de firma del documentos
                  1 si está pendiente de firma
                  2 si está firmado
              hashArchivo:
                type: string
                description: 
                  Código hashing del archivo original sha512
              nombreArchivo:  
                type: string
                description: 
                  Nombre del archivo subido
              ip:
                type: string
                format: ipv4
                description: 
                  Ip de origen de donde se subió el archivo   
              firmasRequeridas:
                type: integer
                description: 
                  Número de firmas requeridas en el documento
              esSolicitud:
                type: string
              solicitudFirmada:
                type: string
              numeroDocumentoPropietario:
                type: string
              totalFirmas:
                type: string
                description:
                  Número de firmas que actualmente tiene el documento
        pageable:
          type: object
          description: 
            Nodo con la información de paginación de la tabla
          properties:            
            sort: 
              type: object
              description: 
                información sobre el orden en la paginación de la tabla
              properties:
                sorted:
                  type: boolean
                  description: 
                    Si la información de la tabla está organizada
                unsorted:
                  type: boolean
                  description: 
                    Si la información no está organizada    
                empty:
                  type: boolean
                  description: 
                    Si la información de la taba está vacía
            offset:
              type: integer
            pageSize:
              type: integer
              description: 
                Número de documentos en esa página de la tabla
            pageNumber:
              type: integer
              description: 
                Número de la página en la que se encuentra actualmente
            unpaged:
              type: boolean
            paged:
              type: boolean
        totalElements:
          type: integer
          description: 
            Número total de elementos en la tabla
        totalPages:
          type: integer
          description: 
            Número total de páginas en la tabla
        last:
          type: boolean
          description: 
            Si actualmente se encuentraen la última página de la tabla
        number:
          type: integer
        sort:
          type: object
          properties:
            sorted:
              type: boolean
              description: 
                Si la información de la tabla está organizada
            unsorted:
              type: boolean
              description: 
                Si la información no está organizada    
            empty:
              type: boolean
              description: 
                Si la información de la taba está vacía
        size: 
          type: integer
          description: 
            Número total de elementos permitidos por página en la tabla
        numberOfElements:
          type: integer
          description: 
            Número total de elementos en la tabla
        first:
          type: boolean
          description: 
            Si alcualmente se enuentra en la primera página de la tabla
        empty:
          type: boolean
    signDocumentsMutipleSignersRequest:
      type: object
      properties:
        firmantes:
          type: array
          items:
            type: object
            properties:
              idUsuario:
                type: string 
        documentos: 
          type: array
          items:
            type: object
            properties:
              idArchivo:
                type: string
              idUsuario: 
                type: string
        tipoFirma:
          type: string     
    signMultipleDocumentsRequest:
      type: object
      properties:
        idArchivo:
          type: string
        idUsuario: 
          type: string
    sendNotificationRequest:
      type: object
      properties:
        correos:
          type: string
        archivos:
          type: array
          items:
            type: object
            properties:
              idArchivo:
                type: string  
    getSignDetailsResponse:
      type: object
      properties:
        codigo:
          type: string
          description: 000 OK 001 Error
        mensaje:
          type: string
          description: 000 "OK" 001 "Error"
        data:
          type: object
          $ref: '#/components/schemas/archivoStructure'
    solicitSignMultipleRequest:
      type: object
      properties:
        tipoFirma:
          type: string
        firmantes:
          type: array
          items:
            type: object
            oneOf:
              - $ref: '#/components/schemas/firmanteStructure'
              - $ref: '#/components/schemas/firmanteStructure/properties/email'
            #Solo correo o La información completa    
        documentos:
          type: array
          items:
            type: object
            properties:
              nombreArchivo: 
                type: string
              cantidadFirmas:
                type: integer
              idUsuario:
                type: string
              archivo64:
                type: string
                format: b64
      required:
        - firmantes
        - documentos
    solicitSignMultipleInterestedResponse:
      type: object
      properties:
        codigo:
          type: string
          enum:
            - 000
            - 001
            - 002
        mensaje:
          type: string
        data:
          type: array
          items:
            type: object
            properties:
            $ref: '#/components/schemas/documentStructure'
    notFoundResponses:
      type: object
      properties:
        timestamp: 
          type: string
          format: date
        status: 
          type: integer
        error:
          type: string
        message:
          type: string
        path: 
          type: string 
    getDocumentbyIdResponse:
      type: object
      properties:
        codigo:
          type: string
          description: 000 OK 001 Error
        mensaje:
          type: string
          description: 000 "OK" 001 "Error"
        data:
          type: string
          format: b64

import React from 'react';
import { render } from '@testing-library/react';
import SignAgreementModal from './SignAgreementModal';

jest.mock('@/components/atoms/Modal', () => ({
  __esModule: true,
  default: jest.fn(({ title }) => <span>{title}</span>),
}));

describe('Tests SignAgreementModal', () => {
  it('should render correctly', () => {
    const { container } = render(<SignAgreementModal onClose={jest.fn()} />);
    expect(container).toHaveTextContent('ACUERDO DE FIRMA ELECTRÓNICA');
  });
});

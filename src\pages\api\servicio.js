import { stringify } from 'query-string';
import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function servicioRoute(req, res) {
  try {
    const { data } = await fetchApi.post(
      '/registro/usuario/servicio',
      stringify(req.body),
      {
        headers: getDefaultHeaders(req),
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(servicioRoute);

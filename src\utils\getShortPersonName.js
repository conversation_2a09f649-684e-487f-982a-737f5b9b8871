import toCapitalize from '@/utils/toCapitalize';

export default function getShortPersonName(fullName) {
  const names = fullName.split(' ').map((name) => toCapitalize(name.trim().toLowerCase()));
  if (names.length === 1) {
    return fullName;
  }

  if ([2, 3].includes(names.length)) {
    return `${names[0]} ${names[1]}.`;
  }

  if (names.length === 4) {
    return `${names[0]}  ${names[2]}`;
  }
}

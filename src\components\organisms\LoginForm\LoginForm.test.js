import React from 'react';
import {
  render, fireEvent, waitFor, screen,
} from '@testing-library/react';
import * as Recoil from 'recoil';
import * as Router from 'next/router';
import * as utils from '@/utils/utils';
import * as userService from '@/services/user';
import LoginForm from './LoginForm';

const getInputs = () => {
  const emailInput = screen.getByPlaceholderText(/Correo electrónico/i);
  const passwordInput = screen.getByPlaceholderText(/Contraseña/i);
  return { emailInput, passwordInput };
};

describe('Tests LoginForm', () => {
  const pushMock = jest.fn();
  jest.spyOn(Router, 'useRouter').mockImplementation(() => ({
    push: pushMock,
    prefetch: jest.fn().mockImplementation(() => Promise.resolve()),
  }));

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should login if response status is 200', async () => {
    const email = '<EMAIL>';
    const password = '123456';
    const loginPayload = {
      username: email,
      password,
      grant_type: 'password',
    };

    const userInfo = {
      access_token: 'usdghiadya8769823u90jwkldwswd',
      token_type: 'bearer',
      refresh_token: 'sakljjd872ksdjfa923239j',
      expires_in: 11999,
      scope: 'foo read write',
      idUsuarioA: '<EMAIL>',
      nombreUsuario: 'Test Usuario',
      email: '<EMAIL>',
      idUsuarioC: '1',
      jti: 'jskhadkldh8-sdajksldjaskjd-kaslkdjadk',
    };

    const getLoginMock = jest.fn().mockResolvedValue({
      status: 200,
      data: userInfo,
    });
    jest.spyOn(userService, 'getlogin').mockImplementation(getLoginMock);

    const saveHourLoginMock = jest.fn();
    jest.spyOn(utils, 'saveHourLogin').mockImplementation(saveHourLoginMock);

    const setUserMock = jest.fn();
    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [{}, setUserMock]);

    const { getByRole } = render(<Recoil.RecoilRoot><LoginForm /></Recoil.RecoilRoot>);

    const { emailInput, passwordInput } = getInputs();

    fireEvent.change(emailInput, { target: { value: email } });
    fireEvent.change(passwordInput, { target: { value: password } });

    const button = getByRole('button', { name: /Iniciar sesión/i });
    button.click();

    await waitFor(() => {
      expect(getLoginMock).toHaveBeenCalled();
      expect(getLoginMock).toHaveBeenCalledWith(loginPayload);

      expect(setUserMock).toHaveBeenCalled();
      expect(setUserMock).toHaveBeenCalledWith(userInfo);

      expect(saveHourLoginMock).toHaveBeenCalled();

      expect(pushMock).toHaveBeenCalled();
      expect(pushMock).toHaveBeenCalledWith('/');
    });
  });

  it('should show error if response status is not 200', async () => {
    const email = '<EMAIL>';
    const password = '654321';

    const getLoginMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 401,
    }));
    jest.spyOn(userService, 'getlogin').mockImplementation(getLoginMock);

    const { getByRole, findByText } = render(<Recoil.RecoilRoot><LoginForm /></Recoil.RecoilRoot>);

    const { emailInput, passwordInput } = getInputs();

    fireEvent.change(emailInput, { target: { value: email } });
    fireEvent.change(passwordInput, { target: { value: password } });

    const button = getByRole('button', { name: /Iniciar sesión/i });
    button.click();

    expect(await findByText(/Error/i)).toBeInTheDocument();
  });

  describe('Form validation', () => {
    it('should all fields are required', async () => {
      const { getByRole, getByText } = render(<Recoil.RecoilRoot><LoginForm /></Recoil.RecoilRoot>);
      const button = getByRole('button', { name: /Iniciar sesión/i });
      button.click();

      await waitFor(() => {
        expect(getByText(/El correo electrónico es obligatorio/i)).toBeInTheDocument();
        expect(getByText(/La contraseña es obligatoria/i)).toBeInTheDocument();
      });
    });

    it('should validate email', async () => {
      const {
        getByRole,
        findByText,
      } = render(<Recoil.RecoilRoot><LoginForm /></Recoil.RecoilRoot>);

      const { emailInput, passwordInput } = getInputs();

      fireEvent.change(emailInput, { target: { value: 'invalid' } });
      fireEvent.change(passwordInput, { target: { value: '123456' } });

      const button = getByRole('button', { name: /Iniciar sesión/i });
      button.click();

      expect(await findByText(/El correo electrónico no es válido/i)).toBeInTheDocument();
    });
  });
});

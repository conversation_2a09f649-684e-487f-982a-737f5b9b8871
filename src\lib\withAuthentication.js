import { withSessionSsr } from './withSession';

const withAuthentication = withSessionSsr(
  async ({ req }) => {
    const { user = null } = req.session;

    if (!user?.access_token) {
      req.session.destroy();
      return {
        redirect: {
          destination: req.url === '/' ? '/login' : `/login?redirect=${req.url}`,
          permanent: false,
        },
      };
    }

    if (!user?.enableOris && req.url.startsWith('/oris')) {
      return {
        redirect: {
          destination: '/',
          permanent: false,
        },
      };
    }

    return {
      props: {
        user,
      },
    };
  },
);
export default withAuthentication;

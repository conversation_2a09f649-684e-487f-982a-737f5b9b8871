import React, { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import FirmaExitosa from '@/components/organisms/FirmaExitosa';
import { userState } from '@/recoil/atoms';
import { withSessionSsr } from '@/lib/withSession';

export const getServerSideProps = withSessionSsr(
  async ({ req }) => {
    const { user = null } = req.session;

    return {
      props: {
        user,
      },
    };
  },
);

export default function FirmaExitosaPage({ user }) {
  const [, setUser] = useRecoilState(userState);

  useEffect(() => {
    setUser(user);
  }, [user]);

  return (
    <FirmaExitosa />
  );
}

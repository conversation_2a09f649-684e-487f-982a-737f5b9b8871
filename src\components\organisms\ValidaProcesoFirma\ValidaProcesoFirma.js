import React, { useState, useEffect } from 'react';
import useDigitInput from '@/hooks/useDigitInput';
import { useRouter } from 'next/router';
import { useRecoilState } from 'recoil';
import Container from '@/components/layout/Container';
import Grid from '@mui/material/Grid';
import { saveStorage, loadItemStorage } from '@/utils/utils';
import { userState } from '@/recoil/atoms';
import { verifiySignProcess, validarOrdenFirma } from '@/services/user';
import icon from '@/assets/images/Logo3.png';
import SignAgreementModal from '@/components/molecules/SignAgreementModal';
import StateHandler from '@/components/organisms/StateHandler';
import Button from '@/components/atoms/Button';
import Check from '@/components/atoms/Check';
import Card from '@/components/atoms/Card';
import Paragraph from '@/components/atoms/Paragraph';
import Spacer from '@/components/layout/Spacer';
import CenteredContent from '@/components/layout/CenteredContent';
import Image from 'next/image';
import phoneIcon from '@/assets/images/phone.png';
import clsx from 'clsx';
import { useForm } from 'react-hook-form';
import styles from './ValidaProcesoFirma.module.css';

function ValidaProcesoFirma() {
  const router = useRouter();
  const [value, onChange] = useState('');
  const [user] = useRecoilState(userState);
  const [showAgreement, setShowAgreement] = useState(false);
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      signAgreement: false,
    },
  });

  const digits = useDigitInput({
    acceptedCharacters: /^[\da-zA-Z]$/,
    length: 6,
    value,
    onChange,
  });

  const [state, setState] = useState({
    isLoaded: false,
    haveError: false,
    isLoading: false,
    haveDocsPending: false,
    isVerified: false,
    loadMessage: 'Estamos Firmando tu documento',
    errorMessage: {
      header: '',
      content: 'este mensaje es automático',
      icon,
    },
    isAuth: false,
  });

  // función para identificar documentos SINGLE
  const esDocumentoSingle = (doc) => {
    // Un documento es SINGLE si:

    // 1. Fue subido por el usuario actual SIN solicitar firmantes
    const esSoloMio = (
      doc.esSolicitud === 'NO'
      || doc.esSolicitud === false
      || doc.esSolicitud == null
      || doc.esSolicitud === undefined
    );

    // 2. O es tipo SINGLE específico
    const esTipoSingle = doc.tipoFirma === 'SINGLE';

    // 3. O es MULTIPLE pero con solo 1 firma requerida (yo mismo)
    const esMultiplePeroSolo = (
      doc.tipoFirma === 'MULTIPLE'
      && (doc.firmasRequeridas === 1 || doc.firmasRequeridas == null)
    );

    // 4. O soy el propietario y no hay otros firmantes involucrados
    const soyPropietarioSinOtros = (
      doc.propietario === user?.email
      && doc.tipoFirma !== 'OTHERS'
      && (doc.totalFirmas == null || doc.totalFirmas <= 1)
    );

    const esSingle = esSoloMio
      || esTipoSingle
      || esMultiplePeroSolo
      || soyPropietarioSinOtros;

    return esSingle;
  };

  // la función validarOrdenDeFirma
  const validarOrdenDeFirma = async () => {
    const documentos = loadItemStorage('docsToSign');

    if (!documentos || documentos.length === 0) {
      return true;
    }

    if (!user?.email || !user?.access_token) {
      return true;
    }

    try {
      // Filtrar documentos que requieren validación
      const documentosConOrden = documentos.filter((doc) => {
        const esSingle = esDocumentoSingle(doc);
        return !esSingle; // Solo validar los que NO son SINGLE
      });

      // Si no hay documentos que requieran validación, permitir continuar
      if (documentosConOrden.length === 0) {
        return true;
      }

      const ordenResults = await Promise.all(
        documentosConOrden.map(async (doc) => {
          const result = await validarOrdenFirma(
            doc.idArchivo,
            user.email,
            user.access_token,
          );
          return result;
        }),
      );

      const invalidOrden = ordenResults.find(
        (result) => result.status !== 200 || !result.data?.data?.puedeFirma,
      );

      if (invalidOrden) {
        // Redirigir a página de "no es tu turno" si no puede firmar
        await router.push('/no-es-tu-turno');
        return false;
      }

      return true;
    } catch (error) {
      return true;
    }
  };

  // Validación inicial al cargar el componente
  useEffect(() => {
    const validarOrdenInicial = async () => {
      // Esperar a que tengamos usuario y que se hayan cargado los documentos
      if (user?.email && user?.access_token) {
        const documentos = loadItemStorage('docsToSign');

        // Solo validar si hay documentos para firmar
        if (documentos && documentos.length > 0) {
          setState((prevState) => ({
            ...prevState,
            isLoading: true,
          }));

          const puedeAcceder = await validarOrdenDeFirma();

          setState((prevState) => ({
            ...prevState,
            isLoading: false,
          }));

          if (!puedeAcceder) {
            // La función validarOrdenDeFirma ya manejó la redirección
          } else {
            console.log('Validación exitosa - puede proceder');
          }
        } else {
          console.log('No hay documentos para validar');
        }
      } else {
        console.log('Faltan datos del usuario:', {
          email: user?.email,
          hasToken: !!user?.access_token,
        });
      }
    };

    validarOrdenInicial();
  }, [user?.email, user?.access_token]); // Agregar access_token como dependencia

  // Función para validar orden antes de procesar
  const validarOrdenAntesDeProceso = async () => {
    const documentos = loadItemStorage('docsToSign');
    if (!documentos || documentos.length === 0) {
      return true; // Si no hay documentos, permitir continuar
    }

    try {
      // Filtrar documentos que requieren validación
      const documentosConOrden = documentos.filter((doc) => {
        const requiereValidacion = (
          doc?.esSolicitud === 'SI'
          || doc?.tipoFirma === 'OTHERS'
          || (doc?.tipoFirma === 'MULTIPLE' && doc?.firmasRequeridas > 1)
        );
        return requiereValidacion;
      });

      // Si no hay documentos que requieran validación, permitir continuar
      if (documentosConOrden.length === 0) {
        return true;
      }

      const ordenResults = await Promise.all(
        documentosConOrden.map((doc) => validarOrdenFirma(
          doc.idArchivo,
          user?.email,
          user?.access_token,
        )),
      );

      const invalidOrden = ordenResults.find(
        (result) => result.status !== 200 || !result.data?.data?.puedeFirma,
      );

      if (invalidOrden) {
        setState((prevState) => ({
          ...prevState,
          haveError: true,
          errorMessage: {
            ...prevState.errorMessage,
            header: 'Turno de firma no disponible',
            content: 'No puedes procesar la firma en este momento porque aún no es tu turno. Debes esperar a que el firmante anterior complete su proceso de firma.',
          },
        }));
        return false;
      }
      return true;
    } catch (error) {
      return true;
    }
  };

  const onSubmit = async () => {
    // Validar orden antes de procesar
    const puedeProcesar = await validarOrdenAntesDeProceso();
    if (!puedeProcesar) {
      return; // No procesar si no es el turno
    }

    setState((prevState) => ({
      ...prevState,
      isLoading: true,
    }));

    verifiySignProcess(user.idUsuarioC, value, user?.access_token)
      .then(async (response) => {
        if (response.status === 200) {
          setState((prevState) => ({
            ...prevState,
            isLoading: true,
            isAuth: true,
          }));
          saveStorage('x-dta', response.data);
          await router.push(`/firmaExitosa${router?.query?.view === 'app' ? '?view=app' : ''}`);
        } else {
          const isOrderError = response.data?.mensaje?.includes('turno')
                              || response.data?.mensaje?.includes('orden')
                              || response.status === 500;

          setState((prevState) => ({
            ...prevState,
            isLoading: false,
            haveError: true,
            errorMessage: {
              ...prevState.errorMessage,
              header: isOrderError ? 'Turno de firma no disponible' : 'Validación de código único de verificación',
              content: isOrderError
                ? 'No puedes firmar en este momento porque aún no es tu turno. Debes esperar a que el firmante anterior complete su proceso de firma.'
                : response.data.mensaje,
            },
          }));
        }
      })
      .catch((error) => console.log(error));
  };

  const handleMessageClick = () => {
    setState((prevState) => ({
      ...prevState,
      haveError: false,
      isLoading: false,
    }));
  };

  const toggleShowAgreement = () => {
    setShowAgreement(!showAgreement);
  };

  return (
    <Container>
      <StateHandler handleErrorButton={handleMessageClick} state={state}>
        {showAgreement && (
          <SignAgreementModal
            onClose={toggleShowAgreement}
          />
        )}
        <Grid container>
          <Grid item lg={12}>
            <Card>
              <Paragraph size="lg" weight="semibold">
                Validación de proceso de firma
              </Paragraph>
              <Paragraph size="sm">
                Te llegará un mensaje de texto al celular registrado con un
                código único de verificación el cual debes digitar a
                continuación.
              </Paragraph>
              <Spacer.Vertical size="md" />
              <CenteredContent>
                <Image src={phoneIcon} height="150px" width="150px" alt="Teléfono" />
              </CenteredContent>

              <Spacer.Horizontal size="sm" />

              <form onSubmit={handleSubmit(onSubmit)}>
                <div
                  className={clsx('input-group text-center', styles['container-digits'])}
                >
                  <input
                    className={styles.digits}
                    autoFocus
                    {...digits[0]}
                  />
                  &nbsp;
                  <input
                    className={styles.digits}
                    {...digits[1]}
                  />
                  &nbsp;
                  <input
                    className={styles.digits}
                    {...digits[2]}
                  />
                  &nbsp;
                  <input
                    className={styles.digits}
                    {...digits[3]}
                  />
                  &nbsp;
                  <input
                    className={styles.digits}
                    {...digits[4]}
                  />
                  &nbsp;
                  <input
                    className={styles.digits}
                    {...digits[5]}
                  />
                </div>
                <Spacer size="xs" />
                <Check
                  size="sm"
                  hasError={Object.keys(errors).includes('signAgreement')}
                  helperText={errors.signAgreement && errors.signAgreement.message}
                  isCentered
                  label={(
                    <span>
                      Acepto el
                      {' '}
                      <a href="#" onClick={toggleShowAgreement}>
                        acuerdo de firma electrónica
                      </a>
                    </span>
                  )}
                  {
                  ...register('signAgreement', {
                    required: 'Debe aceptar el acuerdo de firma electrónica',
                  })
                    }
                />
                <Spacer.Vertical size="sm" />
                <div className="text-center">
                  <Button
                    buttonType="submit"
                    isInline
                  >
                    Confirmar
                  </Button>
                </div>
              </form>
            </Card>
          </Grid>
        </Grid>
      </StateHandler>
    </Container>
  );
}

export default ValidaProcesoFirma;

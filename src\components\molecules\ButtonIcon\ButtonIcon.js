import React from 'react';
import PropTypes from 'prop-types';

import Spacer from '@/components/layout/Spacer';
import Button from '@/components/atoms/Button';
import Icon from '@/components/atoms/Icon';

import { options as optionsBtn } from '@/components/atoms/Heading/constants';
import { options } from './constants';

export function ButtonIcon({
  children, type, icon, disabled, onClick, size,
}) {
  return (
    <Button
      type={type}
      isInline
      disabled={disabled}
      onClick={onClick}
      size={size}
      addons={{
        append: (
          <>
            <Spacer.Horizontal size="xs" />
            <Icon
              name={icon}
              color={type === 'primary' ? 'inverted' : 'primary'}
            />
          </>
        ),
      }}
    >
      {children}
    </Button>
  );
}

ButtonIcon.propTypes = {
  children: PropTypes.node,
  icon: PropTypes.oneOf(options.icons),
  type: PropTypes.oneOf(options.types),
  disabled: PropTypes.bool,
  onClick: PropTypes.func.isRequired,
  size: PropTypes.oneOf(optionsBtn.sizes),
};

ButtonIcon.defaultProps = {
  type: 'secondary',
  icon: 'arrowRight',
  disabled: false,
  size: 'xs',
};

export default ButtonIcon;

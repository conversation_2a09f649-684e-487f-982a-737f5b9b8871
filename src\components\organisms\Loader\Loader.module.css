


.sk-cube-grid {
  width: 40px;
  height: 40px;
  margin: 100px auto;
  --main-bg-color: #0F9454;
}

.sk-cube {
  width: 33%;
  height: 33%;
  background-color: var(--main-bg-color);
  float: left;
  -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
  animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
}

.sk-cube1 {
  composes: sk-cube;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.sk-cube2 {
  composes: sk-cube;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.sk-cube3 {
  composes: sk-cube;
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

.sk-cube4 {
  composes: sk-cube;
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.sk-cube5 {
  composes: sk-cube;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

.sk-cube6 {
  composes: sk-cube;
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

.sk-cube7 {
  composes: sk-cube;
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}

.sk-cube8 {
  composes: sk-cube;
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

.sk-cube9 {
  composes: sk-cube;
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

@-webkit-keyframes sk-cubeGridScaleDelay {
  0%, 70%, 100% {
    transform: scale3D(1, 1, 1);
  }
  35% {
    transform: scale3D(0, 0, 1);
  }
}

@keyframes sk-cubeGridScaleDelay {
  0%, 70%, 100% {
    transform: scale3D(1, 1, 1);
  }
  35% {
    transform: scale3D(0, 0, 1);
  }
}


.message{

  color:#2759D9;
}
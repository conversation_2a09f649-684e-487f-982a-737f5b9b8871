import React from 'react';

export const iconsMap = {
  arrowUp: {
    viewBox: '-1 -1 19 19',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.07 18.14C4.06891 18.14 0 14.0711 0 9.07C0 4.06891 4.06891 0 9.07 0C14.0711 0 18.14 4.06891 18.14 9.07C18.14 14.0711 14.0711 18.14 9.07 18.14ZM9.07 0.996468C4.61804 0.996468 0.996468 4.61804 0.996468 9.07C0.996468 13.522 4.61804 17.1435 9.07 17.1435C13.522 17.1435 17.1435 13.522 17.1435 9.07C17.1435 4.61804 13.522 0.996468 9.07 0.996468Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.539 9.95834L9.06994 5.4887L4.60084 9.95834L3.89581 9.25331L9.06994 4.07971L14.2441 9.25331L13.539 9.95834Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.5343 4.78418H9.60577V13.8917H8.5343V4.78418Z"
        />
      </>
    ),
  },
  arrowRight: {
    viewBox: '0 0 19 20',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M8.65792 19.316L7.34192 18L15.6849 9.658L7.34192 1.316L8.65792 0L18.3149 9.658L8.65792 19.316Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M0 8.65796H17V10.658H0V8.65796Z"
        />
      </>
    ),
  },
  angleDown: {
    viewBox: '0 0 15 9',
    svg: (
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M7.82633 8.70623L0.882568 1.54063L1.89405 0.496826L7.82633 6.61863L13.7586 0.496826L14.7701 1.54063L7.82633 8.70623Z"
      />
    ),
  },
  angleLeft: {
    viewBox: '-3 0 20 20',
    svg: (
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M9.707 19.414L0 9.707L9.707 0L11.121 1.414L2.828 9.707L11.121 18L9.707 19.414Z"
      />
    ),
  },
  angleUp: {
    viewBox: '0 0 9 6',
    svg: (
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.28864 5.121L4.46988 1.30224L0.651119 5.121L0 4.46988L4.46988 0L8.93976 4.46988L8.28864 5.121Z"
      />
    ),
  },
  pauseCircle: {
    viewBox: '0 0 28 28',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M27.86 13.93C27.86 21.6108 21.6108 27.86 13.93 27.86C6.24916 27.86 -2.73159e-07 21.6108 -6.08899e-07 13.93C-9.44639e-07 6.24917 6.24916 1.6893e-05 13.93 1.65572e-05C21.6108 1.62215e-05 27.86 6.24917 27.86 13.93ZM1.53041 13.93C1.53041 20.7675 7.09253 26.3296 13.93 26.3296C20.7674 26.3296 26.3296 20.7675 26.3296 13.93C26.3296 7.09254 20.7674 1.53042 13.93 1.53042C7.09252 1.53042 1.5304 7.09254 1.53041 13.93Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10 19L10 9L12 9L12 19L10 19Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M16 19L16 9L18 9L18 19L16 19Z"
        />
      </>
    ),
  },
  checkCircle: {
    viewBox: '5 1 20 20',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M14.93 20.86C9.45472 20.86 5 16.4053 5 10.93C5 5.45472 9.45472 1 14.93 1C20.4053 1 24.86 5.45472 24.86 10.93C24.86 16.4053 20.4053 20.86 14.93 20.86ZM14.93 2.09095C10.0559 2.09095 6.09095 6.05591 6.09095 10.93C6.09095 15.8041 10.0559 19.769 14.93 19.769C19.8041 19.769 23.769 15.8041 23.769 10.93C23.769 6.05591 19.8041 2.09095 14.93 2.09095Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.744 15.221L8.67871 10.1423L9.45059 9.37155L13.7458 13.6773L20.41 7.01251L21.1807 7.78439L13.744 15.221Z"
        />
      </>
    ),
  },
  plusCircle: {
    viewBox: '0 0 20 20',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10 20C4.48612 20 0 15.5139 0 10C0 4.48612 4.48612 0 10 0C15.5139 0 20 4.48612 20 10C20 15.5139 15.5139 20 10 20ZM10 1.09864C5.09155 1.09864 1.09864 5.09155 1.09864 10C1.09864 14.9084 5.09155 18.9014 10 18.9014C14.9084 18.9014 18.9014 14.9084 18.9014 10C18.9014 5.09155 14.9084 1.09864 10 1.09864Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.68396 9.4093H15.316V10.5906H4.68396V9.4093Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.40942 4.68396H10.5908V15.316H9.40942V4.68396Z"
        />
      </>
    ),
  },
  trash: {
    viewBox: '0 0 20 20',
    svg: <path
      fillRule="evenodd"
      d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
      clipRule="evenodd"
    />,
  },
  tag: {
    viewBox: '0 0 19 19',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M7.78343 18.086C7.23221 18.086 6.71447 17.8722 6.32617 17.4839L0.602738 11.7605C-0.200913 10.9568 -0.200913 9.6496 0.602738 8.84595L8.25802 1.19066C8.9258 0.522888 10.1879 0 11.132 0H16.026C17.1619 0 18.0866 0.924069 18.0866 2.06064V6.95467C18.0866 7.8987 17.5638 9.16084 16.896 9.82862L9.24069 17.4839C8.85239 17.8722 8.33465 18.086 7.78343 18.086ZM11.132 1.2879C10.5196 1.2879 9.60195 1.66783 9.16857 2.10121L1.51328 9.7565C1.21192 10.0579 1.21192 10.5486 1.51328 10.8499L7.23672 16.5734C7.52714 16.8625 8.03972 16.8625 8.33015 16.5734L15.9854 8.91807C16.4188 8.48469 16.7987 7.56706 16.7987 6.95467V2.06064C16.7987 1.63499 16.4517 1.2879 16.026 1.2879H11.132Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.9353 7.68236C11.5398 7.68236 10.4045 6.54708 10.4045 5.15164C10.4045 3.7562 11.5398 2.62091 12.9353 2.62091C14.3307 2.62091 15.466 3.7562 15.466 5.15164C15.466 6.54708 14.3307 7.68236 12.9353 7.68236ZM12.9353 3.81866C12.1999 3.81866 11.6023 4.41625 11.6023 5.15164C11.6023 5.88703 12.1999 6.48461 12.9353 6.48461C13.6707 6.48461 14.2682 5.88703 14.2682 5.15164C14.2682 4.41625 13.6707 3.81866 12.9353 3.81866Z"
        />
      </>
    ),
  },
  clock: {
    viewBox: '-1 0 21 20',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10 20C4.48612 20 0 15.5139 0 10C0 4.48612 4.48612 0 10 0C15.5139 0 20 4.48612 20 10C20 15.5139 15.5139 20 10 20ZM10 1.09864C5.09155 1.09864 1.09864 5.09155 1.09864 10C1.09864 14.9084 5.09155 18.9014 10 18.9014C14.9084 18.9014 18.9014 14.9084 18.9014 10C18.9014 5.09155 14.9084 1.09864 10 1.09864Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.1265 15.143L9.40942 11.4259V4.68402H10.5908V10.9368L13.9617 14.3078L13.1265 15.143Z"
        />
      </>
    ),
  },
  user: {
    viewBox: '0 0 14 15',
    svg: (
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M1.01693 15L0 14.5538C0.308723 13.8561 1.22056 13.4481 2.2775 12.975C3.29682 12.5187 4.56575 11.9509 4.56575 11.2777V10.3614C4.20448 10.0594 3.59241 9.41 3.49627 8.43355C3.20367 8.15831 2.71819 7.59711 2.71819 6.90245C2.71819 6.48066 2.88539 6.14048 3.03229 5.92422C2.94272 5.47204 2.77432 4.52538 2.77432 3.83073C2.77432 1.50369 4.39855 0 6.91192 0C7.63327 0 8.50928 0.193026 8.99714 0.714314C10.1365 0.914489 11.0501 2.26031 11.0501 3.83073C11.0501 4.83815 10.8728 5.66685 10.7659 6.07673C10.8847 6.2775 11.0131 6.57538 11.0131 6.92092C11.0131 7.70196 10.6178 8.20716 10.2488 8.47704C10.1443 9.43979 9.59429 10.0606 9.25869 10.3549V11.2777C9.25869 11.8502 10.2989 12.2351 11.3051 12.6074C12.4492 13.0304 13.6328 13.4683 14 14.4257L12.9616 14.8213C12.7753 14.3328 11.7888 13.9681 10.9187 13.6464C9.62056 13.1657 8.1486 12.6211 8.1486 11.2777V9.77997L8.40776 9.61732C8.43404 9.60005 9.15598 9.11629 9.15598 8.14997V7.77524L9.50471 7.63583C9.56323 7.60902 9.90241 7.43089 9.90241 6.92092C9.90241 6.76483 9.78298 6.59028 9.74118 6.54142L9.53875 6.30431L9.63191 6.00226C9.63489 5.99273 9.94003 5.0562 9.94003 3.83073C9.94003 2.78517 9.33931 1.79681 8.70335 1.79681H8.38447L8.22384 1.52216C8.10621 1.32139 7.607 1.10811 6.91192 1.10811C5.01659 1.10811 3.88441 2.12567 3.88441 3.83073C3.88441 4.63619 4.16746 5.93316 4.17044 5.94626L4.23613 6.24653L4.01638 6.461C4.01638 6.461 3.82828 6.65939 3.82828 6.90245C3.82828 7.20927 4.19731 7.59115 4.37586 7.72818L4.59202 7.89439L4.59382 8.16844C4.59382 9.08114 5.38324 9.59588 5.41727 9.61732L5.67285 9.78056L5.67584 11.2777C5.67584 12.6688 4.11192 13.3688 2.73133 13.986C2.06492 14.2839 1.15308 14.6926 1.01693 15Z"
      />
    ),
  },
  grip: {
    viewBox: '-2 0 14 15',
    svg: (
      <path
        d="M0 14V12H2V14H0ZM4 14V12H6V14H4ZM8 14V12H10V14H8ZM0 10V8H2V10H0ZM4 10V8H6V10H4ZM8 10V8H10V10H8ZM0 6V4H2V6H0ZM4 6V4H6V6H4ZM8 6V4H10V6H8ZM0 2V0H2V2H0ZM4 2V0H6V2H4ZM8 2V0H10V2H8Z"
      />
    ),
  },
  warning: {
    viewBox: '0 0 20 20',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10 20C4.48612 20 0 15.5139 0 10C0 4.48612 4.48612 0 10 0C15.5139 0 20 4.48612 20 10C20 15.5139 15.5139 20 10 20ZM10 1.09864C5.09155 1.09864 1.09864 5.09155 1.09864 10C1.09864 14.9084 5.09155 18.9014 10 18.9014C14.9084 18.9014 18.9014 14.9084 18.9014 10C18.9014 5.09155 14.9084 1.09864 10 1.09864Z"
        />
        <path
          d="M10.7837 11.8579H9.25293L9.08447 4.33594H10.9595L10.7837 11.8579ZM9.0332 14.1577C9.0332 13.8794 9.12354 13.6499 9.3042 13.4692C9.48486 13.2837 9.73145 13.1909 10.0439 13.1909C10.3564 13.1909 10.603 13.2837 10.7837 13.4692C10.9644 13.6499 11.0547 13.8794 11.0547 14.1577C11.0547 14.4263 10.9668 14.6509 10.791 14.8315C10.6152 15.0122 10.3662 15.1025 10.0439 15.1025C9.72168 15.1025 9.47266 15.0122 9.29688 14.8315C9.12109 14.6509 9.0332 14.4263 9.0332 14.1577Z"
        />
      </>
    ),
  },
  home: {
    viewBox: '-1 0 20 20',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M15.247 16.9411H1.69412C0.760093 16.9411 0 16.181 0 15.247V2.82351C0 1.88949 0.633599 1.12939 1.41176 1.12939H2.25882V2.25881H1.41176C1.31463 2.25881 1.12941 2.47904 1.12941 2.82351V15.247C1.12941 15.5582 1.38296 15.8117 1.69412 15.8117H15.247C15.5582 15.8117 15.8117 15.5582 15.8117 15.247V2.82351C15.8117 2.47904 15.6265 2.25881 15.5294 2.25881H14.6823V1.12939H15.5294C16.3076 1.12939 16.9412 1.88949 16.9412 2.82351V15.247C16.9412 16.181 16.1811 16.9411 15.247 16.9411Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.95289 3.38823C3.64117 3.38823 3.38818 3.13524 3.38818 2.82353V0.564705C3.38818 0.252988 3.64117 0 3.95289 0C4.26461 0 4.51759 0.252988 4.51759 0.564705V2.82353C4.51759 3.13524 4.26461 3.38823 3.95289 3.38823Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.9883 3.38823C12.6766 3.38823 12.4236 3.13524 12.4236 2.82353V0.564705C12.4236 0.252988 12.6766 0 12.9883 0C13.3 0 13.553 0.252988 13.553 0.564705V2.82353C13.553 3.13524 13.3 3.38823 12.9883 3.38823Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M5.64722 1.12939H11.2943V2.25881H5.64722V1.12939Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.12939 4.51758H15.8117V5.64699H1.12939V4.51758Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.553 6.77649H14.6824V7.9059H13.553V6.77649Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.2939 6.77649H12.4234V7.9059H11.2939V6.77649Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.0354 6.77649H10.1648V7.9059H9.0354V6.77649Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.77637 6.77649H7.90578V7.9059H6.77637V6.77649Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.51758 6.77649H5.64699V7.9059H4.51758V6.77649Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.553 9.0354H14.6824V10.1648H13.553V9.0354Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.2939 9.0354H12.4234V10.1648H11.2939V9.0354Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.0354 9.0354H10.1648V10.1648H9.0354V9.0354Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.77637 9.0354H7.90578V10.1648H6.77637V9.0354Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.51758 9.0354H5.64699V10.1648H4.51758V9.0354Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.25879 9.0354H3.3882V10.1648H2.25879V9.0354Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.553 11.2941H14.6824V12.4235H13.553V11.2941Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.2939 11.2941H12.4234V12.4235H11.2939V11.2941Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.0354 11.2941H10.1648V12.4235H9.0354V11.2941Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.77637 11.2941H7.90578V12.4235H6.77637V11.2941Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.51758 11.2941H5.64699V12.4235H4.51758V11.2941Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.25879 11.2941H3.3882V12.4235H2.25879V11.2941Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.2939 13.553H12.4234V14.6824H11.2939V13.553Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M9.0354 13.553H10.1648V14.6824H9.0354V13.553Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M6.77637 13.553H7.90578V14.6824H6.77637V13.553Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M4.51758 13.553H5.64699V14.6824H4.51758V13.553Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M2.25879 13.553H3.3882V14.6824H2.25879V13.553Z"
        />
      </>
    ),
  },
  tasks: {
    viewBox: '-1 -3 20 20',
    svg: (
      <>
        <circle cx="1" cy="11" r="1" />
        <circle cx="1" cy="6" r="1" />
        <circle cx="1" cy="1" r="1" />
        <line x1="5.5" y1="1.5" x2="18.5" y2="1.5" strokeLinecap="round" />
        <line x1="5.5" y1="6.5" x2="18.5" y2="6.5" strokeLinecap="round" />
        <line x1="5.5" y1="11.5" x2="18.5" y2="11.5" strokeLinecap="round" />
      </>
    ),
  },
  reports: {
    viewBox: '0 0 20 20',
    svg: (
      <>
        <rect
          className="transparent"
          x="2.5"
          y="8.5"
          width="3"
          height="7"
          rx="0.5"
        />
        <rect
          className="transparent"
          x="8.5"
          y="4.5"
          width="3"
          height="11"
          rx="0.5"
        />
        <rect
          className="transparent"
          x="14.5"
          y="0.5"
          width="3"
          height="15"
          rx="0.5"
        />
        <line y1="15.5" x2="21" y2="15.5" />
      </>
    ),
  },
  settings: {
    viewBox: '0 0 24 24',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M11.9301 17.5578C8.82689 17.5578 6.30225 15.0331 6.30225 11.93C6.30225 8.82677 8.82689 6.30212 11.9301 6.30212C15.0333 6.30212 17.5579 8.82677 17.5579 11.93C17.5579 15.0331 15.0333 17.5578 11.9301 17.5578ZM11.9301 7.47433C9.4735 7.47433 7.47445 9.47337 7.47445 11.93C7.47445 14.3865 9.4735 16.3856 11.9301 16.3856C14.3867 16.3856 16.3857 14.3865 16.3857 11.93C16.3857 9.47337 14.3867 7.47433 11.9301 7.47433Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M13.6127 23.86H10.2473L9.27616 20.9484C8.63397 20.76 8.0151 20.5035 7.42836 20.1833L4.6844 21.5553L2.30407 19.1756L3.67668 16.431C3.35653 15.8455 3.10067 15.2267 2.9116 14.5832L0 13.6127V10.2473L2.9116 9.27679C3.10067 8.63334 3.35653 8.01446 3.67668 7.42899L2.30407 4.6844L4.6844 2.30407L7.42836 3.67668C8.0151 3.35653 8.63397 3.10004 9.27616 2.9116L10.2473 0H13.6127L14.5838 2.9116C15.2254 3.10004 15.8449 3.35653 16.4316 3.67668L19.1756 2.30407L21.5559 4.6844L20.1833 7.42899C20.5035 8.01446 20.7593 8.63334 20.9484 9.27679L23.86 10.2473V13.6127L20.9484 14.5832C20.7593 15.2267 20.5035 15.8455 20.1833 16.431L21.5559 19.1756L19.1756 21.5553L16.4316 20.1833C15.8449 20.5035 15.2254 20.76 14.5838 20.9484L13.6127 23.86ZM11.0918 22.6878H12.7682L13.6719 19.9779L13.9807 19.8985C14.7313 19.7056 15.4498 19.4088 16.1153 19.0149L16.39 18.8523L18.9443 20.1297L20.1297 18.9443L18.8517 16.39L19.0149 16.1146C19.4075 15.451 19.705 14.7332 19.8991 13.9801L19.9779 13.6713L22.6878 12.7682V11.0918L19.9779 10.1887L19.8991 9.87991C19.705 9.1268 19.4075 8.40898 19.0149 7.74536L18.8517 7.46996L20.1297 4.91569L18.9443 3.73025L16.39 5.0077L16.1153 4.84511C15.4498 4.45122 14.7313 4.15439 13.9807 3.96154L13.6719 3.88213L12.7682 1.1722H11.0918L10.1881 3.88213L9.87928 3.96154C9.12869 4.15439 8.41024 4.45122 7.74473 4.84511L7.46996 5.0077L4.91569 3.73025L3.73025 4.91569L5.00833 7.46996L4.84511 7.74536C4.45248 8.40898 4.15502 9.1268 3.96091 9.87991L3.8815 10.1887L1.1722 11.0918V12.7682L3.8815 13.6713L3.96091 13.9801C4.15502 14.7332 4.45248 15.451 4.84511 16.1146L5.00833 16.39L3.73025 18.9443L4.91569 20.1297L7.46996 18.8523L7.74473 19.0149C8.41024 19.4088 9.12869 19.7056 9.87928 19.8985L10.1881 19.9779L11.0918 22.6878Z"
        />
      </>
    ),
  },
  play: {
    viewBox: '0 0 34 34',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M17.0001 33.9298C7.66507 33.9298 0.0700684 26.3348 0.0700684 16.9998C0.0700684 7.66482 7.66507 0.0698242 17.0001 0.0698242C26.3351 0.0698242 33.9301 7.66482 33.9301 16.9998C33.9301 26.3348 26.3351 33.9298 17.0001 33.9298ZM17.0001 1.92982C8.69007 1.92982 1.93007 8.68982 1.93007 16.9998C1.93007 25.3098 8.69007 32.0698 17.0001 32.0698C25.3101 32.0698 32.0701 25.3098 32.0701 16.9998C32.0701 8.68982 25.3101 1.92982 17.0001 1.92982Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.0001 25.7408V8.25879L26.9851 16.9998L12.0001 25.7408ZM14.0001 11.7408V22.2588L23.0151 16.9998L14.0001 11.7408Z"
        />
      </>
    ),
  },
  cross: {
    viewBox: '0 0 28 28',
    svg: (
      <>
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M1.65702 27.6578L0.343018 26.3418L26.343 0.341797L27.657 1.6578L1.65702 27.6578Z"
        />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M26.343 27.6578L0.343018 1.6578L1.65702 0.341797L27.657 26.3418L26.343 27.6578Z"
        />
      </>
    ),
  },
  userAdd: {
    viewBox: '0 0 20 20',
    svg: <path
      d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"
    />,
  },
  logIn: {
    fill: 'none',
    viewBox: '0 0 24 24',
    stroke: 'currentColor',
    strokeWidth: '2',
    svg: <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
    />,
  },
  logOut: {
    fill: 'none',
    viewBox: '0 0 24 24',
    stroke: 'currentColor',
    strokeWidth: '2',
    svg: <path
      strokeLinecap="round"
      strokeLinejoin="round"
      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
    />,
  },
  checkEmpty: {
    viewBox: '0 0 29 28',
    fill: 'none',
    svg: <rect
      x="0.640784"
      y="0.298802"
      width="23.001"
      height="23.0074"
      rx="4.5"
      transform="matrix(0.939654 -0.342126 0.341914 0.939731 -1.0635 7.06872)"
      fill="#33CC99"
      fillOpacity="0.44"
      stroke="#336666"
    />,
  },
  check: {
    viewBox: '0 0 29 33',
    fill: 'none',
    svg: (
      <>
        <rect
          x="0.640784"
          y="0.298802"
          width="23.001"
          height="23.0074"
          rx="4.5"
          transform="matrix(0.939654 -0.342126 0.341914 0.939731 -1.0635 12.0687)"
          fill="#33CC99"
          fillOpacity="0.44"
          stroke="#336666"
        />
        <path d="M9.5 18L7 20.5L13 28.5L18 16.5L21.5 7L18.5 6.5L15 16L12.5 22.5L9.5 18Z" fill="#33CC99" />
        <path d="M22.5 5.5L19 5L20 1L24.5 1.5L22.5 5.5Z" fill="#33CC99" />
        <path d="M9.5 18L7 20.5L13 28.5L18 16.5L21.5 7L18.5 6.5L15 16L12.5 22.5L9.5 18Z" stroke="black" />
        <path d="M22.5 5.5L19 5L20 1L24.5 1.5L22.5 5.5Z" stroke="black" />
      </>
    ),
  },
  pen: {
    viewBox: '0 0 20 20',
    svg: <path
      d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"
    />,
  },
  multipleAccount: {
    viewBox: '0 0 24 24',
    svg: <path
      fill="currentColor"
      d="M19 17V19H7V17S7 13 13 13 19 17 19 17M16 8A3 3 0 1 0 13 11A3 3 0 0 0 16 8M19.2 13.06A5.6 5.6 0 0 1 21 17V19H24V17S24 13.55 19.2 13.06M18 5A2.91 2.91 0 0 0 17.11 5.14A5 5 0 0 1 17.11 10.86A2.91 2.91 0 0 0 18 11A3 3 0 0 0 18 5M8 10H5V7H3V10H0V12H3V15H5V12H8Z"
    />,
  },
  refresh: {
    viewBox: '0 0 20 20',
    svg: <path
      fillRule="evenodd"
      d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
      clipRule="evenodd"
    />,
  },
  // uploadDocument: {
  //   viewBox: '0 0 107 125',
  //   svg: <path
  //     fillRule="evenodd"
  //     clipRule="evenodd"
  /*      d="M98.1217 43.0497H84.1291V28.5494L98.1217 43.0497ZM84.1291
  28.5494V43.0497H98.1217L84.1291 28.5494ZM37.8917 99.0339H67.8798V106.538H37.8917V99.0339ZM37.8917
  86.5365H87.8671V94.0406H37.8917V86.5365ZM37.8917 74.0392H80.3771V81.5432H37.8917V74.0392ZM37.8917
  61.5418H92.8745V69.0459H37.8917V61.5418ZM79.1217 25.4462H59.4871H58.8805L63.3378 35.7149C65.5383
  40.8069 63.1827 46.7876 58.0906 48.988L29.894 61.2174V61.6406V117.653H100.872V48.043H81.6184H79.
  1217V45.5463V25.4462ZM27.9616
  47.507V34.3326H14.632V25.2487H27.9616V12.0602H36.8197V25.2487H50.1493V34.3326H36.8197V47.507H27.
  9616ZM48.7529 6.0654L53.5205 17.0535L61.349 35.0943C63.5494 40.1863 61.1938 46.1529 56.1018
  48.3674L27.087 60.9635C21.995 63.1639 16.0143 60.8083 13.7998 55.7163L1.21778 26.7016C-0.996765
  21.6095 1.35883 15.6288 6.45087 13.4143L35.4797 0.83231C40.5718 -1.38223 46.5383 0.973365 48.7529
  6.0654ZM61.349 35.0943C63.5494 40.1863 61.1938 46.1529 56.1018 48.3674L27.087 60.9635C24.8161
  61.9509 22.3617 62.0214 20.1472 61.3444V61.8945V120.911C20.1472 121.997 20.528 122.914 21.2756
  123.662C22.0232 124.409 22.94 124.762 24.0262 124.762H102.734C103.82 124.762 104.737 124.409
  105.485 123.662C106.232 122.914 106.613 121.997 106.613 120.911V43.6844C106.613 42.5983 106.275
  41.6815 105.612 40.9339L83.6355 18.3089C82.8456 17.5049 81.971 17.0676 80.8849
  17.0676H54.0283H53.5205L61.349 35.0943Z" */
  //     fill="#33CC99"
  //   />,
  // },
  uploadDocument: {
    viewBox: '0 0 48 48',
    svg: (
      <>
        <polygon points="25.89 17.42 24.01 13.08 24 13.08 25.89 17.42" />
        <polygon
          points="19.98 20.41 19.98 17.24 23.19 17.24 23.19 15.05 19.98 15.05 19.98 11.87 17.84 11.87 17.84 15.05 14.63 15.05 14.63 17.24 17.84 17.24 17.84 20.41 19.98 20.41"
          fill="#fff"
        />
        <path
          d="M47.36,27.27,37.57,4.72A7.84,7.84,0,0,0,27.27.65L4.72,10.43A7.85,7.85,0,0,0,.65,20.74l9.78,22.54a7.86,7.86,0,0,0,10.31,4.08l22.54-9.79A7.85,7.85,0,0,0,47.36,27.27ZM36.8,38.11a.92.92,0,0,1-.93.93h-19a.92.92,0,0,1-.94-.93V23.75l.14,0a2.42,2.42,0,0,1-1.67-1.39l-3-7a2.43,2.43,0,0,1,1.26-3.2l7-3a2.45,2.45,0,0,1,3.2,1.27L24,13.07H30.6a.87.87,0,0,1,.66.3l5.3,5.45a1,1,0,0,1,.24.66Z"
        />
        <path
          d="M30.18,20.54V15.1H25.3l1.07,2.47a2.45,2.45,0,0,1-1.26,3.2l-6.8,2.95v13.6H35.42V20.54H30.18Zm-9.94,6.27H30.48v1.81H20.24Zm7.23,7.83H20.24V32.83h7.23Zm4.81-3h-12V29.82h12Zm1.21-6H20.24V23.8H33.49Z"
        />
      </>
    ),
  },
  download: {
    viewBox: '0 0 15 15',
    svg:
  <path
    fillRule="evenodd"
    clipRule="evenodd"
    d="M11.4446 1C11.4446 0.447715 10.9968 0 10.4446 0H3.90868C3.3564 0 2.90868 0.447715 2.90868 1V2.75C2.90868 3.30228 2.46097 3.75 1.90868 3.75H1.00178C0.185833 3.75 -0.286659 4.67451 0.19132 5.3358L6.36616 13.8787C6.76534 14.431 7.5879 14.431 7.98708 13.8787L14.1619 5.3358C14.6399 4.67451 14.1674 3.75 13.3515 3.75H12.4446C11.8923 3.75 11.4446 3.30228 11.4446 2.75V1Z"
  />,
  },
  share: {
    viewBox: '0 0 20 20',
    svg:
  <path
    d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z"
  />,
  },
  verifyDocument: {
    viewBox: '0 0 48 48',
    svg:
            (
              <>
                <path
                  d="M47.36,27.27,37.57,4.72A7.84,7.84,0,0,0,27.27.65L4.72,10.43A7.85,7.85,0,0,0,.65,20.74l9.78,22.54a7.86,7.86,0,0,0,10.31,4.08l22.54-9.79A7.85,7.85,0,0,0,47.36,27.27ZM12.2,18.76c-.08.6-.4.84-.87.73L8.23,17c.43-.45.86-.89,1.29-1.36L10.88,17a55.88,55.88,0,0,1,2.49-6.07c.05.49,1.15.76,1.63.58A17.27,17.27,0,0,0,12.2,18.76Zm3.26-7.91c-.35.44-2.09,0-1.75-.62A10.28,10.28,0,0,1,14.35,9a1.43,1.43,0,0,1,1.05-.6l2.19.5A11.31,11.31,0,0,0,15.46,10.85Zm19.94,28H16.26V15.19h14.5L35.4,21Z"
                  fillRule="evenodd"
                />
                <path
                  d="M30,16.24H17.33V38.12h17V21.93H30Zm-10.21,11h10v1.5h-10Zm6.88,7.44H19.79v-1.5h6.88Zm3.88-3H19.79v-1.5H30.55Zm2-7.44v1.5H19.79v-1.5Z"
                  fillRule="evenodd"
                />
              </>
            ),
  },
  userCircle: {
    viewBox: '0 0 20 20',
    svg:
  <path
    fillRule="evenodd"
    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z"
    clipRule="evenodd"
  />,
  },
  globe: {
    viewBox: '0 0 20 20',
    svg:
  <path
    fillRule="evenodd"
    d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z"
    clipRule="evenodd"
  />,
  },
  calendar: {
    viewBox: '0 0 20 20',
    svg:
  <path
    fillRule="evenodd"
    d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
    clipRule="evenodd"
  />,
  },
  documentDownload: {
    viewBox: '0 0 20 20',
    svg: <path fillRule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v3.586l-1.293-1.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V8z" clipRule="evenodd" />,
  },
  email: {
    viewBox: '0 0 20 20',
    svg: (
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5.404 14.596A6.5 6.5 0 1116.5 10a1.25 1.25 0 01-2.5 0 4 4 0 10-.571 2.06A2.75 2.75 0 0018 10a8 8 0 10-2.343 5.657.75.75 0 00-1.06-1.06 6.5 6.5 0 01-9.193 0zM10 7.5a2.5 2.5 0 100 5 2.5 2.5 0 000-5z"
      />
    ),
  },
  envelope: {
    viewBox: '0 0 20 20',
    svg: (
      <>
        <path
          fill="base"
          d="M3 4a2 2 0 00-2 2v1.161l8.441 4.221a1.25 1.25 0 001.118 0L19 7.162V6a2 2 0 00-2-2H3z"
        />
        <path
          fill="base"
          d="M19 8.839l-7.77 3.885a2.75 2.75 0 01-2.46 0L1 8.839V14a2 2 0 002 2h14a2 2 0 002-2V8.839z"
        />
      </>
    ),
  },
  information: {
    viewBox: '0 0 480 480',
    svg: (
      <path
        fill="shinyShamrock"
        d="M239.15 0c31.9 0 57.7 25.8 57.7 57.7s-25.8 57.7-57.7 57.7s-57.7-25.8-57.7-57.7S207.25 0 239.15 0z M291.65 151.6h-1.5 h-92.8h-3.4c-19 0-34.3 15.4-34.3 34.3l0 0c0 19 15.4 34.3 34.3 34.3h3.4v200h-37.7v68.7h169.6v-68.7h-37.5V151.6H291.65z"
      />
    ),
  },
  phone: {
    viewBox: '0 0 20 20',
    svg: (
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M4 4a3 3 0 013-3h6a3 3 0 013 3v12a3 3 0 01-3 3H7a3 3 0 01-3-3V4zm4-1.5v.75c0 .414.336.75.75.75h2.5a.75.75 0 00.75-.75V2.5h1A1.5 1.5 0 0114.5 4v12a1.5 1.5 0 01-1.5 1.5H7A1.5 1.5 0 015.5 16V4A1.5 1.5 0 017 2.5h1z"
      />
    ),
  },
  whatsapp: {
    viewBox: '0 0 24 24',
    svg: (
      <path
        d="M12.04 2C6.58 2 2.13 6.45 2.13 11.91C2.13 13.66 2.59 15.36 3.45 16.86L2.05 22L7.3 20.62C8.75 21.41 10.38 21.83 12.04 21.83C17.5 21.83 21.95 17.38 21.95 11.92C21.95 9.27 20.92 6.78 19.05 4.91C17.18 3.03 14.69 2 12.04 2M12.05 3.67C14.25 3.67 16.31 4.53 17.87 6.09C19.42 7.65 20.28 9.72 20.28 11.92C20.28 16.46 16.58 20.15 12.04 20.15C10.56 20.15 9.11 19.76 7.85 19L7.55 18.83L4.43 19.65L5.26 16.61L5.06 16.29C4.24 15 3.8 13.47 3.8 11.91C3.81 7.37 7.5 3.67 12.05 3.67M8.53 7.33C8.37 7.33 8.1 7.39 7.87 7.64C7.65 7.89 7 8.5 7 9.71C7 10.93 7.89 12.1 8 12.27C8.14 12.44 9.76 14.94 12.25 16C12.84 16.27 13.3 16.42 13.66 16.53C14.25 16.72 14.79 16.69 15.22 16.63C15.7 16.56 16.68 16.03 16.89 15.45C17.1 14.87 17.1 14.38 17.04 14.27C16.97 14.17 16.81 14.11 16.56 14C16.31 13.86 15.09 13.26 14.87 13.18C14.64 13.1 14.5 13.06 14.31 13.3C14.15 13.55 13.67 14.11 13.53 14.27C13.38 14.44 13.24 14.46 13 14.34C12.74 14.21 11.94 13.95 11 13.11C10.26 12.45 9.77 11.64 9.62 11.39C9.5 11.15 9.61 11 9.73 10.89C9.84 10.78 10 10.6 10.1 10.45C10.23 10.31 10.27 10.2 10.35 10.04C10.43 9.87 10.39 9.73 10.33 9.61C10.27 9.5 9.77 8.26 9.56 7.77C9.36 7.29 9.16 7.35 9 7.34C8.86 7.34 8.7 7.33 8.53 7.33Z"
      />
    ),
  },
};
export const options = {
  sizes: ['xs', 'sm', 'md', 'lg', 'xl', '2xl'],
  names: Object.keys(iconsMap),
  colors: ['base', 'highlight', 'muted', 'primary', 'inverted', 'danger', 'secondary', 'shinyShamrock'],
  backgrounds: [
    'transparent',
    'base',
    'highlight',
    'inverted',
    'muted',
    'spotlight',
    'brand',
    'danger',
    'shinyShamrock',
  ],
  border: ['primary', 'secondary', 'danger', 'base', 'none', 'shinyShamrock'],
};

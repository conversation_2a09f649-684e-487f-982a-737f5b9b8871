import React, { useMemo } from 'react';
import dynamic from 'next/dynamic';
import PropTypes from 'prop-types';

const Message = dynamic(() => import('@/components/organisms/Message'));
const Loader = dynamic(() => import('@/components/organisms/Loader'));

function StateHandler({ children, handleErrorButton, state }) {
  const component = useMemo(() => (state.isLoading
    ? <Loader message={state.loadMessage} />
    : children
  ), [children, state.isLoading, state.loadMessage]);

  return state.haveError
    ? <Message handleClick={handleErrorButton} errorMessage={state.errorMessage} />
    : component;
}

StateHandler.defaultProps = {
  handleErrorButton: () => null,
};

StateHandler.propTypes = {
  children: PropTypes.node.isRequired,
  handleErrorButton: PropTypes.func,
  state: PropTypes.shape({
    haveError: PropTypes.bool,
    isLoading: PropTypes.bool,
    errorMessage: PropTypes.shape({
      icon: PropTypes.oneOfType([
        PropTypes.element,
        PropTypes.node,
        PropTypes.shape({
          src: PropTypes.string,
          height: PropTypes.number,
          width: PropTypes.number,
          blurDataURL: PropTypes.string,
        })]),
      title: PropTypes.string,
      header: PropTypes.string,
      content: PropTypes.string,
    }),
    loadMessage: PropTypes.string,
  }).isRequired,
};

export default StateHandler;

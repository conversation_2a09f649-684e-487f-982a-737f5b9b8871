import React from 'react';
import PropTypes from 'prop-types';
import withStyles from '@/hocs/withStyles';
import styles from './Tooltip.module.css';
import { options } from './constants';

export function Tooltip({
  getStyles,
  title,
  children,
}) {
  return (
    <div className={getStyles('tooltip')}>
      {children}
      <span className={getStyles('tooltip-title', ['position'])}>{title}</span>
    </div>
  );
}

Tooltip.propTypes = {
  title: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  getStyles: PropTypes.func,
  position: PropTypes.oneOf(options.positions),
};

Tooltip.defaultProps = {
  getStyles: () => ({}),
  position: 'bottom',
};

export default withStyles(styles)(Tooltip);

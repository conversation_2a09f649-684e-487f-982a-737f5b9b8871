import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import styles from './Card.module.css';
import { options } from './constants';

export const handleClick = ({ onClick }) => (event) => {
  onClick(event);
};

export function Card({
  getStyles,
  onClick,
  isClickable,
  isDraggable,
  children,
  isInline,
  style,
}) {
  return (
    <div
      onClick={onClick ? handleClick({ onClick }) : undefined}
      className={getStyles('card', ['color', 'size', 'border'], {
        'is-clickable': isClickable,
        'is-draggable': isDraggable,
        'is-inline': isInline,
      })}
      style={style}
    >
      {children}
    </div>
  );
}

Card.propTypes = {
  children: PropTypes.node.isRequired,
  getStyles: PropTypes.func.isRequired,
  onClick: PropTypes.func,
  color: PropTypes.oneOf(options.colors),
  size: PropTypes.oneOf(options.sizes),
  isClickable: PropTypes.bool,
  isDraggable: PropTypes.bool,
  border: PropTypes.oneOf(options.colors),
  isInline: PropTypes.bool,
  // eslint-disable-next-line react/forbid-prop-types
  style: PropTypes.object,
};

Card.defaultProps = {
  color: 'base',
  size: 'sm',
  getStyles: () => ({}),
  border: 'base',
  isInline: false,
  style: {},
};

export default withStyles(styles)(Card);

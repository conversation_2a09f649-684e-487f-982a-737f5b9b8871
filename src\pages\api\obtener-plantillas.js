import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function obtenerPlantillasRoute(req, res) {
  try {
    const { user } = req.session;
    if (!user?.access_token) {
      return res.status(401).send();
    }

    const { data } = await fetchApi.post(
      '/firma/manager/plantillas/v1/listar',
      undefined,
      {
        headers: {
          Authorization: `Bearer ${user.access_token}`,
          ...getDefaultHeaders(req),
        },
      },
    );

    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(obtenerPlantillasRoute);

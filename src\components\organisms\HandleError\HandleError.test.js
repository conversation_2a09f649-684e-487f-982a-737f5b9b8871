import React from 'react';
import { render } from '@testing-library/react';
import HandlerError from './HandleError';

function Bomb() {
  return this['💣'].caboom();
}

describe('Tests HandlerError', () => {
  it('should catch error', () => {
    const component = (
      <HandlerError>
        <Bomb />
      </HandlerError>
    );
    const { getByText } = render(component);
    expect(getByText(/Oops!/i)).toBeInTheDocument();
    expect(getByText(/Algo ha salido mal/i)).toBeInTheDocument();
    expect(getByText(/Volver al inicio/i)).toBeInTheDocument();
  });

  it('should render children if has no error', () => {
    const component = (
      <HandlerError>
        <p>Hello world</p>
      </HandlerError>
    );
    const { getByText } = render(component);
    expect(getByText(/Hello world/i)).toBeInTheDocument();
  });
});

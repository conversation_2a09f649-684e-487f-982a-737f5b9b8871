![Logo](/src/assets/images/logo_principal.png)

# Proyecto

> ⚠️ **Nota:** Para ejecutar correctamente los comandos, asegúrate de usar **Git Bash** (especialmente en Windows).

---

## Pasos Iniciales

### `npm install`

Instala todas las dependencias necesarias del proyecto.  
Este paso debe realizarse **antes de cualquier otro comando**.

---

## Scripts Disponibles

En el directorio del proyecto, puedes ejecutar:

## Available Scripts

In the project directory, you can run:



### `npm start:watch`

Runs the app in the development mode.<br />
Open [http://localhost:3000](http://localhost:3000) to view it in the browser.

The page will reload if you make edits.<br />
You will also see any lint errors in the console.

### `npm test`

Launches the test runner

### `npm run build`

Builds the app for production

### `npm run build:dev`

Builds the app for development

### `npm run start`

Starts the app. The app should be compiled with `npm run build` or `npm run build:dev` first

### `npm run lint`

Run eslint on the project

### `npm run build:tokens`

Generates the tokens.css file based on design tokens in `src/tokens/index.js` file

## OS Requirements

- Node.js
- NPM
- ImageMagick
- Ghostscript

## Troubleshooting
### Ghostscript
    convert-im6.q16: attempt to perform an operation not allowed by the security policy `PDF' @ error/module.c/OpenModule/1285.
***Solution:***
Edit `/etc/ImageMagick-6/policy.xml` and replace the line:

    <policy domain="coder" rights="none" pattern="PDF" />
by:

    <policy domain="coder" rights="read|write" pattern="PDF" />
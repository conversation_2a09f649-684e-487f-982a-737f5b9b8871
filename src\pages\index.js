import React, { useEffect } from 'react';
import ReactGA from 'react-ga';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import withAuthentication from '@/lib/withAuthentication';
import StateHandler from '@/components/organisms/StateHandler';
import DocsPendingToSignHome from '@/components/organisms/DocsPendingToSignHome';

export const getServerSideProps = withAuthentication;

export default function HomePage({ user: cookieUser }) {
  ReactGA.pageview('/');
  const [user, setUser] = useRecoilState(userState);

  useEffect(() => {
    setUser({
      ...cookieUser,
    });
  }, [cookieUser]);

  return (
    <StateHandler state={{ isLoading: !user?.email }}>
      <DocsPendingToSignHome />
    </StateHandler>
  );
}

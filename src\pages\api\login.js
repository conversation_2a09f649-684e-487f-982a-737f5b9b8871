import { stringify } from 'query-string';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import catchApiError from '@/utils/catchApiError';
import { getServicioUsuario } from '@/services/user';

async function loginRoute(req, res) {
  try {
    const response = await fetchApi.post(
      '/security/oauth/token',
      stringify(req.body),
      {
        auth: {
          username: 'firmese',
          password: 'VnKfirm3Se',
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          ...getDefaultHeaders(req),
        },
      },
    );

    const { data } = response;

    data.idUsuarioC = +data.idUsuarioC;

    const { data: { data: serviceInfo } } = await getServicioUsuario(data.access_token);

    // Usar el expires_in del JWT response
    const expiresIn = data.expires_in; // Este viene del JWT

    // Guardar usuario en sesión CON toda la información del JWT
    req.session.user = {
      ...data, // Incluye access_token, refresh_token, expires_in.
      enableOris: serviceInfo.orisHabilitado,
    };

    await req.session.save();

    console.log('Login exitoso, usuario guardado con expires_in:', expiresIn);

    // Retornar toda la información del JWT al cliente
    return res.send({
      ...data,
      enableOris: serviceInfo.orisHabilitado,
    });
  } catch (error) {
    console.error('Error en login:', error);
    return catchApiError(error, res);
  }
}

export default withSessionRoute(loginRoute);

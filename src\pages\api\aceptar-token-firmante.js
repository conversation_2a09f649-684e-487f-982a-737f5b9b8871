import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function aceptarTokenFirmanteRoute(req, res) {
  try {
    const { code } = req.body;

    const { data } = await fetchApi.post(
      '/token/registro/aceptar-token-firmante',
      code,
      {
        headers: {
          'Content-Type': 'text/plain',
          ...getDefaultHeaders(req),
        },
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(aceptarTokenFirmanteRoute);

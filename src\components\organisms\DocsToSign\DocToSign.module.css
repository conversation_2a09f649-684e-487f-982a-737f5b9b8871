.grid-card {
    display: grid;
    gap: var(--doc-card-gap);
    grid-auto-rows: var(--doc-card-height);
    grid-template-columns: repeat(auto-fill, minmax(min(100%, var(--doc-card-min-width)), 1fr));
    margin-bottom: var(--doc-card-margin-bottom);
}

.card-actions {
    display: flex;
    justify-content: space-between;
    pointer-events: none;
}

.sign-type {
    text-align: right;
}

.preview {
    height: 50%;
    width: 100%;
    object-fit: cover;
    object-position: top left;
    background: var(--color-base-white);
}

.table{
    text-align: center !important;
    border: solid 2px var(--color-primary);
}

.containerTable{
    overflow-x: auto;
    margin-right: auto;
    margin-left: auto;
    margin-bottom: 10px;
}

.tableHead{
    text-align: center !important;
    color: var(--color-shiny-shamrock);
}

.itemTable{
    margin-bottom: 5px;
    margin-top: 5px;
}

.txtCenter{
    text-align: center !important;
}
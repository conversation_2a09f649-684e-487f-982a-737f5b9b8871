import React from 'react';
import {
  render, fireEvent, waitFor, screen,
} from '@testing-library/react';
import * as Recoil from 'recoil';
import * as Router from 'next/router';
import * as utils from '@/utils/utils';
import * as userService from '@/services/user';
import ValidaProcesoFirma from './ValidaProcesoFirma';

jest.mock('@/components/molecules/SignAgreementModal', () => ({
  __esModule: true,
  default: jest.fn(() => <span>SignAgreementModal mock</span>),
}));

function fillForm(chars, checkLength = false) {
  const digitInputs = screen.getAllByRole('textbox');
  if (checkLength) {
    expect(digitInputs.length).toBe(6);
  }

  digitInputs.forEach((input, index) => {
    fireEvent.change(input, { target: { value: chars[index] } });
  });

  const signAgreement = screen.getByRole('checkbox');
  signAgreement.click();
}

describe('Tests ValidaProcesoFirma', () => {
  const userMock = {
    idUsuarioC: '<EMAIL>',
    access_token: 'access_token',
  };
  jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);

  beforeEach(() => {
    render(<Recoil.RecoilRoot><ValidaProcesoFirma /></Recoil.RecoilRoot>);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show SignAgreementModal when click on "acuerdo de firma electrónica"', async () => {
    const modalTrigger = screen.getByText(/acuerdo de firma electrónica/i);

    expect(screen.queryAllByText(/SignAgreementModal mock/i)).toEqual([]);

    modalTrigger.click();

    expect(await screen.findByText(/SignAgreementModal mock/i)).toBeInTheDocument();
  });

  it('should show error if SignAgreement is not accepted', async () => {
    expect(screen.queryAllByText(/Debe aceptar el acuerdo de firma electrónica/i)).toEqual([]);

    const button = screen.getByRole('button', { name: /Confirmar/i });
    button.click();

    expect(await screen.findByText(/Debe aceptar el acuerdo de firma electrónica/i)).toBeInTheDocument();
  });

  it('should redirect to /firmaExitosa if verifySignProcess response status is 200', async () => {
    const verifySignProcessResponseMock = {
      status: 'success',
    };
    const verifySignProcessMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: verifySignProcessResponseMock,
    }));
    jest.spyOn(userService, 'verifiySignProcess').mockImplementation(verifySignProcessMock);

    const saveStorageMock = jest.fn();
    jest.spyOn(utils, 'saveStorage').mockImplementation(saveStorageMock);

    const pushMock = jest.fn();
    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ push: pushMock }));

    const chars = ['1', 'A', '2', 'B', '3', 'C'];
    fillForm(chars, true);

    const button = screen.getByRole('button', { name: /Confirmar/i });
    button.click();

    await waitFor(() => {
      expect(verifySignProcessMock).toHaveBeenCalledTimes(1);
      expect(verifySignProcessMock).toHaveBeenCalledWith(userMock.idUsuarioC, chars.join(''), userMock.access_token);

      expect(saveStorageMock).toHaveBeenCalledTimes(1);
      expect(saveStorageMock).toHaveBeenCalledWith('x-dta', verifySignProcessResponseMock);

      expect(pushMock).toHaveBeenCalledTimes(1);
      expect(pushMock).toHaveBeenCalledWith('/firmaExitosa');
    });
  });

  it('should show error if verifySignProcess response status is not 200', async () => {
    const message = 'Error';
    const verifySignProcessMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'verifiySignProcess').mockImplementation(verifySignProcessMock);

    const chars = ['1', 'A', '2', 'B', '3', 'C'];
    fillForm(chars);

    const button = screen.getByRole('button', { name: /Confirmar/i });
    button.click();

    expect(await screen.findByText(message)).toBeInTheDocument();
  });

  it('should catch verifySignProcess request exception', async () => {
    const error = new Error('Error');
    const verifySignProcessMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'verifiySignProcess').mockImplementation(verifySignProcessMock);

    const logMock = jest.fn();
    jest.spyOn(console, 'log').mockImplementation(logMock);

    const digitInputs = screen.getAllByRole('textbox');
    const chars = ['1', 'A', '2', 'B', '3', 'C'];

    digitInputs.forEach((input, index) => {
      fireEvent.change(input, { target: { value: chars[index] } });
    });

    const signAgreement = screen.getByRole('checkbox');
    signAgreement.click();

    const button = screen.getByRole('button', { name: /Confirmar/i });
    button.click();

    await waitFor(() => {
      expect(logMock).toHaveBeenCalledTimes(1);
      expect(logMock).toHaveBeenCalledWith(error);
    });
  });
});

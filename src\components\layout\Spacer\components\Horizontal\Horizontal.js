import React from 'react';
import PropTypes from 'prop-types';
import withStyles from '@/hocs/withStyles';
import { mapSize } from '@/components/layout/Spacer/helpers';

import styles from '@/components/layout/Spacer/Spacer.module.css';

export function Horizontal({
  getStyles,
  size,
  height,
  maxHeight,
  isPlayground,
}) {
  return (
    <div
      className={getStyles('spacer', 'horizontal', {
        'is-playground': isPlayground,
      })}
      style={{
        height,
        maxHeight,
        width: mapSize(size),
      }}
    />
  );
}

Horizontal.propTypes = {
  getStyles: PropTypes.func.isRequired,
  size: PropTypes.string,
  isPlayground: PropTypes.bool,
  height: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
  maxHeight: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
};

Horizontal.defaultProps = {
  size: 'none',
  height: '100%',
  maxHeight: 'auto',
  isPlayground: false,
  getStyles: () => ({}),
};

export default withStyles(styles)(Horizontal);

.tabs {
  width: 100%;
  display: flex;
  background: var(--color-base-white);
  max-width: var(--container-md-max-width);
  border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
  overflow: hidden;
  box-shadow: var(--box-shadow-sm);
}



.tab {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0.5rem;
  cursor: pointer;
  transition: border 0.2s ease-in-out;
  background: transparent;
  border: 3px solid var(--color-base-transparent);
  color: var(--color-font-base)
}

.tab.is-selected {
  border-bottom: 3px solid var(--color-primary);
  color: var(--color-primary)
}

.content {
  background: var(--color-base-white);
  box-shadow: var(--box-shadow-sm);
  border-radius: 0 var(--border-radius-sm) var(--border-radius-sm) var(--border-radius-sm);
  padding-top: 15px;
}
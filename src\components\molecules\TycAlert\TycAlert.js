import React from 'react';
import Alert from '@mui/material/Alert';
import AlertTitle from '@mui/material/AlertTitle';
import Button from '@/components/atoms/Button';
import PropTypes from 'prop-types';

function TycAlert({ mensaje, onFirmarClick }) {
  return (
    <Alert severity="warning" variant="outlined">
      <AlertTitle>Términos y Condiciones Pendientes</AlertTitle>
      {mensaje || 'Debes firmar los Términos y Condiciones para continuar usando la plataforma.'}
      <div style={{ marginTop: '8px' }}>
        <Button
          type="primary"
          size="sm"
          onClick={onFirmarClick}
          isInline
        >
          Firmar Términos y Condiciones
        </Button>
      </div>
    </Alert>
  );
}

TycAlert.propTypes = {
  mensaje: PropTypes.string,
  onFirmarClick: PropTypes.func.isRequired,
};

TycAlert.defaultProps = {
  mensaje: '',
};

export default TycAlert;

const nextJest = require('next/jest');

const createJestConfig = nextJest({
  dir: './',
});

/** @type {import('jest').Config} */
const customJestConfig = {
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleDirectories: ['node_modules', '<rootDir>/'],
  testEnvironment: 'jest-environment-jsdom',
  testTimeout: 30000,
  moduleNameMapper: {
    '^@/assets/(.*)$': '<rootDir>/src/assets/$1',
    '^@/components/(.*)$': '<rootDir>/src/components/$1',
    '^@/config/(.*)$': '<rootDir>/src/config/$1',
    '^@/helpers/(.*)$': '<rootDir>/src/helpers/$1',
    '^@/hocs/(.*)$': '<rootDir>/src/hocs/$1',
    '^@/hooks/(.*)$': '<rootDir>/src/hooks/$1',
    '^@/lib/(.*)$': '<rootDir>/src/lib/$1',
    '^@/pages/(.*)$': '<rootDir>/src/pages/$1',
    '^@/recoil/(.*)$': '<rootDir>/src/recoil/$1',
    '^@/services/(.*)$': '<rootDir>/src/services/$1',
    '^@/styles/(.*)$': '<rootDir>/src/styles/$1',
    '^@/tokens/(.*)$': '<rootDir>/src/tokens/$1',
    '^@/utils/(.*)$': '<rootDir>/src/utils/$1',
  },
};

module.exports = createJestConfig(customJestConfig);

import React, { useRef, useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';
import { useRecoilState } from 'recoil';
import Grid from '@mui/material/Grid';
import Table from '@mui/material/Table';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import TableBody from '@mui/material/TableBody';
import { fileToBase64String, humanizeSize } from '@/utils/fileAdmin';
import { userState } from '@/recoil/atoms';
import { uploadDocumentsG64 } from '@/services/user';
import deleteFileIcon from '@/assets/images/delete_file_2.png';
import dynamic from 'next/dynamic';
import Card from '@/components/atoms/Card';
import Icon from '@/components/atoms/Icon';
import CenteredContent from '@/components/layout/CenteredContent';
import Paragraph from '@/components/atoms/Paragraph';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';
import Error from '@/components/molecules/Error';
import styles from './DropZone.module.css';

const Modal = dynamic(() => import('@/components/atoms/Modal'), { ssr: false });

function DropZone() {
  const router = useRouter();

  const fileInputRef = useRef();
  const modalImageRef = useRef();
  const modalRef = useRef();
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [validFiles, setValidFiles] = useState([]);
  const [unsupportedFiles, setUnsupportedFiles] = useState([]);
  const [errorMessage, setErrorMessage] = useState('');
  const [user] = useRecoilState(userState);
  const [isOpen, setIsOpen] = useState(false);
  const [modalContent, setModalContent] = useState({
    isError: false,
    content: '',
    errors: [],
  });

  useEffect(() => {
    const filteredArr = selectedFiles.reduce((acc, current) => {
      const x = acc.find((item) => item.name === current.name);
      if (!x) {
        return acc.concat([current]);
      }
      return acc;
    }, []);
    setValidFiles([...filteredArr]);
  }, [selectedFiles]);

  const preventDefault = (e) => {
    e.preventDefault();
  };

  const dragOver = (e) => {
    preventDefault(e);
  };

  const dragEnter = (e) => {
    preventDefault(e);
  };

  const dragLeave = (e) => {
    preventDefault(e);
  };

  const validateFile = (file) => {
    const validTypes = ['application/pdf'];
    return validTypes.indexOf(file.type) !== -1;
  };

  const handleFiles = (files) => {
    // eslint-disable-next-line no-restricted-syntax
    for (const element of files) {
      if (validateFile(element)) {
        setSelectedFiles((prevArray) => [...prevArray, element]);
      } else {
        element.invalid = true;
        setSelectedFiles((prevArray) => [...prevArray, element]);
        setErrorMessage('No permitido');
        setUnsupportedFiles((prevArray) => [...prevArray, element]);
      }
    }
  };

  const fileDrop = (e) => {
    preventDefault(e);
    const { files } = e.dataTransfer;
    if (files.length) {
      handleFiles(files);
    }
  };

  const filesSelected = () => {
    if (fileInputRef.current.files.length) {
      handleFiles(fileInputRef.current.files);
    }
  };

  const fileInputClicked = () => {
    fileInputRef.current.click();
  };

  const removeFile = (name) => {
    const index = validFiles.findIndex((e) => e.name === name);
    const index2 = selectedFiles.findIndex((e) => e.name === name);
    const index3 = unsupportedFiles.findIndex((e) => e.name === name);
    validFiles.splice(index, 1);
    selectedFiles.splice(index2, 1);
    setValidFiles([...validFiles]);
    setSelectedFiles([...selectedFiles]);
    if (index3 !== -1) {
      unsupportedFiles.splice(index3, 1);
      setUnsupportedFiles([...unsupportedFiles]);
    }
  };

  const closeModal = () => {
    modalRef.current.style.display = 'none';
    modalImageRef.current.style.backgroundImage = 'none';
  };
  const uploadFiles = async () => {
    setIsOpen(true);
    setModalContent({
      isError: false,
      content: 'Cargando archivo(s) seleccionado(s)...',
      errors: [],
    });
    const body = [];
    for (let i = 0; i < validFiles.length; i += 1) {
      const { name } = validFiles[i];
      // eslint-disable-next-line no-await-in-loop
      const base64 = await fileToBase64String(validFiles[i]);
      const base64result = base64.substring(base64.indexOf(',') + 1);
      body[i] = {
        nombreArchivo: name,
        cantidadFirmas: 1,
        idUsuario: user.idUsuarioC,
        archivo64: base64result,
      };
    }
    const response = await uploadDocumentsG64(body, user?.access_token);
    if (response.status === 200) {
      const { data } = response;
      const hasErrors = data?.data?.filter?.((doc) => doc?.observacion !== 'OK') || [];
      if (hasErrors.length) {
        setModalContent({
          isError: true,
          content: 'Error al subir documentos a la plataforma ',
          errors: hasErrors.map((doc) => doc?.observacion),
        });
        return;
      }
      setModalContent({
        isError: false,
        content: 'Documentos cargados correctamente',
        errors: [],
      });
      validFiles.length = 0;
      setValidFiles([...validFiles]);
      setSelectedFiles([...validFiles]);
      setUnsupportedFiles([...validFiles]);
    } else {
      setModalContent({
        isError: true,
        content: 'Error al subir documentos a la plataforma ',
        errors: [],
      });
    }
  };

  const closeUploadModal = async () => {
    setIsOpen(false);

    if (!modalContent.isError) {
      await router.push('/');
    }
  };

  return (
    <>
      <div
        style={{ width: '100%', cursor: 'pointer' }}
        onDragOver={dragOver}
        onDragEnter={dragEnter}
        onDragLeave={dragLeave}
        onDrop={fileDrop}
        onClick={fileInputClicked}
      >
        <Card color="secondary">
          <CenteredContent>
            <Spacer.Vertical size="lg" />
            <Icon name="uploadDocument" color="inverted" size="2xl" />
            <Spacer.Vertical size="sm" />
            <Paragraph isCentered color="inverted">
              Arrastre los archivos o de click para agregar
            </Paragraph>
            <Spacer.Vertical size="lg" />
          </CenteredContent>
          <input
            ref={fileInputRef}
            className={styles['file-input']}
            type="file"
            multiple
            data-testid="upload-input"
            onChange={filesSelected}
          />
        </Card>
      </div>
      <Spacer.Vertical size="md" />
      <Grid container>
        <Grid item lg={6}>
          {unsupportedFiles.length ? (
            <Error>
              Por favor remueva los archivos no soportados.
            </Error>
          ) : (
            ''
          )}
          <div>
            <Table size="small">
              <TableBody>
                {validFiles.map((data, i) => (
                  <TableRow
                    key={i}
                  >
                    <TableCell>
                      <span>
                        {data.name.length > 50 ? `${data.name.substring(0, 50)}...` : data.name}
                        {' '}
                        <em>
                          {data.invalid ? (
                            <span className={styles['file-error-message']}>
                              (
                              {errorMessage}
                              )
                            </span>
                          ) : (
                            <Paragraph size="xs" color="muted" isInline>
                              (
                              {humanizeSize(data.size)}
                              )
                            </Paragraph>
                          )}
                        </em>
                      </span>
                    </TableCell>

                    <TableCell>
                      <Button
                        type="danger-outlined"
                        style={{ float: 'right' }}
                        onClick={() => removeFile(data.name)}
                        title="Remover documento  cargado"
                        data-testid={`remove-file-${data.name}`}
                      >
                        <Image
                          alt="Eliminar"
                          src={deleteFileIcon}
                          width={30}
                          height={30}
                          className={styles['delete-icon']}
                        />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {unsupportedFiles.length === 0 && validFiles.length ? (
            <div>
              <Paragraph size="xs" color="muted">
                <em>
                  Haz click en el botón para subir documentos cargados a la
                  plataforma Firmese
                </em>
              </Paragraph>
              <Spacer.Vertical size="sm" />
              <Button
                onClick={() => uploadFiles()}
              >
                Subir documentos a la plataforma
              </Button>
            </div>
          ) : (
            ''
          )}
        </Grid>
      </Grid>
      <div className={styles.modal} ref={modalRef}>
        <div className={styles['modal-overlay']} />
        <span className={styles.close} onClick={() => closeModal()}>
          X
        </span>
        <div className={styles['modal-image']} ref={modalImageRef} />
      </div>

      { isOpen && (
        <Modal
          onClose={closeUploadModal}
          title="Firme.se"
          type="tertiary"
          isInline
        >
          <div className={styles['progress-container-span']}>
            {modalContent.isError ? (
              <>
                <span className="error">{modalContent.content}</span>
                <ul>
                  {modalContent.errors.map((error, i) => (
                    <li key={i}>
                      {error}
                    </li>
                  ))}
                </ul>
              </>
            ) : (
              <span>{modalContent.content}</span>
            )}
          </div>
        </Modal>
      ) }
    </>
  );
}

export default DropZone;

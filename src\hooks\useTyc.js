import { useState, useEffect } from 'react';
import { useQuery } from 'react-query';
import { verificarTerminosCondiciones } from '@/services/user';

export const useTyc = (user) => {
  const [tycState, setTycState] = useState({
    requiereFirmar: false,
    mensaje: '',
    isLoading: true,
  });

  const {
    data: tycResponse,
    isLoading: tycLoading,
    error: tycError,
  } = useQuery(
    ['verificarTyC', user?.idUsuarioC],
    () => verificarTerminosCondiciones(user?.idUsuarioC, user?.access_token),
    {
      enabled: !!user?.access_token && !!user?.idUsuarioC,
      retry: false,
      refetchInterval: 5 * 60 * 1000, // Refrescar cada 5 minutos
      refetchIntervalInBackground: true,
      refetchOnWindowFocus: true,
    },
  );

  useEffect(() => {
    if (tycError) {
      setTycState({
        requiereFirmar: false,
        mensaje: '',
        isLoading: false,
      });
    }

    if (!tycLoading && tycResponse) {
      if (tycResponse.status === 200) {
        const { requiereFirmar, mensaje } = tycResponse.data.data;
        setTycState({
          requiereFirmar,
          mensaje,
          isLoading: false,
        });
      } else {
        setTycState({
          requiereFirmar: false,
          mensaje: '',
          isLoading: false,
        });
      }
    }
  }, [tycLoading, tycResponse, tycError]);

  return tycState;
};

import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function verificarTerminosCondicionesRoute(req, res) {
  try {
    // Solo permitir método POST
    if (req.method !== 'POST') {
      return res.status(405).json({ error: 'Method not allowed' });
    }

    const { user } = req.session;
    if (!user?.access_token) {
      return res.status(401).json({ error: 'No autorizado' });
    }

    if (!user?.idUsuarioC) {
      return res.status(400).json({ error: 'ID de usuario requerido' });
    }

    const response = await fetchApi.post(
      `/firma/manager/terminos-condiciones/verificar?idUsuario=${user.idUsuarioC}`,
      undefined, // Sin body
      {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${user.access_token}`,
          ...getDefaultHeaders(req),
        },
      },
    );

    return res.status(200).json(response.data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(verificarTerminosCondicionesRoute);

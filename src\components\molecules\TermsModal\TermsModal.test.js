import React from 'react';
import { render } from '@testing-library/react';
import TermsModal from './TermsModal';

jest.mock('@/components/atoms/Modal', () => ({
  __esModule: true,
  default: jest.fn(({ title }) => <span>{title}</span>),
}));

describe('Tests TermsModal', () => {
  it('should render correctly', () => {
    const { container } = render(<TermsModal onClose={jest.fn()} />);
    expect(container).toHaveTextContent('TÉRMINOS Y CONDICIONES');
  });
});

#author leonardo machado jaimes
image: docker:19.03.12
#image: maven:latest
stages:
  - docker-build-test
  - docker-build-prod
  - deploy-kube
  - deploy-kube-prod

docker-build-test:
  image: docker:latest
  stage: docker-build-test
  services:
    - docker:dind
  before_script:
    - az acr login --name firmese
    - export TAG=latest
  script:
    - docker build -t ${CI_PROJECT_TITLE}:$TAG --build-arg ENV_FILE=.env.development .
    - docker images
    - docker tag ${CI_PROJECT_TITLE} firmese.azurecr.io/${CI_PROJECT_TITLE}:$TAG
    - docker push firmese.azurecr.io/${CI_PROJECT_TITLE}:$TAG
  except:
    - master
  tags:
    - firmese-runner

docker-build-prod:
  image: docker:latest
  stage: docker-build-prod
  services:
    - docker:dind
  before_script:
    - az acr login --name firmese
    - export TAG=latest
  script:
    - docker build -t ${CI_PROJECT_TITLE}:$TAG --build-arg ENV_FILE=.env.production .
    - docker images
    - docker tag ${CI_PROJECT_TITLE} firmese.azurecr.io/${CI_PROJECT_TITLE}:$TAG
    - docker push firmese.azurecr.io/${CI_PROJECT_TITLE}:$TAG
  only:
    - master
  tags:
    - firmese-runner


deploy-kube:
  stage: deploy-kube
  before_script:
    - az aks get-credentials -g testing-kubernetes -n red-uno
  script:
    - kubectl get pods -o wide
    - kubectl apply -f ./scripts/deployment-app-web.yml --namespace=firmese-test
  after_script:
    - kubectl rollout restart deployment deployment-frms-web --namespace=firmese-test
    # - docker rmi -f  $(docker images -q)
  tags:
    - firmese-runner

deploy-kube-prod:
  stage: deploy-kube-prod
  before_script:
    - az aks get-credentials -g testing-kubernetes -n red-uno
  script:
    - kubectl get pods -o wide
    - kubectl apply -f ./scripts/deployment-app-web.yml --namespace=firmese-prod
  after_script:
    - kubectl rollout restart deployment deployment-frms-web --namespace=firmese-prod
    # - docker rmi -f  $(docker images -q)
  only:
    - master
  tags:
    - firmese-runner

import { stringify } from 'query-string';
import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function docsFirmadoRoute(req, res) {
  try {
    const { user } = req.session;
    if (!user?.access_token) {
      return res.status(401).send();
    }
    const { data } = await fetchApi.post(
      '/firma/manager/docs-firmado',
      stringify(req.body),
      {
        headers: {
          Authorization: `Bearer ${user.access_token}`,
          'Content-Type': 'application/x-www-form-urlencoded',
          ...getDefaultHeaders(req),
        },
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(docsFirmadoRoute);

.container-digits{
    width: 100%;
    margin: auto;
    text-align: center;
    align-items: center;
    justify-content: center;
}

.digits{
    width: 40px;
    border: var(--border-width-thin) solid var(--color-base-transparent);
    border-bottom: var(--border-width-thin) solid var(--color-font-base);
    aspect-ratio: 1;
    border-radius: 4px;
    padding: .3rem .1rem;
    text-align: center;
    font-size: 1.3rem;
}

.digits:focus {
    outline: none;
    border: var(--border-width-thin) solid var(--color-base-transparent);
    border-bottom: var(--border-width-thick) solid var(--color-primary);
}

@media screen and (max-width: 450px) {
    .digits {
        width: 30px;
    }
}
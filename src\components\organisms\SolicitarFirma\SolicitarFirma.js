import React, { useState } from 'react';
import Autocomplete, {
  createFilterOptions,
} from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import { useRecoilState } from 'recoil';
import Container from '@/components/layout/Container';
import Grid from '@mui/material/Grid';
import { userState } from '@/recoil/atoms';
import { solicitarMultipleFirma } from '@/services/user';
import logo3 from '@/assets/images/Logo3.png';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import Button from '@/components/atoms/Button';
import StatesHandler from '@/components/organisms/StateHandler';
import ButtonGroup from '@/components/atoms/ButtonGroup';
import Spacer from '@/components/layout/Spacer';
import Document from '@/components/molecules/Document';
import Paragraph from '@/components/atoms/Paragraph';
import Heading from '@/components/atoms/Heading';
import { useTheme } from '@mui/material';
import styles from './SolicitarFirma.module.css';

const initialState = {
  firmantes: [],
  documentos: [],
  tipoFirma: 'MULTIPLE',
};

function SolicitarFirma({ children, handleClick, documentos }) {
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();
  const filter = createFilterOptions();

  const [user] = useRecoilState(userState);
  const [value, setValue] = React.useState(null);
  const [state, setState] = useState(initialState);

  const newValues = [];
  const emails = [{ email: '' }];

  const handleChange = (event) => {
    setState({
      ...state,
      tipoFirma: event.target.value,
    });
  };

  const solicitarFirmas = () => {
    // Creación de firmantes - Evita la persistencia de firmantes anteriores
    // y genera un nuevo array basado solo en la selección actual
    const nuevosFirmantes = value ? value.map((item) => ({ email: item.email })) : [];

    // Creación de documentos - Misma estrategia para los documentos
    // asegurando que solo se incluyan los documentos actuales
    const nuevosDocumentos = documentos.files.map((file) => ({
      idArchivo: file.idArchivo,
      idUsuario: user.idUsuarioC,
    }));

    // Actualización del estado - Se utiliza un nuevo array para evitar
    // problemas de referencia y asegurar que los cambios se reflejen
    setState({
      ...state,
      isLoading: true,
      firmantes: nuevosFirmantes,
      documentos: nuevosDocumentos,
    });

    // Validación de datos - Verifica que haya al menos un firmante
    // antes de continuar con la solicitud
    if (!value?.length > 0) {
      setState({
        ...state,
        isLoading: false,
      });
      return enqueueSnackbar('Debe seleccionar al menos un firmante', { variant: 'error' });
    }

    // Creación de objeto para petición - Asegura que se usen los arrays
    // recién creados, no los del estado que podría no estar actualizado aún
    const estadoActualizado = {
      ...state,
      firmantes: nuevosFirmantes,
      documentos: nuevosDocumentos,
      tipoFirma: state.tipoFirma,
    };

    // Llamada a la API con datos limpios
    solicitarMultipleFirma(estadoActualizado, user?.access_token)
      .then((response) => {
        if (response?.status === 200) {
          // Limpieza completa del estado tras éxito
          setState({
            ...initialState,
            haveError: true,
            isLoading: false,
            errorMessage: {
              ...state.errorMessage,
              header: 'Solicitud de firmas',
              content: 'Se ha enviado solicitud de firma electrónica a las cuentas de correo asignadas',
              icon: logo3,
            },
          });
          setValue(null);
          newValues.splice(0, newValues.length);
        } else {
          setState({
            ...state,
            isLoading: false,
          });
          enqueueSnackbar(response.data.mensaje, { variant: 'error' });
        }
      })
      .catch((error) => console.log(error));
  };

  function handleBack() {
    handleClick();
    setState(initialState);
    setValue(null);
    newValues.splice(0, newValues.length);
  }

  return (
    <Container>
      {documentos.isMultiple ? (
        <Grid container>
          <Grid item xs={12}>
            <Button
              style={{ float: 'right' }}
              onClick={handleBack}
              type="tertiary"
              isInline
            >
              Volver al inicio
            </Button>
          </Grid>
          <Grid item xs={12}>
            <StatesHandler handleErrorButton={handleBack} state={state}>
              <Heading>
                Solicitud de múltiples firmas
              </Heading>
              <Paragraph size="sm">
                Documentos seleccionados para múltiple firma
              </Paragraph>
              <Spacer.Vertical size="sm" />
              <div className={styles['grid-card']}>
                {documentos.files.map((file) => (
                  <Document
                    key={file.idArchivo}
                    hasCheck={false}
                    {
                        ...Object.keys(file)
                          .filter((key) => ['idArchivo', 'nombreArchivo', 'fechaRegistro', 'descripcion'].includes(key))
                          .reduce((acc, key) => ({ ...acc, [key]: file[key] }), {})
                    }
                  />
                ))}
              </div>
              <Grid
                item
                lg={12}
                sx={{
                  marginTop: theme.spacing(4),
                  padding: 0,
                }}
              >
                <Heading size="sm">Tipo de solicitud de firma</Heading>
                <Spacer.Vertical size="sm" />
                <ButtonGroup>
                  <Button
                    component="label"
                    type="secondary"
                    isInline
                  >
                    <input
                      type="checkbox"
                      name="tipoFirmante"
                      value="MULTIPLE"
                      onChange={handleChange}
                      checked={state.tipoFirma === 'MULTIPLE'}
                    />
                    &nbsp;
                    Otros y yo
                  </Button>

                  <Button
                    component="label"
                    type="secondary"
                    isInline
                  >
                    <input
                      type="checkbox"
                      name="tipoFirmante"
                      value="OTHERS"
                      onChange={handleChange}
                      checked={state.tipoFirma === 'OTHERS'}
                    />
                    &nbsp;
                    Solo otros
                  </Button>
                </ButtonGroup>

                <Spacer.Vertical size="sm" />
                <Paragraph size="xs">
                  Indica a continuación las cuentas de correo electrónico de los
                  firmantes y luego das click en el botón
                  {' '}
                  <strong>
                    <em>Solicitar firmas</em>
                  </strong>
                </Paragraph>
                <Spacer.Vertical size="xs" />
                <div>
                  <Autocomplete
                    multiple
                    id="tags-standard"
                    data-testid="autocomplete"
                    options={emails}
                    getOptionLabel={(option) => option.email}
                    isOptionEqualToValue={(option, val) => option.email === val.email}
                    value={value || []}
                    onChange={(_event, newVal) => {
                      let newValue = newVal;

                      if (typeof newValue === 'string') {
                        setValue({
                          email: newValue,
                        });
                      } else if (newValue && newValue.inputValue) {
                        // Create a new value from the user input
                        setValue({
                          email: newValue.inputValue,
                        });
                      } else {
                        // Eliminación de duplicados - Usa Set para garantizar emails únicos
                        // // y prevenir duplicación accidental de firmantes
                        newValue = [...new Set(newValue.map((v) => v?.email))]
                          .map((email) => ({ email }));
                        setValue(newValue);
                      }
                      newValues.splice(0, newValues.length);
                      // eslint-disable-next-line no-restricted-syntax
                      for (const element of newValue) {
                        newValues.push(element);
                      }
                    }}
                    filterOptions={(options, params) => {
                      const filtered = filter(options, params);
                      const selectedEmails = value?.map(({ email }) => email?.toLowerCase()) || [];

                      // Suggest the creation of a new value
                      if (params.inputValue !== '' && !selectedEmails.includes(params.inputValue?.toLowerCase())) {
                        filtered.push({
                          email: `${params.inputValue}`,
                        });
                      }
                      return filtered;
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        variant="outlined"
                        label="Correo electrónico"
                        placeholder="Agrega correo electrónico de firmante"
                      />
                    )}
                  />
                </div>
                <Spacer.Vertical size="sm" />
                <Button
                  onClick={solicitarFirmas}
                  isInline
                >
                  Solicitar firmas
                </Button>
              </Grid>
            </StatesHandler>
          </Grid>
        </Grid>
      ) : (
        children
      )}
    </Container>
  );
}

SolicitarFirma.defaultProps = {
  children: null,
  handleClick: () => null,
};

SolicitarFirma.propTypes = {
  children: PropTypes.node,
  handleClick: PropTypes.func,
  documentos: PropTypes.shape({
    isMultiple: PropTypes.bool,
    files: PropTypes.arrayOf(PropTypes.shape({
      idArchivo: PropTypes.string.isRequired,
      nombreArchivo: PropTypes.string.isRequired,
      fechaRegistro: PropTypes.string.isRequired,
    }).isRequired),
  }).isRequired,
};

export default SolicitarFirma;

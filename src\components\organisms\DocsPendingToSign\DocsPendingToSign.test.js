import React from 'react';
import * as Recoil from 'recoil';
import {
  render, waitFor, screen,
} from '@testing-library/react';
import * as Notistack from 'notistack';
import * as Router from 'next/router';
import * as userService from '@/services/user';
import { QueryClient, QueryClientProvider } from 'react-query';
import DocsPendingToSign from './DocsPendingToSign';

const checkFirstDocument = async () => {
  const check = await screen.findByRole('checkbox');
  check.click();
};

const docInfo = {
  codigoTransaccion: '',
  hashArchivo: 'd8031178d89264a25c3bef41a59b2dd41da9191e623b254d6f837f3144c13c574455b7364eb0b7b77cfa86d5aeee3faa7a47f0656b9ce18d99ad77326f1c3ad0',
  estado: 'Pendiente Firma',
  nombreArchivo: 'Firmese.pdf',
  ip: '***********',
  idArchivo: '303',
  firmantes: null,
  tipoFirma: null,
  fechaRegistro: '2022-04-11 09:14:59.0',
  totalFirmas: '0',
  firmasRequeridas: '1',
};

const getDocsPendingToSignResponseMock = {
  content: [docInfo],
  pageable: {
    sort: {
      sorted: true,
      unsorted: false,
      empty: false,
    },
    pageSize: 6,
    pageNumber: 0,
    offset: 0,
    paged: true,
    unpaged: false,
  },
  last: true,
  totalPages: 1,
  totalElements: 1,
  first: true,
  sort: {
    sorted: true,
    unsorted: false,
    empty: false,
  },
  numberOfElements: 1,
  size: 6,
  number: 0,
  empty: false,
};

describe('Tests DocsPendingToSign', () => {
  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));
  const queryClient = new QueryClient();
  const user = {
    idUsuarioC: '1',
    nombreUsuario: 'test',
    access_token: 'access_token',
  };

  const component = (
    <QueryClientProvider client={queryClient}>
      <Recoil.RecoilRoot>
        <DocsPendingToSign />
      </Recoil.RecoilRoot>
    </QueryClientProvider>
  );

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call getDocsPendingToSign', async () => {
    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [user, jest.fn()]);

    const getDocsPendingToSignMock = jest.fn().mockImplementation(() => ({
      status: 200,
      data: getDocsPendingToSignResponseMock,
    }));

    jest.spyOn(userService, 'getDocsPendingToSign').mockImplementation(getDocsPendingToSignMock);

    render(component);

    await waitFor(() => {
      expect(getDocsPendingToSignMock).toHaveBeenCalled();
    });
  });

  it('should delete selected document', async () => {
    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [user, jest.fn()]);

    const getDocsPendingToSignMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: getDocsPendingToSignResponseMock,
    }));

    jest.spyOn(userService, 'getDocsPendingToSign').mockImplementation(getDocsPendingToSignMock);

    const message = 'success';
    const deletePendingDocumentsMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        mensaje: message,
      },
    }));

    jest.spyOn(userService, 'deletePendingDocuments').mockImplementation(deletePendingDocumentsMock);

    const {
      findByTestId,
      findByRole,
    } = render(component);

    await checkFirstDocument();

    const deleteButton = await findByTestId('delete-button');
    deleteButton.click();

    const confirmButton = await findByRole('button', { name: 'Eliminar' });
    confirmButton.click();

    await waitFor(() => {
      expect(deletePendingDocumentsMock).toHaveBeenCalled();
      expect(deletePendingDocumentsMock)
        .toHaveBeenCalledWith(docInfo.idArchivo, user.idUsuarioC, user.access_token);

      expect(enqueueSnackbarMock).toHaveBeenCalled();
      expect(enqueueSnackbarMock).toHaveBeenCalledWith(`Archivo ${docInfo.nombreArchivo} fue eliminado ${message}`, { variant: 'success' });
    });
  });

  it('should show error message if deletePendingDocuments response status is not 200', async () => {
    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [user, jest.fn()]);

    const getDocsPendingToSignMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: getDocsPendingToSignResponseMock,
    }));

    jest.spyOn(userService, 'getDocsPendingToSign').mockImplementation(getDocsPendingToSignMock);

    const message = 'error';
    const deletePendingDocumentsMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));

    jest.spyOn(userService, 'deletePendingDocuments').mockImplementation(deletePendingDocumentsMock);

    const {
      findByTestId,
      findByRole,
    } = render(component);

    await checkFirstDocument();

    const deleteButton = await findByTestId('delete-button');
    deleteButton.click();

    const confirmButton = await findByRole('button', { name: 'Eliminar' });
    confirmButton.click();

    await waitFor(() => {
      expect(enqueueSnackbarMock).toHaveBeenCalled();
      expect(enqueueSnackbarMock).toHaveBeenCalledWith(`archivo: ${docInfo.nombreArchivo} ${message}`, { variant: 'error' });
    });
  });

  it('should catch deletePendingDocuments request exception', async () => {
    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [user, jest.fn()]);

    const getDocsPendingToSignMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: getDocsPendingToSignResponseMock,
    }));

    jest.spyOn(userService, 'getDocsPendingToSign').mockImplementation(getDocsPendingToSignMock);

    const error = new Error('error');
    const deletePendingDocumentsMock = jest.fn().mockImplementation(() => Promise.reject(error));

    jest.spyOn(userService, 'deletePendingDocuments').mockImplementation(deletePendingDocumentsMock);

    const logMock = jest.fn();
    jest.spyOn(console, 'log').mockImplementation(logMock);

    const {
      findByTestId,
      findByRole,
    } = render(component);

    await checkFirstDocument();

    const deleteButton = await findByTestId('delete-button');
    deleteButton.click();

    const confirmButton = await findByRole('button', { name: 'Eliminar' });
    confirmButton.click();

    await waitFor(() => {
      expect(logMock).toHaveBeenCalled();
      expect(logMock).toHaveBeenCalledWith(error);
    });
  });

  it('should call signDocuments when click on firmar documentos button and has documents selected', async () => {
    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [user, jest.fn()]);

    const getDocsPendingToSignMock = jest.fn().mockResolvedValue({
      status: 200,
      data: getDocsPendingToSignResponseMock,
    });

    jest.spyOn(userService, 'getDocsPendingToSign').mockImplementation(getDocsPendingToSignMock);

    const signDocumentsMock = jest.fn().mockResolvedValue({
      status: 200,
    });

    jest.spyOn(userService, 'signDocuments').mockImplementation(signDocumentsMock);
    const signDocumentsParameters = [{
      idArchivo: docInfo.idArchivo,
      idUsuario: user.idUsuarioC,
    }];

    const {
      findByTestId,
    } = render(component);

    await checkFirstDocument();

    const firmarButton = await findByTestId('sign-button');
    firmarButton.click();

    await waitFor(() => {
      expect(signDocumentsMock).toHaveBeenCalled();
      expect(signDocumentsMock).toHaveBeenCalledWith(signDocumentsParameters, user.access_token);
    });
  });

  it('should redirect to /multiple-firma if signDocuments response status is 200', async () => {
    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [user, jest.fn()]);

    const getDocsPendingToSignMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: getDocsPendingToSignResponseMock,
    }));

    jest.spyOn(userService, 'getDocsPendingToSign').mockImplementation(getDocsPendingToSignMock);

    const signDocumentsMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
    }));

    jest.spyOn(userService, 'signDocuments').mockImplementation(signDocumentsMock);

    const pushMock = jest.fn();
    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({
      push: pushMock,
    }));

    const {
      findByTestId,
    } = render(component);

    await checkFirstDocument();

    const firmarButton = await findByTestId('sign-button');
    firmarButton.click();

    await waitFor(() => {
      expect(pushMock).toHaveBeenCalled();
      expect(pushMock).toHaveBeenCalledWith('/multiple-firma');
    });
  });

  it('should catch signDocuments request exception', async () => {
    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [user, jest.fn()]);

    const getDocsPendingToSignMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: getDocsPendingToSignResponseMock,
    }));

    jest.spyOn(userService, 'getDocsPendingToSign').mockImplementation(getDocsPendingToSignMock);

    const error = 'Error';
    const signDocumentsMock = jest.fn().mockImplementation(() => Promise.reject(error));

    jest.spyOn(userService, 'signDocuments').mockImplementation(signDocumentsMock);

    const logMock = jest.fn();
    jest.spyOn(console, 'log').mockImplementation(logMock);

    const {
      findByTestId,
    } = render(component);

    await checkFirstDocument();

    const firmarButton = await findByTestId('sign-button');
    firmarButton.click();

    await waitFor(() => {
      expect(logMock).toHaveBeenCalled();
      expect(logMock).toHaveBeenCalledWith(error);
    });
  });
});

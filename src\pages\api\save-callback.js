import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function saveCallback(req, res) {
  try {
    const { data } = await fetchApi.put(
      '/registro/usuario/servicio',
      req.body,
      {
        headers: {
          'Content-Type': 'application/json',
          ...getDefaultHeaders(req),
        },
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(saveCallback);

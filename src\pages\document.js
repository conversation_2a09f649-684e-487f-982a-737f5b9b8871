import React, { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import DocumentQR from '@/components/organisms/DocumentQR';
import { withSessionSsr } from '@/lib/withSession';
import { userState } from '@/recoil/atoms';

export const getServerSideProps = withSessionSsr(
  async ({ req }) => {
    const { user = null } = req.session;

    return {
      props: {
        user,
      },
    };
  },
);

export default function Document({ user }) {
  const [, setUser] = useRecoilState(userState);

  useEffect(() => {
    setUser(user);
  }, [user]);

  return (
    <DocumentQR />
  );
}

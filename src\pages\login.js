import React, { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import { useSnackbar } from 'notistack';
import LoginForm from '@/components/organisms/LoginForm';
import { withSessionSsr } from '@/lib/withSession';
import { userState } from '@/recoil/atoms';
import { useRouter } from 'next/router';
import { setTokenInfo } from '@/utils/tokenManager';
import axios from 'axios';

export const getServerSideProps = withSessionSsr(
  async ({ req }) => {
    const { user = null } = req.session;

    if (user?.email) {
      return {
        redirect: {
          destination: '/',
          permanent: false,
        },
      };
    }

    return {
      props: {
        user: user || null,
      },
    };
  },
);

export default function Login({ user }) {
  const [, setUser] = useRecoilState(userState);
  const router = useRouter();
  const { redirect, reason } = router.query;
  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    // Solo actualizar si hay usuario desde el servidor
    if (user && user.email && user.access_token) {
      console.log('Usuario desde SSR:', user);
      setUser(user);
      // Guardar token info si viene del servidor
      if (user.expires_in) {
        console.log('Guardando token info desde login SSR:', user.expires_in);
        setTokenInfo(user.expires_in);
      }
    }
  }, [user, setUser]);

  // Mostrar mensaje si llegó por token expirado
  useEffect(() => {
    if (reason === 'token_expired') {
      enqueueSnackbar('Tu sesión ha expirado. Por favor, inicia sesión nuevamente.', {
        variant: 'info',
        autoHideDuration: 5000,
      });
    } else if (reason === 'unauthorized') {
      enqueueSnackbar('Sesión inválida. Por favor, inicia sesión nuevamente.', {
        variant: 'warning',
        autoHideDuration: 5000,
      });
    }
  }, [reason, enqueueSnackbar]);

  const handleSubmit = async (values) => {
    try {
      const response = await axios.post('/api/login', values);

      console.log('Respuesta completa del login:', response.data);

      // La respuesta ya incluye toda la información del JWT
      const userData = response.data;

      // Guardar usuario en Recoil con toda la información
      setUser(userData);

      console.log('Login exitoso, token expires_in:', userData.expires_in);

      // Redirigir
      if (redirect) {
        router.push(redirect);
      } else {
        router.push('/');
      }
    } catch (error) {
      console.error('Error en login:', error);
      enqueueSnackbar('Error al iniciar sesión', {
        variant: 'error',
        autoHideDuration: 3000,
      });
    }
  };

  return <LoginForm redirect={redirect} onSubmit={handleSubmit} />;
}

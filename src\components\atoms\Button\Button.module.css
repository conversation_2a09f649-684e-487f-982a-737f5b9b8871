.button {
    display: inline-flex;
    width: 100%;
    /*min-width: max-content;*/
    height: 45px;
    max-height: fit-content;
    align-items: center;
    justify-content: center;
    padding: 20px 30px;
    border: var(--border-width-thin) solid var(--color-base-transparent);
    border-radius: var(--button-border-radius-sm);
    box-shadow: var(--box-shadow-sm);
    cursor: pointer;
    transition: filter 0.5s ease, box-shadow 0.2s ease-in;
}

.button:focus {
    box-shadow: 0 0 0 1px var(--color-primary), 0 0 10px 0 var(--color-primary);
    outline: none;
}

.button:hover {
    filter: brightness(1.1);
}

.button:active {
    box-shadow: var(--box-shadow-xs);
    filter: brightness(0.9);
}

.type-primary {
    background: var(--color-primary);
}

.type-primary:focus {
    box-shadow: 0 0 0 1px var(--color-primary-inverted),
    0 0 10px 0 var(--color-primary);
}

.type-secondary {
    border: var(--border-width-thin) solid var(--color-primary);
    background: var(--color-base-transparent);
}

.type-tertiary {
    height: 25px;
    padding: 0;
    border-bottom: 1px solid var(--color-primary);
    background: var(--color-base-transparent);
    border-radius: var(--border-radius-none);
    box-shadow: var(--box-shadow-none);
}

.type-tertiary:focus {
    box-shadow: 0 10px 10px -8px var(--color-primary);
}

.type-danger-outlined {
    border: var(--border-width-thin) solid var(--color-red-500);
    background: var(--color-base-transparent);
}

.type-danger-outlined:focus {
    box-shadow: 0 10px 10px -8px var(--color-red-500);
}

.type-danger {
    background: var(--color-red-500);
}

.type-danger:focus {
    box-shadow: 0 0 0 1px var(--color-primary-inverted),
    0 0 10px 0 var(--color-red-500);
}

.type-base {
    border: var(--border-width-thin) solid var(--color-font-base);
    background: var(--color-base-transparent);
}

.type-base:focus {
    box-shadow: 0 0 0 1px var(--color-primary-inverted),
    0 0 10px 0 var(--color-font-base);
}

.button.is-inline {
    max-width: max-content;
}

.is-muted {
    background: var(--color-primary-muted);
}

@media (max-width: 768px) {
    .button.type-primary,
    .button.type-secondary,
    .button.type-danger-outlined,
    .button.type-danger,
    .button.type-base {
        max-width: 100% !important;
    }
}
import React, { useState } from 'react';
import Container from '@/components/layout/Container';
import { solicitarCambioContrasena } from '@/services/user';
import icon from '@/assets/images/logo_principal.png';
import { useSnackbar } from 'notistack';
import StatesHandler from '@/components/organisms/StateHandler';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';
import Input from '@/components/atoms/Input';
import { useForm } from 'react-hook-form';
import Paragraph from '@/components/atoms/Paragraph';
import Heading from '@/components/atoms/Heading';

function CambiarContrasena() {
  const { enqueueSnackbar } = useSnackbar();
  const [state, setState] = useState({
    isLoaded: false,
    haveError: false,
    isLoading: false,
    loadMessage: 'Enviando solicitud de cambio de contraseña',
    errorMessage: {
      header: 'Ha ocurrido un error al solicitar contraseña',
      content: 'este mensaje es automático',
      icon,
    },
  });

  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      username: '',
    },
  });

  const handleError = () => {
    setState({
      ...state,
      haveError: false,
    });
  };

  const onSubmit = (values) => {
    const { username } = values;
    setState({
      ...state,
      isLoading: true,
    });
    solicitarCambioContrasena({ username })
      .then((response) => {
        if (response.status === 200) {
          setState({
            ...state,
            isLoading: false,
            haveError: true,
            errorMessage: {
              ...state.errorMessage,
              header: 'Cambio de contraseña',
              content: response.data.data,
              icon,
            },
          });
        } else {
          setState({
            ...state,
            isLoading: false,
          });
          enqueueSnackbar(response.data.mensaje, { variant: 'warning' });
        }
      })
      .catch((error) => console.log(error));
  };

  return (
    <div>
      <StatesHandler handleErrorButton={handleError} state={state}>
        <Container>
          <Heading>Solicitud de cambio de contraseña</Heading>
          <Paragraph size="xs">Digita a continuación el correo electrónico asignado al registro</Paragraph>
          <Spacer.Vertical size="sm" />
          <form onSubmit={handleSubmit(onSubmit)}>
            <Input
              type="email"
              placeholder="Correo electrónico"
              {
              ...register('username', {
                required: 'El correo electrónico es requerido',
                pattern: {
                  value: /[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+/,
                  message: 'El correo electrónico no es válido',
                },
              })
            }
              hasError={Object.keys(errors).includes('username')}
              helperText={errors.username && errors.username.message}
            />

            <Button isInline buttonType="submit">
              Recuperar contraseña
            </Button>
          </form>
        </Container>
      </StatesHandler>
    </div>
  );
}

export default CambiarContrasena;

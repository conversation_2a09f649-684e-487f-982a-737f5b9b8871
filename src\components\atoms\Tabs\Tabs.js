import React, { useState } from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';

import styles from './Tabs.module.css';

export function Tabs({
  selected,
  tabs,
  getStyles,
}) {
  const [active, setActive] = useState(selected);

  const handleChange = (index) => () => {
    setActive(index);
  };

  return (
    <>
      <div
        className={getStyles('tabs')}
      >
        {
                tabs
                  .sort((a, b) => a.index - b.index)
                  .map((tab, index) => {
                    const isSelected = active === index;

                    return (
                      <button
                        type="button"
                        key={tab?.index || index}
                        className={getStyles('tab', {
                          'is-selected': isSelected,
                        })}
                        onClick={handleChange(index)}
                      >
                        {tab.label}
                      </button>
                    );
                  })
            }
      </div>
      <div className={getStyles('content')}>
        {tabs[active].content}
      </div>
    </>
  );
}

Tabs.propTypes = {
  onChange: PropTypes.func.isRequired,
  selected: PropTypes.number.isRequired,
  tabs: PropTypes.arrayOf(PropTypes.shape({
    index: PropTypes.number,
    label: PropTypes.string.isRequired,
    // eslint-disable-next-line react/forbid-prop-types
    content: PropTypes.object.isRequired,
  })).isRequired,
  getStyles: PropTypes.func.isRequired,
};

Tabs.defaultProps = {
  onChange: () => {},
  selected: 0,
  tabs: [],
  getStyles: () => {},
};

export default withStyles(styles)(Tabs);

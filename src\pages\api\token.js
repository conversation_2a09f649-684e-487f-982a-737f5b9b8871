import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function tokenRoute(req, res) {
  try {
    const { token } = req.query;
    const { data } = await fetchApi.post(
      `/token/registro/?codigo=${token}`,
      undefined,
      {
        headers: getDefaultHeaders(req),
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(tokenRoute);

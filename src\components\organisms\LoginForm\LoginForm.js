import Logo from '@/assets/images/Logo3.png';
import { userState } from '@/recoil/atoms';
import Image from 'next/image';
import NextLink from 'next/link';
import { useRouter } from 'next/router';
import React, { useState } from 'react';
import { useRecoilState } from 'recoil';
import { useForm } from 'react-hook-form';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';
import Input from '@/components/atoms/Input';
import CenteredContent from '@/components/layout/CenteredContent';
import Link from '@/components/atoms/Link';
import Divider from '@/components/atoms/Divider';
import StateHandler from '@/components/organisms/StateHandler';
import withStyles from '@/hocs/withStyles';
import PropTypes from 'prop-types';
import styles from './LoginForm.module.css';

export function LoginForm({
  getStyles,
  redirect,
}) {
  const [, setUser] = useRecoilState(userState);

  const router = useRouter();
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      username: '',
      password: '',
      grant_type: 'password',
    },
  });

  const [state, setState] = useState({
    isLoaded: false,
    haveError: false,
    isLoading: false,
    loadMessage: 'Iniciando Sesión',
    errorMessage: {
      header: 'Ha ocurrido un error iniciando sesión',
      content: 'este mensaje es automático',
      icon: Logo,
    },
  });

  const onSubmit = async (values) => {
    setState({
      ...state,
      isLoading: true,
    });

    const { getlogin } = await import('@/services/user');
    const response = await getlogin(values);

    if (response.status === 200) {
      const { data } = response;

      setUser({
        ...data,
      });
      const fechaInicioSesion = new Date();
      const { saveHourLogin } = await import('@/utils/utils');
      saveHourLogin(fechaInicioSesion);
      const parsedRedirect = redirect?.match(/^\/[a-zA-Z]+(\/[a-zA-Z/]*)?(\?[a-zA-Z\d=&]*)?/)?.[0] || null;
      const isValidRedirect = !!redirect
          && !!parsedRedirect
          && parsedRedirect?.length === redirect?.length;
      return router.push(isValidRedirect ? redirect : '/');
    }
    setState({
      ...state,
      isLoading: false,
      haveError: true,
      errorMessage: {
        ...state.errorMessage,
        header: 'Error al iniciar sesión, usuario o contraseña incorrectos',
        content: 'Por favor vuelve a intentar',
      },
    });
  };

  const handleError = () => {
    setState({
      ...state,
      isLoading: false,
      haveError: false,
    });
  };

  return (
    <CenteredContent>
      <StateHandler handleErrorButton={handleError} state={state}>
        <Spacer.Horizontal size="sm" />
        <div style={{ textAlign: 'center' }}>
          <Image src={Logo} alt="Logo" height={200} width={200} />
        </div>
        {/* <Grid container justifyContent="center" className={classes.logoContainer}> */}
        {/*  <Grid item sm={10} lg={6}> */}
        {/*  </Grid> */}
        {/* </Grid> */}
        <div className={getStyles('login')}>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Input
              id="mail"
              placeholder="Correo electrónico"
              {
                ...register('username', {
                  required: 'El correo electrónico es obligatorio',
                  pattern: {
                    value: /[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+/,
                    message: 'El correo electrónico no es válido',
                  },
                })
              }
              hasError={Object.keys(errors).includes('username')}
              helperText={errors.username && errors.username.message}
            />
            <Input
              type="password"
              {
                ...register('password', {
                  required: 'La contraseña es obligatoria',
                })
              }
              placeholder="Contraseña"
              role="textbox"
              hasError={Object.keys(errors).includes('password')}
              helperText={errors.password && errors.password.message}
            />
            <Button
              buttonType="submit"
            >
              Iniciar sesión
            </Button>
            <Spacer.Horizontal size="sm" />
            <div className={getStyles('forgot-password')}>
              <NextLink href="/olvide-contrasena" passHref>
                <Link>Olvidé mi contraseña</Link>
              </NextLink>
            </div>
            <Spacer.Horizontal size="sm" />
            <Divider />
            <Spacer.Horizontal size="sm" />
            <div
              className={getStyles('actions')}
            >
              <NextLink href="/verificar" passHref>
                <Button
                  type="secondary"
                >
                  Verificar documento
                </Button>
              </NextLink>
              <NextLink href="/registrar" passHref>
                <Button
                  type="secondary"
                >
                  Regístrese
                </Button>
              </NextLink>
            </div>
          </form>
        </div>
      </StateHandler>
    </CenteredContent>
  );
}

LoginForm.propTypes = {
  getStyles: PropTypes.func.isRequired,
  redirect: PropTypes.string,
};

LoginForm.defaultProps = {
  getStyles: () => {},
  redirect: null,
};

export default withStyles(styles)(LoginForm);

import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import styles from './Divider.module.css';

export function Divider({ getStyles }) {
  return <div className={getStyles('divider')} />;
}

Divider.propTypes = {
  getStyles: PropTypes.func.isRequired,
};

Divider.defaultProps = {
  getStyles: () => ({}),
};

export default withStyles(styles)(Divider);

.input {
  width: 100%;
  height: var(--input-height);
  padding: 12px 17px;
  border: var(--border-width-thin) solid var(--input-border-color);
  background: var(--input-background);
  border-radius: var(--input-border-radius);
  color: var(--color-font-base);
  font-family: var(--font-family-sans);
  font-size: var(--input-font-size);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  transition: box-shadow 0.2s ease;
}

.input::placeholder {
  color: var(--color-font-highlight);
}

.input:focus {
  border: var(--border-width-thin) solid var(--input-border-color);
  box-shadow: 0 0 0 1px var(--color-primary), 0 0 10px 0 var(--color-primary);
  outline: none;
}

.input:disabled {
  filter: opacity(.5);
  cursor: not-allowed;
}

.is-inline {
  max-width: max-content;
}

.has-error {
  border-color: var(--color-red-500);
  color: var(--color-red-500);
}

.has-error::placeholder {
  color: var(--color-red-400);
}

.helper-text {
  padding-left: 10px;
  margin: 1rem;
}

.helper-text.has-error {
  margin-top: 0;
}

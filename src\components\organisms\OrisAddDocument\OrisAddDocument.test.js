import {
  act, fireEvent, render,
} from '@testing-library/react';
import OrisAddDocument, * as exportedFunctions from '@/components/organisms/OrisAddDocument/OrisAddDocument';

jest.mock('@/components/organisms/OrisAddDocument/OrisAddDocument', () => ({
  __esModule: true,
  ...jest.requireActual('@/components/organisms/OrisAddDocument/OrisAddDocument'),
}));
describe('Tests OrisAddDocument', () => {
  const handleSubmitMock = jest.fn();

  it('all fields should be required', async () => {
    const { getByText } = render(<OrisAddDocument handleSubmit={handleSubmitMock} />);

    const btn = getByText(/Solicitar Firma/i);

    await act(() => {
      fireEvent.click(btn);
    });

    const onSubmitMock = jest.fn();
    jest.spyOn(exportedFunctions, 'onSubmit').mockImplementation(onSubmitMock);

    expect(onSubmitMock).not.toHaveBeenCalled();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});

.title{
    color: var(--color-font-base);
}

.btnPrimary{
    color: var(--color-base-white);
    font-weight: bold;
    font-size: 15px;
}

.btnSecondary{
    color: var(--color-primary);
    font-weight: bold;
    font-size: 15px;
}

.table{
    border: solid 1.5px var(--color-primary);
}

.containerTable{
    overflow-x: auto;
    margin-right: auto;
    margin-left: auto;
    margin-bottom: 10px;
}

.tableHead{
    color: var(--color-shiny-shamrock);
}

/* .preview-container {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
} */

.preview-info-container {
    display: inline-flex;
}

.grid {
    display: flex;
    grid-gap: 1rem;
    flex-direction: row;
    justify-content: space-around;
}

.divBtn{
    width: 40%;
}

@media (max-width: 1250px) {
    .grid{
        flex-direction: column;
    }
}

@media (max-width: 920px) {
    .preview-container > .preview-info-container {
        margin-top: 15px;
    }

    .grid{
        flex-direction: column;
    }
}

@media (max-width: 700px) {
    .divBtn{
        width: 50%;
    }
}
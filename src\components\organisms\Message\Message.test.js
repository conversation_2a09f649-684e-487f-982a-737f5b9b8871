import React from 'react';
import { render, fireEvent } from '@testing-library/react';
import Message from './Message';

const message = {
  title: 'Test Title',
  header: 'Test Header',
};

describe('Tests Message', () => {
  it('should show message', () => {
    const { getByText } = render(<Message errorMessage={message} handleClick={jest.fn()} />);
    expect(getByText(message.title)).toBeInTheDocument();
    expect(getByText(message.header)).toBeInTheDocument();
  });

  it('should call handleClick', () => {
    const handleClick = jest.fn();
    const { getByRole } = render(<Message errorMessage={message} handleClick={handleClick} />);
    const button = getByRole('button');
    fireEvent.click(button);

    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});

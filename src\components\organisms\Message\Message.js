import React, { useMemo } from 'react';
import Image from 'next/image';
import Container from '@/components/layout/Container';
import Grid from '@mui/material/Grid';
import PropTypes from 'prop-types';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';

function Message({ handleClick, errorMessage }) {
  const imageSize = useMemo(() => {
    if (errorMessage?.icon?.width > errorMessage?.icon?.height) {
      return {
        width: 200,
        height: 200 * (errorMessage.icon.height / errorMessage.icon.width),
      };
    }
    return {
      width: 200,
      height: 200,
    };
  }, [errorMessage]);
  return (
    <Container>
      <Grid container justifyContent="center">
        <Grid item lg={6} md={6} sm={12} xs={12} style={{ textAlign: 'center' }}>

          {/* Solo renderizar Image si existe el icono */}
          {errorMessage?.icon && (
            <Image
              src={errorMessage.icon}
              alt="icono"
              height={imageSize.height}
              width={imageSize.width}
            />
          )}

          {errorMessage?.title && (
            <Heading>{errorMessage.title}</Heading>
          )}

          {errorMessage?.header && (
            <Heading size="sm">{errorMessage.header}</Heading>
          )}

          {errorMessage?.content && (
            <Paragraph>{errorMessage.content}</Paragraph>
          )}
          <Spacer.Vertical size="sm" />
          <Button
            onClick={handleClick}
            isInline
          >
            Volver al inicio
          </Button>

        </Grid>
      </Grid>
    </Container>

  );
}

Message.propTypes = {
  handleClick: PropTypes.func.isRequired,
  errorMessage: PropTypes.shape({
    icon: PropTypes.oneOfType([
      PropTypes.element,
      PropTypes.node,
      PropTypes.shape({
        src: PropTypes.string,
        height: PropTypes.number,
        width: PropTypes.number,
        blurDataURL: PropTypes.string,
      })]),
    title: PropTypes.string,
    header: PropTypes.string,
    content: PropTypes.string,
  }).isRequired,
};

export default Message;

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import { saveStorage, loadItemStorage } from '@/utils/utils';
import dayjs from 'dayjs';
import Container from '@/components/layout/Container';
import StateHandler from '@/components/organisms/StateHandler';
import Heading from '@/components/atoms/Heading';
import Spacer from '@/components/layout/Spacer';
import Paragraph from '@/components/atoms/Paragraph';
import Card from '@/components/atoms/Card';
import CenteredContent from '@/components/layout/CenteredContent';
import Icon from '@/components/atoms/Icon';
import Button from '@/components/atoms/Button';
import { seeSigners, consultarEstadoSolicitud } from '@/services/user';
import icon from '@/assets/images/document-error-flat.png';
import Table from '@mui/material/Table';
import TableRow from '@mui/material/TableRow';
import { TableHead } from '@mui/material';
import TableCell from '@mui/material/TableCell';
import TableBody from '@mui/material/TableBody';
import DocPreview from '@/components/organisms/DocPreview';
import styles from './InformationDocsRequestSign.module.css';

function InformationDocsRequestSign() {
  const router = useRouter();
  const [user] = useRecoilState(userState);
  const [state, setState] = useState({
    files: [],
    information: {},
    isLoading: true,
    haveError: false,
    errorMessage: {
      header: 'Ocurrió un error',
      content:
                'No se pudo cargar la información del documento. Por favor, intenta nuevamente.',
      icon,
    },
  });

  const handleErrorButton = async () => {
    saveStorage('docsToShow', '');
    setState({
      ...state,
      files: [],
      isLoading: true,
    });
    await router.replace('/');
  };

  const parseFiles = async () => {
    const docs = loadItemStorage('docsToShow') || [];
    const info = loadItemStorage('element');

    const docsToShow = await Promise.all(docs.map(async (doc) => {
      try {
        // Intentar usar el nuevo endpoint primero
        const estadoResponse = await consultarEstadoSolicitud(doc.idArchivo, user?.access_token);
        
        if (estadoResponse.status === 200) {
          console.log('Usando nuevo endpoint para doc:', doc.idArchivo, estadoResponse.data);
          
          // Adaptar los datos del nuevo formato
          const solicitudes = estadoResponse.data.solicitudes || [];
          const firmantesAdaptados = solicitudes.map((solicitud, index) => ({
            idDocument: `${estadoResponse.data.idArchivoFirma}-${solicitud.emailFirmante}-${index}`,
            nombreFirmante: solicitud.emailFirmante.split('@')[0],
            correoFirmante: solicitud.emailFirmante,
            rol: solicitud.rolFirmante,
            fechaSolicitud: solicitud.fechaRegistro,
            detalleSolicitud: solicitud.firmado ? 'Firmado por interviniente' : 'Pendiente de firma',
            fechaFirma: solicitud.firmado ? solicitud.fechaRegistro : null,
            ordenFirma: solicitud.ordenFirma,
            tipoOrdenFirma: solicitud.tipoOrdenFirma,
            fechaVencimiento: solicitud.fechaVencimiento,
          }));

          return {
            ...doc,
            firmantes: firmantesAdaptados,
            previewB64: doc.previewB64 || null,
            // Información adicional del nuevo endpoint
            descripcion: estadoResponse.data.descripcion,
            progreso: estadoResponse.data.progreso,
            totalSolicitudes: estadoResponse.data.totalSolicitudes,
            solicitudesFirmadas: estadoResponse.data.solicitudesFirmadas,
          };
        } else {
          throw new Error('Nuevo endpoint falló, usar fallback');
        }
      } catch (error) {
        console.log('Usando endpoint fallback para doc:', doc.idArchivo);
        
        // Fallback al endpoint anterior
        const fallbackResponse = await seeSigners(doc.idArchivo, user?.access_token);
        
        return {
          ...doc,
          ...(fallbackResponse.status === 200 
            ? {
                firmantes: fallbackResponse.data?.data?.firmantes || [],
                previewB64: fallbackResponse.data?.data?.previewB64,
              }
            : {
                firmantes: [],
                previewB64: null,
              }
          ),
        };
      }
    }));

    setState({
      ...state,
      files: docsToShow,
      information: info,
      isLoading: false,
    });
  };

  useEffect(() => {
    if (user?.access_token) {
      parseFiles();
    }
  }, [user]);

  const goHome = async () => {
    saveStorage('docsToShow', '');
    saveStorage('element', '');
    await router.replace('/');
  };

  return (
    <Container>
      <StateHandler handleErrorButton={handleErrorButton} state={state}>
        <div>
          <div style={{ textAlign: 'right' }}>
            <Button
              type="tertiary"
              isInline
              onClick={goHome}
            >
              Volver al inicio
            </Button>
          </div>
          <Spacer.Vertical size="sm" />
          <Heading>
            Información detallada del archivo a firmar
          </Heading>
          <Spacer.Vertical size="lg" />
          {state.files.length === 0
            ? (
              <div>
                <CenteredContent>
                  <Icon name="verifyDocument" color="primary" size="2xl" />
                  <Spacer size="md" />
                  <Paragraph size="md" color="muted" isCentered>
                    Esta solicitud no tiene un archivo.
                  </Paragraph>
                </CenteredContent>
              </div>
            )
            : (
              <div>
                {state.files.map((element) => (
                  <div key={element.idArchivo} className={styles.grid}>
                    <div className={styles['preview-container']}>
                      <Spacer.Vertical size="sm" />
                      <DocPreview docId={element.idArchivo} />
                    </div>
                    <div className={styles['info-container']}>
                      <div className={styles['row-info']} style={{ flexDirection: 'row' }}>
                        <p className={styles['text-bold']}>
                          {'Nombre archivo: '}
                        </p>
                        <Spacer.Horizontal size="xs" />
                        <p className={styles['text-normal']}>{element.nombreArchivo}</p>
                      </div>
                      <div className={styles['row-info']} style={{ flexDirection: 'row' }}>
                        <p className={styles['text-bold']}>
                          {'Fecha Solicitud: '}
                        </p>
                        <Spacer.Horizontal size="xs" />
                        <p className={styles['text-normal']}>{dayjs(state.information.fechaSolicitud).format('DD/MM/YYYY')}</p>
                      </div>
                      {state?.information?.fechaVigencia != null && (
                        <div className={styles['row-info']} style={{ flexDirection: 'row' }}>
                          <p className={styles['text-bold']}>
                            {'Fecha Vigencia: '}
                          </p>
                          <Spacer.Horizontal size="xs" />
                          <p className={styles['text-normal']}>{dayjs(state.information.fechaVigencia).format('DD/MM/YYYY')}</p>
                        </div>
                      )}
                      <Spacer.Vertical size="md" />
                      <p className={styles['text-bold']}>
                        Propietario
                      </p>
                      <Spacer.Vertical size="xs" />
                      <div className={styles['row-info']} style={{ flexDirection: 'row' }}>
                        <p className={styles['text-bold']}>
                          {'Nombre: '}
                        </p>
                        <Spacer.Horizontal size="xs" />
                        <p className={styles['text-normal']}>{state.information.propietario.nombreCompleto}</p>
                      </div>
                      {state?.information?.propietario?.numeroDocumento != null && (
                        <div className={styles['row-info']} style={{ flexDirection: 'row' }}>
                          <p className={styles['text-bold']}>
                            {'Documento: '}
                          </p>
                          <Spacer.Horizontal size="xs" />
                          <p className={styles['text-normal']}>{state.information.propietario.numeroDocumento}</p>
                        </div>
                      )}
                      <Spacer.Vertical size="md" />
                      <div className={styles['row-info']} style={{ flexDirection: 'row' }}>
                        <p className={styles['text-bold']}>
                          {'IP archivo: '}
                        </p>
                        <Spacer.Horizontal size="xs" />
                        <p className={styles['text-normal']}>{element.ip}</p>
                      </div>
                      <Spacer.Vertical size="lg" />
                      <Table size="small" className={styles.table}>
                        <TableHead>
                          <TableRow>
                            <TableCell>
                              <strong className={styles.tableHead}>Nombre</strong>
                            </TableCell>
                            <TableCell>
                              <strong className={styles.tableHead}>Correo</strong>
                            </TableCell>
                            <TableCell>
                              <strong className={styles.tableHead}>Rol</strong>
                            </TableCell>
                            <TableCell>
                              <strong className={styles.tableHead}>Fecha Firma</strong>
                            </TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {element.firmantes && element.firmantes.map((firmante) => (
                            <TableRow key={firmante.idDocument || firmante.correoFirmante}>
                              <TableCell>
                                {firmante.nombreFirmante || firmante.correoFirmante?.split('@')[0] || 'N/A'}
                              </TableCell>
                              <TableCell>
                                {firmante.correoFirmante || 'N/A'}
                              </TableCell>
                              <TableCell>
                                {firmante.rol || 'Firmante'}
                              </TableCell>
                              <TableCell>
                                {firmante.fechaFirma 
                                  ? dayjs(firmante.fechaFirma).format('DD/MM/YYYY')
                                  : 'Pendiente'
                                }
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                      <Spacer.Vertical size="md" />
                      {state?.information?.token !== null && (
                        <Button
                          type="primary"
                          isInline
                          onClick={() => router.push(`/multiple-firma/${state.information.token}`)}
                          size="xxs"
                        >
                          Firmar
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
        </div>
      </StateHandler>
    </Container>
  );
}

export default InformationDocsRequestSign;

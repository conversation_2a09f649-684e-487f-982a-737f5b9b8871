import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import catchApiError from '@/utils/catchApiError';
import getPDFPreview from '@/utils/getPDFPreview';
import path from 'path';
import fs from 'fs';

async function verificaTokenFirmanteRoute(req, res) {
  try {
    const { code } = req.body;
    if (!code) {
      return res.status(400).json({
        error: 'Invalid code',
      });
    }
    const { data } = await fetchApi.post(
      '/token/registro/verifica-token-firmante',
      code,
      {
        headers: {
          'Content-Type': 'text/plain',
          ...getDefaultHeaders(req),
        },
      },
    );
    const docs = Promise.all(data?.data?.archivos?.map(async (doc) => {
      const pdfDir = path.join(
        __dirname,
        '../../../../tmp/',
      );

      if (!fs.existsSync(pdfDir)) {
        fs.mkdirSync(pdfDir);
      }

      const pdfPath = path.join(
        pdfDir,
        `${Math.floor(new Date().getTime() * Math.random())}.pdf`,
      );

      try {
        await fs.writeFileSync(pdfPath, doc.b64, { encoding: 'base64' });
        const previewB64 = await getPDFPreview(pdfPath);
        await fs.unlinkSync(pdfPath);

        return {
          ...doc,
          previewB64,
        };
      } catch (e) {
        console.error(e);

        if (fs.existsSync(pdfPath)) {
          await fs.unlinkSync(pdfPath);
        }

        return {
          ...doc,
          previewB64: null,
        };
      }
    }));
    return res.send({
      ...data,
      data: {
        ...data?.data || {},
        archivos: await docs,
      },
    });
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(verificaTokenFirmanteRoute);

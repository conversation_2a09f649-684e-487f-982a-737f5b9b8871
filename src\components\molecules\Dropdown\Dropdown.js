import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import Icon from '@/components/atoms/Icon';

import styles from './Dropdown.module.css';

export const Dropdown = React.forwardRef(({
  getStyles,
  id,
  options,
  isInline,
  hasError,
  helperText,
  onChange,
  onBlur,
  name,
  label,
  ...props
}, ref) => (
  <div>
    {label && (
    <label
      htmlFor={id}
      className={getStyles('label', {
        'has-error': hasError,
      })}
    >
      {label}
    </label>
    )}
    <div
      id={id}
      className={getStyles('dropdown', {
        'is-inline': isInline,
      })}
    >
      <Icon className="dropdown-icon" name="angleDown" background="highlight" />
      <select
        className={getStyles('dropdown-select', {
          'has-error': hasError,
        })}
        name={name}
        onChange={onChange}
        onBlur={onBlur}
        ref={ref}
        role="combobox"
        {
            ...props
        }
      >
        {options?.map(({ text, value }) => (
          <option key={value} value={value}>
            {text}
          </option>
        ))}
      </select>
    </div>
    <p
      className={getStyles('helper-text', {
        'has-error': hasError,
      })}
    >
      {helperText}
    </p>
  </div>
));

Dropdown.propTypes = {
  onChange: PropTypes.func.isRequired,
  getStyles: PropTypes.func.isRequired,
  options: PropTypes.arrayOf(
    PropTypes.shape({
      text: PropTypes.string,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    }),
  ).isRequired,
  id: PropTypes.string,
  isInline: PropTypes.bool,
  hasError: PropTypes.bool,
  helperText: PropTypes.string,
  name: PropTypes.string,
  onBlur: PropTypes.func,
  label: PropTypes.string,
};

Dropdown.defaultProps = {
  isInline: false,
  onChange: () => { /* */ },
  getStyles: () => ({}),
  options: [],
  hasError: false,
  helperText: '',
  label: null,
};

export default withStyles(styles)(Dropdown);

export const docRepository = (strategy) => {
  const normalizeResponse = (response) => {
    console.log('[Repository] Normalizando respuesta:', {
      status: response?.status,
      hasData: !!response?.data,
    });

    if (!response || response.status !== 200) {
      const error = `Error en la API: ${response?.status || 'Desconocido'}`;
      console.error('[Repository] Respuesta inválida:', error);
      throw new Error(error);
    }

    const { data } = response;
    const nextPage = data?.totalPages > 1 && !data?.last 
      ? (data?.number || 0) + 1 
      : null;

    const normalized = {
      data: data || { content: [], totalPages: 0, last: true },
      nextPage,
      status: response.status,
    };

    console.log('[Repository] Respuesta normalizada:', {
      itemsCount: normalized.data.content?.length || 0,
      hasNextPage: !!nextPage,
      totalPages: normalized.data.totalPages,
    });

    return normalized;
  };

  const handleError = (error) => {
    console.error('[Repository] Manejando error:', {
      message: error.message,
      stack: error.stack?.split('\n')[0],
    });

    return {
      data: { content: [], totalPages: 0, last: true },
      nextPage: null,
      error: error.message,
      status: error.response?.status || 500,
    };
  };

  return {

    async fetchData(params) {
      console.log('[Repository] Obteniendo datos:', {
        parametros: params,
      });

      try {
        const response = await strategy.fetchData(params);
        return normalizeResponse(response);
      } catch (error) {
        return handleError(error);
      }
    },

    getQueryKey(searchName, userId) {
      return strategy.getQueryKey(searchName, userId);
    },
  };
};
import React, { useState, useEffect } from 'react';
import { documentosSolicitudFirma } from '@/services/user';
import { useRecoilState } from 'recoil';
import { useRouter } from 'next/router';
import { userState } from '@/recoil/atoms';
import Table from '@mui/material/Table';
import { TableHead } from '@mui/material';
import TableRow from '@mui/material/TableRow';
import TableCell from '@mui/material/TableCell';
import TableBody from '@mui/material/TableBody';
import Paper from '@mui/material/Paper';
import StateHandler from '@/components/organisms/StateHandler';
import Spacer from '@/components/layout/Spacer';
import Heading from '@/components/atoms/Heading';
import Container from '@/components/layout/Container';
import icon from '@/assets/images/document-error-flat.png';
import ButtonIcon from '@/components/molecules/ButtonIcon';
import Paragraph from '@/components/atoms/Paragraph';
import CenteredContent from '@/components/layout/CenteredContent';
import Icon from '@/components/atoms/Icon';
import { saveStorage } from '@/utils/utils';
import dayjs from 'dayjs';
import styles from './DocToSign.module.css';

function DocsToSign() {
  const [user, setUser] = useRecoilState(userState);
  const router = useRouter();
  const [state, setState] = useState({
    response: '',
    isLoading: true,
  });

  useEffect(() => {
    setUser(user);
  }, [user]);

  const handleErrorButton = async () => {
    setState({
      ...state,
      isLoading: false,

    });
    await router.replace('/');
  };

  useEffect(() => {
    documentosSolicitudFirma(user?.access_token).then((responseApi) => {
      if (responseApi.status === 200) {
        return setState({
          ...state,
          response: responseApi?.data,
          isLoading: false,
        });
      }
      return setState({
        ...state,
        haveError: true,
        response: {},
        isLoading: false,
        errorMessage: {
          ...state.errorMessage,
          header: 'Error al traer información',
          content:
            'Lo sentimos ocurrió un error al traer la información del documento, por favor, intenta más tarde.',
          icon,
        },
      });
    }).catch((err) => console.error(err));
  }, []);

  return (
    <Container>
      <StateHandler handleErrorButton={handleErrorButton} state={state}>
        <Heading>Documentos con solicitud de firma</Heading>
        <Spacer.Vertical size="md" />
        {state?.response?.data?.length === 0
          ? (
            <div>
              <CenteredContent>
                <Spacer.Vertical size="lg" />
                <Paragraph size="md" className={styles.txtCenter}>Por el momento no cuentas con solicitud de firmas.</Paragraph>
                <Spacer.Vertical size="lg" />
              </CenteredContent>
            </div>
          ) : (
            <div>
              <Paper className={styles.containerTable}>
                <Table size="small" aria-label="customized table" className={styles.table}>
                  <TableHead>
                    <TableRow>
                      <TableCell style={{ textAlign: 'center' }}>
                        <strong className={styles.tableHead}>Nombre del propietario</strong>
                      </TableCell>
                      <TableCell className={styles.txtCenter}>
                        <strong className={styles.tableHead}>Número de documento</strong>
                      </TableCell>
                      <TableCell className={styles.txtCenter}>
                        <strong className={styles.tableHead}>Nombre archivo</strong>
                      </TableCell>
                      <TableCell className={styles.txtCenter}>
                        <strong className={styles.tableHead}>Ip del archivo</strong>
                      </TableCell>
                      <TableCell className={styles.txtCenter}>
                        <strong className={styles.tableHead}>Fecha</strong>
                      </TableCell>
                      <TableCell className={styles.txtCenter}>
                        <strong className={styles.tableHead}>Información</strong>
                      </TableCell>
                      <TableCell className={styles.txtCenter}>
                        <strong className={styles.tableHead}>Firmar documentos</strong>
                      </TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {state.response.data?.map((solicitud) => solicitud.archivos.map((file) => (
                      <TableRow key={solicitud.token}>
                        <TableCell className={styles.txtCenter}>
                          {solicitud.propietario.nombreCompleto}
                        </TableCell>
                        <TableCell className={styles.txtCenter}>
                          {solicitud.propietario.numeroDocumento}
                        </TableCell>
                        <TableCell className={styles.txtCenter}>
                          <p className={styles.itemTable}>
                            {file.nombreArchivo}
                          </p>
                        </TableCell>
                        <TableCell className={styles.txtCenter}>
                          <p className={styles.itemTable}>
                            {file.ip}
                          </p>
                        </TableCell>
                        <TableCell className={styles.txtCenter}>
                          <p className={styles.itemTable}>
                            {dayjs(solicitud.fechaSolicitud).format('DD/MM/YYYY')}
                          </p>
                        </TableCell>
                        <TableCell className={styles.txtCenter}>
                          <Icon
                            onClick={() => {
                              const requestInfo = {
                                fechaSolicitud: solicitud.fechaSolicitud,
                                propietario: solicitud.propietario,
                                token: solicitud.token,
                                fechaVigencia: solicitud.fechaVigencia,
                                archivos: solicitud?.archivos
                                  ?.map(({
                                    idArchivo, nombreArchivo, ip,
                                  }) => ({
                                    idArchivo, nombreArchivo, ip,
                                  })) || [],
                              };

                              saveStorage('element', requestInfo);
                              saveStorage('docsToShow', requestInfo.archivos);
                              return router.push('/informacion-documento');
                            }}
                            isClickable
                            name="information"
                            size="md"
                            border="shinyShamrock"
                            color="shinyShamrock"
                            data-testid="information-button"
                          />
                        </TableCell>
                        <TableCell className={styles.txtCenter}>
                          <ButtonIcon icon="pen" type="primary" onClick={() => router.push(`/multiple-firma/${solicitud.token}`)} size="xxs" />
                        </TableCell>
                      </TableRow>
                    )))}
                  </TableBody>
                </Table>
              </Paper>
            </div>
          )}
      </StateHandler>
    </Container>
  );
}

export default DocsToSign;

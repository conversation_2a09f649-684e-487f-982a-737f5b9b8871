import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Container from '@/components/layout/Container';
import { getVerifyQR } from '@/services/user';
import icon from '@/assets/images/Logo3.png';
import StateHandler from '@/components/organisms/StateHandler';
import DocumentInfo from '@/components/organisms/DocumentInfo';

const capturarCodigo = () => {
  const query = window.location.search;
  return query.substring(4, query.length);
};

function DocumentQR() {
  const [document, setDocument] = useState();
  const router = useRouter();

  const [state, setState] = useState({
    haveError: false,
    isLoading: true,
    loadMessage: 'Estamos generando tu código',
    code: '',
    errorMessage: {
      header: 'Ha ocurrido un error la obtención del código',
      content: 'este mensaje es automático',
      icon,
    },
  });

  useEffect(() => {
    const cod = capturarCodigo();
    getVerifyQR(cod)
      .then(({ data, status }) => {
        if (status === 200) {
          const {
            nombreArchivo,
            fechaRegistroStr: fechaRegistro,
            cantidadConsultas,
            firmas,
            ip,
            propietario,
            hashArchivo,
            idArchivo,
            previewB64,
            size,
            pdfPages,
          } = data.data[0];
          setDocument({
            ...document,
            showInfo: true,
            nombreArchivo,
            fechaRegistro,
            cantidadConsultas,
            firmas,
            ip,
            propietario,
            hashArchivo,
            idArchivo,
            previewB64,
            size,
            pdfPages,
          });
          setState({
            ...state,
            isLoading: false,
            code: data.data.codigoTransaccion,
          });
        } else if (status !== 200) {
          setState({
            ...state,
            isLoading: false,
            haveError: true,
            content: data.mensaje,
            errorMessage: {
              ...state.errorMessage,
              content: data.mensaje,
              header: 'Verificación de documento',
              title: 'Contacta con nuestro servicio técnico',
            },
          });
        }
      })
      .catch((err) => console.error(err));
  }, []);

  const handleBack = async () => {
    await router.push('/');
  };

  const handleErrorButton = async () => {
    await router.push('/');
  };

  return (
    <div>
      <Container>
        <StateHandler handleErrorButton={handleErrorButton} state={state}>
          <DocumentInfo handleClick={handleBack} documentInfo={document} />
        </StateHandler>
      </Container>
    </div>
  );
}

export default DocumentQR;

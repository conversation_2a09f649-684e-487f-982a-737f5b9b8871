import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { getPreview } from '@/services/user';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import Card from '@/components/atoms/Card';
import CenteredContent from '@/components/layout/CenteredContent';
import Icon from '@/components/atoms/Icon';
import Spacer from '@/components/layout/Spacer';
import Paragraph from '@/components/atoms/Paragraph';
import Loader from '@/components/organisms/Loader';
import { useRouter } from 'next/router';
import styles from './DocPreview.module.css';

export default function DocPreview({ className, docId, isFlat }) {
  const [b64, setB64] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [user] = useRecoilState(userState);
  const router = useRouter();

  const { token } = router.query;

  useEffect(() => {
    if (!user?.access_token && !token) {
      setIsLoading(false);
      return;
    }

    getPreview(docId, user?.access_token, token).then((res) => {
      if (res?.status !== 200) {
        setB64(null);
        return;
      }

      setB64(Buffer.from(res.data, 'binary').toString('base64'));
    })
      .finally(() => {
        setIsLoading(false);
      });
  }, [user, token]);

  const content = (
    <>

      {!!b64 && (
        <>
          {/* eslint-disable-next-line @next/next/no-img-element */}
          <img
            src={`data:image/png;base64,${b64}`}
            className={className || styles.preview}
            alt="Preview"
          />
        </>
      )}

      {!isLoading && !b64 && (
      <>
        {isFlat ? (
          <div style={{ textAlign: 'center', padding: '20px 5px' }}>
            <Icon name="verifyDocument" color="primary" size="2xl" />
            <Spacer size="sm" />
            <Paragraph size="sm" color="muted" isCentered>
              No se pudo obtener la vista previa del documento.
            </Paragraph>
          </div>
        ) : (
          <CenteredContent>
            <Icon name="verifyDocument" color="primary" size="2xl" />
            <Spacer size="sm" />
            <Paragraph size="sm" color="muted" isCentered>
              No se pudo obtener la vista previa del documento.
            </Paragraph>
          </CenteredContent>
        )}
      </>
      )}

      {!!isLoading && (
      <>
        {isFlat
          ? (<Loader message="Cargando vista previa del documento" />)
          : (
            <CenteredContent>
              <Loader message="Cargando vista previa del documento" />
            </CenteredContent>
          )}
      </>
      )}
    </>
  );

  return isFlat ? content : (
    <Card isInline={!!b64} border="primary" style={{ height: b64 ? 'auto' : '100%' }}>
      {content}
    </Card>
  );
}

DocPreview.defaultProps = {
  isFlat: false,
};

DocPreview.propTypes = {
  className: PropTypes.string,
  docId: PropTypes.number.isRequired,
  isFlat: PropTypes.bool,
};

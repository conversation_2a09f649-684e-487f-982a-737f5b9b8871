import React from 'react';
import PropTypes from 'prop-types';
import withStyles from '@/hocs/withStyles';
import { mapSize } from '@/components/layout/Spacer/helpers';

import styles from '@/components/layout/Spacer/Spacer.module.css';

export function Vertical({ getStyles, size, isPlayground }) {
  return (
    <div
      className={getStyles('spacer', 'vertical', {
        'is-playground': isPlayground,
      })}
      style={{
        height: mapSize(size),
      }}
    />
  );
}

Vertical.propTypes = {
  getStyles: PropTypes.func.isRequired,
  size: PropTypes.string.isRequired,
  isPlayground: PropTypes.bool,
};

Vertical.defaultProps = {
  size: 'none',
  isPlayground: false,
  getStyles: () => ({}),
};

export default withStyles(styles)(Vertical);

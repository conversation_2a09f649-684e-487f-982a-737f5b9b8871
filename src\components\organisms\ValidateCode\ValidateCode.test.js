import React from 'react';
import {
  fireEvent, render, waitFor, screen,
} from '@testing-library/react';
import * as Router from 'next/router';
import * as utils from '@/utils/utils';
import * as userService from '@/services/user';
import * as Notistack from 'notistack';
import ValidateCode from './ValidateCode';

function fillForm(chars, { checkLength = false, acceptAgreement = true } = {}) {
  const digitInputs = screen.getAllByRole('textbox');
  if (checkLength) {
    expect(digitInputs.length).toBe(6);
  }

  digitInputs.forEach((input, index) => {
    fireEvent.change(input, { target: { value: chars[index] } });
  });

  if (acceptAgreement) {
    const signAgreement = screen.getByRole('checkbox');
    signAgreement.click();
  }
}

jest.mock('@/components/molecules/SignAgreementModal', () => ({
  __esModule: true,
  default: jest.fn(() => <span>SignAgreementModal mock</span>),
}));

describe('Tests ValidateCode', () => {
  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should accept Sign Agreement if has userToken', async () => {
    const {
      getByRole, getByText, queryAllByText, findByText,
    } = render(<ValidateCode userToken="123" haytoken={false} />);
    expect(getByRole('checkbox')).toBeInTheDocument();

    expect(queryAllByText(/Debe aceptar el acuerdo/i)).toHaveLength(0);

    const chars = ['1', 'A', '2', 'B', '3', 'C'];
    fillForm(chars, { checkLength: true, acceptAgreement: false });

    const btn = getByText(/Confirmar/i);
    btn.click();

    expect(await findByText(/Debe aceptar el acuerdo/i)).toBeInTheDocument();
  });

  it('should call validarFirmanteDocumento if haytoken is true', async () => {
    const signResult = {
      resultadoFirma: 'OK',
      nombreArchivo: 'chucknorris.pdf',
    };
    const validarFirmanteDocumentoMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: [signResult],
      },
    }));
    jest.spyOn(userService, 'validarFirmanteDocumento').mockImplementation(validarFirmanteDocumentoMock);

    const alttoken = '123';
    const userToken = '456';
    const user = {
      idUsuarioC: '789',
    };

    const saveStorageMock = jest.fn();
    jest.spyOn(utils, 'saveStorage').mockImplementation(saveStorageMock);
    const pushMock = jest.fn();
    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ push: pushMock }));

    const {
      getByText,
    } = render(<ValidateCode haytoken alltoken={alttoken} userToken={userToken} user={user} />);

    const chars = ['1', 'A', '2', 'B', '3', 'C'];
    fillForm(chars, { acceptAgreement: true });

    const btn = getByText(/Confirmar/i);
    btn.click();

    await waitFor(() => {
      expect(saveStorageMock).toHaveBeenCalledTimes(1);
      expect(saveStorageMock).toHaveBeenCalledWith('x-dta', { data: [signResult] });

      expect(pushMock).toHaveBeenCalledTimes(1);
      expect(pushMock).toHaveBeenCalledWith('/firmaExitosa');
    });
  });

  it('should show error message if validarFirmanteDocumento response status is not 200', async () => {
    const message = 'Error';
    const validarFirmanteDocumentoMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'validarFirmanteDocumento').mockImplementation(validarFirmanteDocumentoMock);

    const alttoken = '123';
    const userToken = '456';
    const user = {
      idUsuarioC: '789',
    };
    const {
      getByText,
      findByText,
    } = render(<ValidateCode haytoken alltoken={alttoken} userToken={userToken} user={user} />);

    const chars = ['1', 'A', '2', 'B', '3', 'C'];
    fillForm(chars, { acceptAgreement: true });

    const btn = getByText(/Confirmar/i);
    btn.click();

    expect(await findByText(message)).toBeInTheDocument();
  });

  it('should catch validarFirmanteDocumento request exception', async () => {
    const error = new Error('Error');
    const validarFirmanteDocumentoMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'validarFirmanteDocumento').mockImplementation(validarFirmanteDocumentoMock);

    const errorMock = jest.fn();
    jest.spyOn(console, 'error').mockImplementation(errorMock);

    const alttoken = '123';
    const userToken = '456';
    const user = {
      idUsuarioC: '789',
    };
    const {
      getByText,
    } = render(<ValidateCode haytoken alltoken={alttoken} userToken={userToken} user={user} />);

    const chars = ['1', 'A', '2', 'B', '3', 'C'];
    fillForm(chars, { acceptAgreement: true });

    const btn = getByText(/Confirmar/i);
    btn.click();

    await waitFor(() => {
      expect(errorMock).toHaveBeenCalled();
      expect(errorMock).toHaveBeenCalledWith(error);
    });
  });

  it('should show Sign Agreement when click on "acuerdo de firma electrónica"', async () => {
    const alttoken = '123';
    const userToken = '456';
    const user = {
      idUsuarioC: '789',
    };
    const {
      getByText,
      queryAllByText,
      findByText,
    } = render(<ValidateCode haytoken alltoken={alttoken} userToken={userToken} user={user} />);

    expect(queryAllByText(/SignAgreementModal mock/i)).toHaveLength(0);

    const modalTrigger = getByText(/Acuerdo de firma electrónica/i);
    modalTrigger.click();

    expect(await findByText(/SignAgreementModal mock/i)).toBeInTheDocument();
  });

  it('should call verifySignProcess if haytoken is false', async () => {
    const verifySignProcessResponseMock = {
      status: 'success',
    };
    const verifySignProcessMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: verifySignProcessResponseMock,
    }));
    jest.spyOn(userService, 'verifiySignProcess').mockImplementation(verifySignProcessMock);

    const saveStorageMock = jest.fn();
    jest.spyOn(utils, 'saveStorage').mockImplementation(saveStorageMock);

    const pushMock = jest.fn();
    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ push: pushMock }));

    const alttoken = '123';
    const user = {
      idUsuarioC: '789',
      access_token: 'access_token',
    };
    const {
      getByText,
    } = render(<ValidateCode haytoken={false} alltoken={alttoken} user={user} />);

    const chars = ['1', 'A', '2', 'B', '3', 'C'];
    fillForm(chars, { acceptAgreement: false });

    const btn = getByText(/Confirmar/i);
    btn.click();

    await waitFor(() => {
      expect(verifySignProcessMock).toHaveBeenCalledTimes(1);
      expect(verifySignProcessMock).toHaveBeenCalledWith(user.idUsuarioC, chars.join(''), user.access_token);

      expect(saveStorageMock).toHaveBeenCalledTimes(1);
      expect(saveStorageMock).toHaveBeenCalledWith('x-dta', verifySignProcessResponseMock);

      expect(pushMock).toHaveBeenCalledTimes(1);
      expect(pushMock).toHaveBeenCalledWith('/firmaExitosa');
    });
  });

  it('should catch verifySignProcess request exception', async () => {
    const error = new Error('Error');
    const verifySignProcessMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'verifiySignProcess').mockImplementation(verifySignProcessMock);

    const logMock = jest.fn();
    jest.spyOn(console, 'log').mockImplementation(logMock);

    const alttoken = '123';
    const user = {
      idUsuarioC: '789',
    };
    const {
      getByText,
    } = render(<ValidateCode haytoken={false} alltoken={alttoken} user={user} />);

    const chars = ['1', 'A', '2', 'B', '3', 'C'];
    fillForm(chars, { acceptAgreement: false });

    const btn = getByText(/Confirmar/i);
    btn.click();

    await waitFor(() => {
      expect(logMock).toHaveBeenCalledTimes(1);
      expect(logMock).toHaveBeenCalledWith(error);
    });
  });

  it('should call enviarCodigoPorCorreo with idUsuarioC when click on "Click para enviar tu código de verificación por correo" if haytoken is false', async () => {
    const message = 'Success';
    const enviarCodigoPorCorreoMock = jest.fn().mockResolvedValue({
      status: 200,
      data: {
        data: message,
      },
    });
    jest.spyOn(userService, 'enviarCodigoPorCorreo').mockImplementation(enviarCodigoPorCorreoMock);

    const altToken = '123';
    const user = {
      idUsuarioC: 789,
    };
    const {
      findByRole,
    } = render(<ValidateCode haytoken={false} alltoken={altToken} user={user} />);

    const btn = await findByRole('button', { name: 'Correo' });
    btn.click();

    const payload = {
      id: user.idUsuarioC,
      tipo: 'EML',
    };
    await waitFor(() => {
      expect(enviarCodigoPorCorreoMock).toHaveBeenCalledTimes(1);
      expect(enviarCodigoPorCorreoMock).toHaveBeenCalledWith(payload);
      expect(enqueueSnackbarMock).toHaveBeenCalledWith('Se ha enviado tu código nuevamente.', { variant: 'success' });
    });
  });

  it('should call enviarCodigoPorCorreo with userToken when click on "Click para enviar tu código de verificación por correo" if haytoken is true', async () => {
    const message = 'Success';
    const enviarCodigoPorCorreoMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: message,
      },
    }));
    jest.spyOn(userService, 'enviarCodigoPorCorreo').mockImplementation(enviarCodigoPorCorreoMock);

    const altToken = '123';
    const user = {
      idUsuarioC: 789,
    };
    const userToken = '456';
    const {
      getByRole,
    } = render(<ValidateCode haytoken alltoken={altToken} user={user} userToken={userToken} />);

    const btn = getByRole('button', { name: 'Correo' });
    btn.click();

    const payload = {
      id: userToken,
      tipo: 'EML',
    };
    await waitFor(() => {
      expect(enviarCodigoPorCorreoMock).toHaveBeenCalledTimes(1);
      expect(enviarCodigoPorCorreoMock).toHaveBeenCalledWith(payload);
      expect(enqueueSnackbarMock).toHaveBeenCalledWith('Se ha enviado tu código nuevamente.', { variant: 'success' });
    });
  });

  it('should dont show "Click para enviar tu código de verificación por correo" if isNew is true', () => {
    const {
      queryAllByText,
    } = render(<ValidateCode haytoken={false} isNew />);

    expect(queryAllByText(/Click para enviar tu código de verificación por correo/i)).toHaveLength(0);
  });
});

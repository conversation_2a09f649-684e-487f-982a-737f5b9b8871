import { getUserLocation } from '@/helpers/getUserLocation';

describe('[ helpers / getUserLocation ]', () => {
  describe('when get geolocation', () => {
    it('should return geolocation if it is successfully', async () => {
      const expected = {
        latitude: 51.1,
        longitude: 45.3,
      };

      global.navigator.geolocation = {
        getCurrentPosition: jest.fn().mockImplementation(
          (success) => success({
            coords: expected,
          }),
        ),
        watchPosition: jest.fn(),
        clearWatch: jest.fn(),
      };

      const result = await getUserLocation();

      expect(result).toEqual(expected);
    });

    it('should call reject if it is not successfully', async () => {
      const mockAlert = jest.fn();

      jest.spyOn(window, 'alert').mockImplementation(mockAlert);

      const mockLog = jest.fn();
      jest.spyOn(console, 'log').mockImplementation(mockLog);

      const mockError = new Error();

      global.navigator.geolocation = {
        getCurrentPosition: jest.fn().mockImplementation(
          (_success, error) => error(mockError),
        ),
        watchPosition: jest.fn(),
        clearWatch: jest.fn(),
      };

      await expect(async () => getUserLocation()).rejects.toEqual(mockError);
    });
  });
});

import { 
  signedDocsStrategy,
  ownerDocsStrategy,
  unificadosDocsStrategy,
} from '@/lib/strategies/documentStrategies';

const strategies = new Map([
  ['signed-docs', signedDocsStrategy],
  ['signed-docs-me', ownerDocsStrategy],
  ['signed-docs-uni', unificadosDocsStrategy],
]);

export const docsFactory = {
  getStrategy(dataType) {
    const strategy = strategies.get(dataType);
    if (!strategy) {
      console.warn(`No existe estrategia para: ${dataType}`);
      throw new Error(`No hay estrategia de tipo: ${dataType}`);
    }
    return strategy;
  },
  registerStrategy(dataType, strategy) {
    if (strategies.has(dataType)) {
      console.warn(`Ya existe una estrategia registrada para: ${dataType}`);
    }
    strategies.set(dataType, strategy);
  },
};

import * as userService from '@/services/user';
import {
  fireEvent, render, screen, waitFor,
} from '@testing-library/react';
import * as Router from 'next/router';
import React from 'react';
import RegisterForm from './index';

jest.mock('@/components/molecules/TermsModal', () => ({
  __esModule: true,
  default: () => <span>TermsModal mock</span>,
}));

jest.mock('@/components/molecules/DataPolicyModal', () => ({
  __esModule: true,
  default: () => <span>DataPolicyModal mock</span>,
}));

function fillForm(formValues, { withChecks = false } = {}) {
  const documentTypeSelect = screen.getByRole('combobox');
  fireEvent.change(documentTypeSelect, { target: { value: formValues.tipoDocumento } });

  const documentNumberInput = screen.getByLabelText(/Número de documento/i);
  fireEvent.change(documentNumberInput, { target: { value: formValues.numeroDocumento } });

  const documentExpeditionDateInput = screen.getByLabelText(/Fecha de expedición/i);
  fireEvent.change(
    documentExpeditionDateInput,
    { target: { value: formValues.fechaExpedicion } },
  );

  const cellphoneInput = screen.getByLabelText(/Número de celular/i);
  fireEvent.change(cellphoneInput, { target: { value: formValues.numeroCelular } });

  const emailInput = screen.getByLabelText(/Correo electrónico/i);
  fireEvent.change(emailInput, { target: { value: formValues.correoElectronico } });

  const passwordInput = screen.getByLabelText('Contraseña');
  fireEvent.change(passwordInput, { target: { value: formValues.contrasena } });

  const confirmPasswordInput = screen.getByLabelText('Confirmar Contraseña');
  fireEvent.change(confirmPasswordInput, { target: { value: formValues.contrasena } });

  if (withChecks) {
    const checks = screen.getAllByText(/Acepto/i);
    checks.forEach((check) => {
      fireEvent.click(check);
    });
  }
}

describe('Tests RegisterForm', () => {
  const formValues = {
    tipoDocumento: 'CC',
    numeroDocumento: '12345678',
    fechaExpedicion: '2000-01-01',
    correoElectronico: '<EMAIL>',
    numeroCelular: '12345678',
    contrasena: '12345678',
    cpasswd: '12345678',
  };

  const pushMock = jest.fn();
  jest.spyOn(Router, 'useRouter').mockImplementation(() => ({ push: pushMock }));

  beforeEach(() => {
    render(<RegisterForm />);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show terms modal when click on "términos y condiciones"', async () => {
    expect(screen.queryAllByText(/TermsModal mock/i)).toEqual([]);

    const modalTrigger = screen.getByText(/términos y condiciones/i);
    modalTrigger.click();

    expect(await screen.findByText(/TermsModal mock/i)).toBeInTheDocument();
  });

  it('should show data policy modal when click on "política de tratamiento de datos personales"', () => {
    expect(screen.queryAllByText(/DataPolicyModal mock/i)).toEqual([]);

    const modalTrigger = screen.getByText(/política de tratamiento de datos personales/i);
    fireEvent.click(modalTrigger);

    expect(screen.getByText(/DataPolicyModal mock/i)).toBeInTheDocument();
  });

  it('should redirect to /aviso/[email] if register response status is 200', async () => {
    const registerMock = jest.fn().mockResolvedValue({ status: 200 });
    jest.spyOn(userService, 'register').mockImplementation(registerMock);

    fillForm(formValues, { withChecks: true });

    const button = screen.getByRole('button', { name: /Enviar registro/i });
    fireEvent.click(button);

    await waitFor(() => {
      expect(registerMock).toHaveBeenCalled();
      expect(registerMock).toHaveBeenCalledWith(formValues);

      expect(pushMock).toHaveBeenCalled();
      expect(pushMock).toHaveBeenCalledWith(`/aviso/${formValues.correoElectronico}`);
    });
  });

  it('should show error if request status is not 200', async () => {
    const message = 'Error message';
    const registerMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'register').mockImplementation(registerMock);

    fillForm(formValues, { withChecks: true });

    const button = screen.getByRole('button', { name: /Enviar registro/i });
    button.click();

    expect(await screen.findByText(message)).toBeInTheDocument();
  });

  describe('Form validation', () => {
    it('should all fields are required', async () => {
      const button = screen.getByRole('button', { name: /Enviar registro/i });
      button.click();

      await waitFor(() => {
        expect(screen.getByText(/El tipo de documento es obligatorio/i)).toBeInTheDocument();
        expect(screen.getByText(/El número de documento es obligatorio/i)).toBeInTheDocument();
        expect(screen.getByText(/La fecha de expedición es obligatoria/i)).toBeInTheDocument();
        expect(screen.getByText(/El número de celular es obligatorio/i)).toBeInTheDocument();
        expect(screen.getByText(/El correo electrónico es obligatorio/i)).toBeInTheDocument();
        expect(screen.getByText(/La contraseña es obligatoria/)).toBeInTheDocument();
        expect(screen.getByText(/La confirmación de la contraseña es obligatoria/)).toBeInTheDocument();
        expect(screen.getByText(/Debe aceptar los términos y condiciones/i)).toBeInTheDocument();
        expect(screen.getByText(/Debe aceptar la política de tratamiento de datos/i)).toBeInTheDocument();
      });
    });

    it('should validate document number', async () => {
      const documentNumberInput = screen.getByLabelText(/Número de documento/i);

      expect(documentNumberInput.type).toBe('number');
    });

    it('should validate cellphone', async () => {
      const cellphoneInput = screen.getByLabelText(/Número de celular/i);

      expect(cellphoneInput.type).toBe('number');
    });

    it('should validate email', async () => {
      const emailInput = screen.getByLabelText(/Correo electrónico/i);

      expect(emailInput.type).toBe('email');
    });

    it('should validate password min length', async () => {
      const invalidFormValues = {
        ...formValues,
        contrasena: '12345',
      };

      fillForm(invalidFormValues, { withChecks: true });

      const button = screen.getByRole('button', { name: /Enviar registro/i });
      button.click();

      await waitFor(() => {
        expect(screen.getByText(/La contraseña debe tener entre 6-20 caracteres/i)).toBeInTheDocument();
      });
    });

    it('should validate password max length', async () => {
      const invalidFormValues = {
        ...formValues,
        contrasena: '1234567890123456789012345678901',
      };

      fillForm(invalidFormValues, { withChecks: true });

      const button = screen.getByRole('button', { name: /Enviar registro/i });
      button.click();

      await waitFor(() => {
        expect(screen.getByText(/La contraseña debe tener entre 6-20 caracteres/i)).toBeInTheDocument();
      });
    });
  });
});

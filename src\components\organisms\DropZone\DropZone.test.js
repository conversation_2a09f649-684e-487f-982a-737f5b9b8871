import React from 'react';
import {
  render, screen, fireEvent, waitFor,
} from '@testing-library/react';
import * as Recoil from 'recoil';
import * as fileAdmin from '@/utils/fileAdmin';
import * as userService from '@/services/user';
import DropZone from './DropZone';

describe('Tests DropZone', () => {
  const base64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
  const fileToBase64StringMock = jest.fn().mockImplementation(() => Promise.resolve(base64));
  jest.spyOn(fileAdmin, 'fileToBase64String').mockImplementation(fileToBase64StringMock);

  const userMock = {
    idUsuarioC: 'idUsuarioC',
    access_token: 'access_token',
  };

  jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);

  beforeEach(() => {
    const component = (
      <Recoil.RecoilRoot>
        <DropZone />
      </Recoil.RecoilRoot>
    );
    render(component);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render correctly', () => {
    expect(screen.getByText(/Arrastre los archivos o de click para agregar/i)).toBeInTheDocument();
  });

  it('should show unsupported file message', () => {
    const file = new File(['(⌐□_□)'], 'chucknorris.png', { type: 'image/png' });
    const input = screen.getByTestId('upload-input');

    fireEvent.change(input, { target: { files: [file] } });

    expect(screen.getByText(/Por favor remueva los archivos no soportados/i)).toBeInTheDocument();
    expect(screen.getByText(/No permitido/i)).toBeInTheDocument();
    expect(screen.queryAllByText(/Subir documentos a la plataforma/i)).toEqual([]);
  });

  it('should show valid files info', () => {
    const fileName = 'chucknorris.pdf';
    const file = new File(['(⌐□_□)'], fileName, { type: 'application/pdf' });
    const input = screen.getByTestId('upload-input');

    fireEvent.change(input, { target: { files: [file] } });

    expect(screen.getByText(fileName)).toBeInTheDocument();
    expect(screen.getByText(/12 Bytes/i)).toBeInTheDocument();
  });

  it('should show upload button is files are valid', () => {
    const fileName = 'chucknorris.pdf';
    const file = new File(['(⌐□_□)'], fileName, { type: 'application/pdf' });
    const input = screen.getByTestId('upload-input');

    fireEvent.change(input, { target: { files: [file] } });

    expect(screen.getByText(/Subir documentos a la plataforma/i)).toBeInTheDocument();
  });

  describe('should show file size', () => {
    it('in bytes', () => {
      const file = new File(['(⌐□_□)'], 'chucknorris.pdf', { type: 'application/pdf' });
      const input = screen.getByTestId('upload-input');

      fireEvent.change(input, { target: { files: [file] } });

      expect(screen.getByText(/12 Bytes/i)).toBeInTheDocument();
    });

    it('in kilobytes', () => {
      const file = new File(['a'.repeat('1024')], 'chucknorris.pdf', { type: 'application/pdf' });
      const input = screen.getByTestId('upload-input');

      fireEvent.change(input, { target: { files: [file] } });

      expect(screen.getByText(/1 KB/i)).toBeInTheDocument();
    });

    it('in megabytes', () => {
      const file = new File(['a'.repeat(1024 * 1024)], 'chucknorris.pdf', { type: 'application/pdf' });
      const input = screen.getByTestId('upload-input');

      fireEvent.change(input, { target: { files: [file] } });

      expect(screen.getByText(/1 MB/i)).toBeInTheDocument();
    });
  });

  it('should allow remove invalid file', () => {
    const fileName = 'chucknorris.png';
    const file = new File(['(⌐□_□)'], fileName, { type: 'image/png' });
    const input = screen.getByTestId('upload-input');

    fireEvent.change(input, { target: { files: [file] } });

    const btn = screen.getByTestId(`remove-file-${fileName}`);

    fireEvent.click(btn);

    expect(screen.queryAllByText(fileName)).toEqual([]);
  });

  it('should allow remove valid file', () => {
    const fileName = 'chucknorris.pdf';
    const file = new File(['(⌐□_□)'], fileName, { type: 'application/pdf' });
    const input = screen.getByTestId('upload-input');

    fireEvent.change(input, { target: { files: [file] } });

    const btn = screen.getByTestId(`remove-file-${fileName}`);

    fireEvent.click(btn);

    expect(screen.queryAllByText(fileName)).toEqual([]);
  });

  it('should call uploadDocumentsG64 when click on "Subir documentos a la plataforma"', async () => {
    const uploadDocumentsG64Mock = jest.fn().mockResolvedValue({
      status: 200,
      data: {
        data: [{
          observacion: 'OK',
        }],
      },
    });
    jest.spyOn(userService, 'uploadDocumentsG64').mockImplementation(uploadDocumentsG64Mock);

    const fileName = 'chucknorris.pdf';
    const file = new File(['(⌐□_□)'], fileName, { type: 'application/pdf' });
    const input = screen.getByTestId('upload-input');

    fireEvent.change(input, { target: { files: [file] } });

    const btn = await screen.findByText(/subir documentos a la plataforma/i);

    fireEvent.click(btn);

    const docs = [{
      nombreArchivo: fileName,
      cantidadFirmas: 1,
      idUsuario: userMock.idUsuarioC,
      archivo64: base64.substring(base64.indexOf(',') + 1),
    }];

    await waitFor(() => {
      expect(uploadDocumentsG64Mock).toHaveBeenCalled();
      expect(uploadDocumentsG64Mock).toHaveBeenCalledWith(docs, userMock.access_token);
      expect(screen.getByText(/Documentos cargados correctamente/i)).toBeInTheDocument();
    });
  });

  it('should show error message if any "observacion" in document is not "OK" in uploadDocumentsG64 response', async () => {
    const fileName = 'chucknorris.pdf';
    const errorInfo = 'ERROR_INFO';
    const uploadDocumentsG64Mock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: [{
          observacion: errorInfo,
          nombreArchivo: fileName,
        }],
      },
    }));
    jest.spyOn(userService, 'uploadDocumentsG64').mockImplementation(uploadDocumentsG64Mock);

    const file = new File(['(⌐□_□)'], fileName, { type: 'application/pdf' });
    const input = screen.getByTestId('upload-input');

    fireEvent.change(input, { target: { files: [file] } });

    const btn = await screen.findByText(/subir documentos a la plataforma/i);

    fireEvent.click(btn);

    await waitFor(() => {
      expect(screen.getByText(/Error al subir documentos a la plataforma/i)).toBeInTheDocument();
      expect(screen.getByText(errorInfo)).toBeInTheDocument();
    });
  });
});

.backdrop {
  position: fixed;
  z-index: 1300;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--modal-backdrop-color);
  inset: 0;
}

.backdrop:not(.is-playground) {
  animation: fadeInModal 0.2s ease-in forwards;
}

.backdrop.on-fade-out {
  animation: fadeOutModal 0.2s ease-in forwards;
}

.modal {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  overflow: auto;
  border-radius: var(--border-radius-sm);
  max-width: 80vw;
  max-height: 80vh;
  margin-left: 10vw;
}

.modal:not(.not-padded) {
  padding: 16px 32px;
}

:not(.is-playground) > .modal {
  animation: fadeInModal 0.4s ease-in forwards;
  opacity: 0;
}

.backdrop.on-fade-out .modal {
  animation: fadeOutModal 0.4s ease-in forwards;
}

.heading {
  display: flex;
  justify-content: space-between;
}

.not-padded .heading {
  padding: 16px 32px;
}

.is-playground {
  position: relative;
  width: calc(50% - 15px);
  border: var(--border-width-thick) dashed var(--color-primary);
}

.type-primary {
  background-color: var(--color-primary);
}

.type-secondary {
  background-color: var(--background-color-primary);
}

.type-tertiary {
  background-color: var(--color-brand-alabaster);
  border: var(--border-width-thick) solid var(--color-primary);
}

@media (min-width: 992px) {
  .modal {
    max-width: var(--modal-desktop-max-width);
    height: var(--modal-desktop-height);
  }
}

@keyframes fadeInModal {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes fadeOutModal {
  from {
    opacity: 1;
  }

  to {
    opacity: 0;
  }
}

.is-inline {
  height: max-content;
}

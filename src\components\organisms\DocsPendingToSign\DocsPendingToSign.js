import React, {
  useEffect, useMemo, useState,
} from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';
import { useRecoilState } from 'recoil';
import Grid from '@mui/material/Grid';
import {
  getDocsPendingToSign,
  signDocuments,
  validarOrdenFirma,
  deletePendingDocuments,
} from '@/services/user';
import appStore from '@/assets/images/appStore.png';
import playStore from '@/assets/images/googlePlay.png';
import icon from '@/assets/images/document-error-flat.png';
import { userState } from '@/recoil/atoms';
import { useSnackbar } from 'notistack';
import Modal from '@/components/atoms/Modal';
import { useInfiniteQuery } from 'react-query';
import Paragraph from '@/components/atoms/Paragraph';
import dayjs from 'dayjs';
import Icon from '@/components/atoms/Icon';
import Tooltip from '@/components/atoms/Tooltip';
import pluralize from '@/utils/pluralize';
import getShortPersonName from '@/utils/getShortPersonName';
import SolicitarFirma from '@/components/organisms/SolicitarFirma';
import StatesHandler from '@/components/organisms/StateHandler';
import Document from '@/components/molecules/Document';
import Heading from '@/components/atoms/Heading';
import Spacer from '@/components/layout/Spacer';
import Button from '@/components/atoms/Button';
import { saveStorage } from '@/utils/utils';
import { SIGN_TYPES } from '@/utils/constants';
import styles from './DocsPendingToSign.module.css';

import 'dayjs/locale/es';

dayjs.locale('es');

export default function DocsPendingToSign() {
  const router = useRouter();
  const [user] = useRecoilState(userState);
  const [state, setState] = useState({});
  const { enqueueSnackbar } = useSnackbar();
  const [selectedDocs, setSelectedDocs] = useState([]);
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  const {
    data,
    isLoading,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    refetch,
  } = useInfiniteQuery('docs-pending-to-sign', async ({ pageParam = 0 }) => {
    // Validar que tenemos los datos necesarios
    if (!user?.idUsuarioC || !user?.access_token) {
      throw new Error('User data not ready');
    }

    const {
      data: pendingDocs,
      ...response
    } = await getDocsPendingToSign(user.idUsuarioC, pageParam, 12, user?.access_token);
    let nextPage = null;
    if (pendingDocs?.totalPages > 1 && pendingDocs?.last === false) {
      nextPage = (pendingDocs?.number || 0) + 1;
    }

    return { ...response, data: pendingDocs, nextPage };
  }, {
    enabled: !!user?.idUsuarioC && !!user?.access_token && !!user?.email, // Condición más estricta
    getNextPageParam: (lastPage) => lastPage.nextPage,
    retry: (failureCount, error) => {
      // No reintentar si es error de autenticación
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: 1000,
  });

  const infoDocument = () => {
    if (selectedDocs.length === 1) {
      const selectedDoc = selectedDocs[0];

      if (selectedDoc?.esSolicitud === 'SI') {
        const requestInfo = {
          fechaSolicitud: selectedDoc?.fechaRegistro,
          propietario: {
            nombreCompleto: selectedDoc?.propietario,
            numeroDocumento: selectedDoc?.numeroDocumentoPropietario,
          },
          token: null,
          fechaVigencia: selectedDoc?.fechaVencimiento,
          archivos: [
            {
              idArchivo: selectedDoc?.idArchivo,
              nombreArchivo: selectedDoc?.nombreArchivo,
              ip: selectedDoc?.ip,
            },
          ],
        };

        saveStorage('element', requestInfo);
        saveStorage('docsToShow', requestInfo.archivos);
        return router.push('/informacion-documento');
      }

      saveStorage('docs', selectedDocs[0]);
      saveStorage('idDoc', selectedDocs[0].idArchivo);
      return router.push('/informacion');
    }
    setState({
      ...state,
      haveError: true,
      errorMessage: {
        ...state.errorMessage,
        header: 'Solo es posible ver información de un documento',
        content: 'Para ver información de documentos solo es permitido seleccionar uno a la vez.',
        icon,
      },
    });
  };

  const solicitarFirmas = () => {
    if (selectedDocs.length > 0) {
      setState({
        ...state,
        isMultiple: true,
        files: selectedDocs,
      });
    } else {
      setState({
        ...state,
        haveError: true,
        errorMessage: {
          ...state.errorMessage,
          header: 'Error en firma de documentos',
          content: 'No se han seleccionado los documentos para firma',
          icon,
        },
      });
    }
  };

  const esDocumentoSingle = (doc) => {
    const esSoloMio = (
      doc.esSolicitud === 'NO'
      || doc.esSolicitud === false
      || doc.esSolicitud == null
      || doc.esSolicitud === undefined
    );
    const esTipoSingle = doc.tipoFirma === 'SINGLE';
    const esMultiplePeroSolo = (
      doc.tipoFirma === 'MULTIPLE'
      && (doc.firmasRequeridas === 1 || doc.firmasRequeridas == null)
    );
    const soyPropietarioSinOtros = (
      doc.propietario === user?.email
      && doc.tipoFirma !== 'OTHERS'
      && (doc.totalFirmas == null || doc.totalFirmas <= 1)
    );

    const resultado = esSoloMio
      || esTipoSingle
      || esMultiplePeroSolo
      || soyPropietarioSinOtros;

    // LOG para debugging
    console.log(`📋 DocsPendingToSign - Análisis documento ${doc.idArchivo}:`, {
      nombreArchivo: doc.nombreArchivo,
      esSolicitud: doc.esSolicitud,
      tipoFirma: doc.tipoFirma,
      firmasRequeridas: doc.firmasRequeridas,
      propietario: doc.propietario,
      userEmail: user?.email,
      RESULTADO_FINAL: resultado ? '✅ ES SINGLE' : '❌ REQUiere VALIDACIÓN',
    });

    return resultado;
  };

  // NUEVA FUNCIÓN: Validar orden para múltiples documentos
  const validarOrdenMultiplesDocumentos = async (documentos) => {
    if (!documentos?.length || !user?.email || !user?.access_token) {
      return true;
    }

    try {
      console.log('🔍 Validando orden para múltiples documentos:', documentos.map((d) => d.idArchivo));
      // Filtrar usando la función mejorada
      const documentosConOrden = documentos.filter((doc) => {
        const esSingle = esDocumentoSingle(doc);
        console.log(`📋 ${doc.nombreArchivo}:`, {
          esSingle: esSingle ? '✅' : '❌',
          requiereValidacion: !esSingle ? '✅' : '❌',
        });
        return !esSingle; // Solo validar los que NO son SINGLE
      });

      console.log('📑 Documentos que requieren validación:', documentosConOrden.length);
      console.log('📄 Documentos SINGLE (sin validación):', documentos.length - documentosConOrden.length);

      // Si no hay documentos que requieran validación, permitir continuar
      if (documentosConOrden.length === 0) {
        console.log('✅ TODOS LOS DOCUMENTOS SON SINGLE - PERMITIENDO CONTINUAR');
        return true;
      }

      const validaciones = await Promise.all(
        documentosConOrden.map(async (doc) => {
          const response = await validarOrdenFirma(
            doc.idArchivo,
            user.email,
            user.access_token,
          );
          return {
            idArchivo: doc.idArchivo,
            puedeFirmar: response.status === 200 && response.data?.data?.puedeFirmar,
            response,
          };
        }),
      );

      console.log('Resultados validación múltiples documentos:', validaciones);

      const documentosInvalidos = validaciones.filter((v) => !v.puedeFirmar);

      if (documentosInvalidos.length > 0) {
        console.log('Documentos que no puede firmar:', documentosInvalidos);
        await router.push('/no-es-tu-turno');
        return false;
      }

      console.log('✅ TODOS LOS DOCUMENTOS PUEDEN SER FIRMADOS');
      return true;
    } catch (error) {
      console.error('Error al validar orden múltiples documentos:', error);
      return true; // En caso de error, permitir continuar
    }
  };

  const firmarDocumentos = async () => {
    if (selectedDocs.length > 0) {
      // NUEVA VALIDACIÓN: Verificar orden antes de proceder
      const puedeFiremar = await validarOrdenMultiplesDocumentos(selectedDocs);
      if (!puedeFiremar) {
        return; // Si no puede firmar, la función ya manejó la redirección
      }

      setState((prev) => ({
        ...prev,
        isLoading: true,
      }));

      const array = [];
      let requiredSigns = 0;
      for (let index = 0; index < selectedDocs.length; index += 1) {
        array[index] = {
          idArchivo: selectedDocs[index].idArchivo,
          idUsuario: user.idUsuarioC,
        };
        requiredSigns = Math.max(requiredSigns, selectedDocs[index].firmasRequeridas);
      }

      signDocuments(array, user?.access_token)
        .then((response) => {
          if (response.status === 200) {
            saveStorage('docsToSign', array);
            localStorage.setItem('requiredSigns', `${requiredSigns}`);
            return router.push('/multiple-firma');
          }
          return setState({
            ...state,
            isLoading: false,
            haveError: true,
            errorMessage: {
              ...state.errorMessage,
              header: 'Error en firma de documentos',
              content: response.data.mensaje,
              icon,
            },
          });
        })
        .catch((error) => console.log(error));
    } else {
      setState({
        ...state,
        isLoading: false,
        haveError: true,
        errorMessage: {
          ...state.errorMessage,
          header: 'Error en selección de documentos',
          content: 'No se han seleccionado los documentos para firma',
          icon,
        },
      });
    }
  };

  const handleError = async () => {
    setSelectedDocs([]);
    await refetch({ refetchPage: () => true });
    setState({
      ...state,
      haveError: false,
      isMultiple: false,
      isLoading: false,
    });
  };

  // Comprobar que un usuario esta registrado en la parte móvil
  useEffect(() => {
    setState({
      data: [],
      selected: true,
      selectedRowId: null,
      c: '#ccc',
      currentRow: {},
      showPopUp: false,
      botonActivo: false,
      show: false,
    });
    if (user.nombreUsuario == null) {
      setState({
        ...state,
        show: true,
        showPopUp: true,
        botonActivo: true,
      });
    }
  }, []);

  // cerrar popup
  const handleClose = () => {
    setState({
      ...state,
      show: false,
    });
  };

  const toggleDoc = (doc) => () => {
    setSelectedDocs((prev) => {
      if (prev.some((doc2) => doc2?.idArchivo === doc?.idArchivo)) {
        return [...prev.filter((prevDoc) => prevDoc?.idArchivo !== doc?.idArchivo)];
      }

      return [...prev, doc];
    });
  };

  const parsedData = useMemo(() => {
    if (!data) return [];
    const groupedByDate = data?.pages?.reduce((grouped, response) => ({
      ...grouped,
      ...response?.data?.content?.reduce((acc, doc) => {
        const dateFormat = 'DD MMMM, YYYY';

        let parsedDate = dayjs(doc.fechaRegistro).format(dateFormat);
        if (parsedDate === dayjs().format(dateFormat)) {
          parsedDate = 'Hoy';
        }

        if (parsedDate.slice(-4) === dayjs().format('YYYY')) {
          // eslint-disable-next-line prefer-destructuring
          parsedDate = parsedDate.split(',')[0];
        }

        return {
          ...acc,
          [parsedDate]: [
            ...grouped[parsedDate]
              ?.filter(
                ({ hashArchivo }) => !(acc[parsedDate]?.map((p) => p.hashArchivo) || [])
                  .includes(hashArchivo),
              ) || [],
            ...acc[parsedDate] || [],
            { ...doc },
          ],
        };
      }, {}),
    }), {});

    return Object?.keys(groupedByDate || {}).map((date) => (
      <div key={date}>
        <Spacer.Vertical size="sm" />
        <Paragraph size="xs">{date}</Paragraph>
        <Spacer.Vertical size="sm" />
        <div className={styles['grid-card']}>
          {groupedByDate[date].map((doc) => (
            <Document
              key={doc.idArchivo}
              onClick={toggleDoc(doc)}
              border={doc?.esSolicitud === 'SI' ? 'warning' : 'primary'}
              hasCheck
              moreInfo={{
                missingSigns: ![SIGN_TYPES.UNSIGNED, SIGN_TYPES.ME].includes(doc?.tipoFirma)
                  ? `${doc?.totalFirmas} de ${doc?.firmasRequeridas} ${pluralize('firma', doc?.firmasRequeridas)}`
                  : <Paragraph size="xs" key={`p-${doc.idArchivo}`}>&nbsp;</Paragraph>,
                ...(doc?.esSolicitud === 'SI' ? { propietario: getShortPersonName(doc?.propietario) } : {}),
              }}
              isChecked={selectedDocs
                .some((selectedDoc) => selectedDoc?.idArchivo === doc?.idArchivo)}
              {...Object.keys(doc)
                .filter((key) => ['nombreArchivo', 'fechaRegistro', 'tipoFirma', 'descripcion', 'esSolicitud', 'propietario', 'firmasRequeridas'].includes(key))
                .reduce((acc, key) => ({ ...acc, [key]: doc[key] }), {})
                            }
            />
          ))}
        </div>
      </div>
    )) || [];
  }, [data, selectedDocs]);

  const isSignDisabled = useMemo(() => !selectedDocs.length || selectedDocs.some((doc) => doc?.tipoFirma === SIGN_TYPES.OTHERS && doc?.esSolicitud === 'NO'), [selectedDocs]);

  const isMultipleSignDisabled = useMemo(
    () => !selectedDocs.length
      || !selectedDocs.every(
        (doc) => doc?.tipoFirma === SIGN_TYPES.UNSIGNED,
      ),
    [selectedDocs],
  );

  const isDeleteDisabled = useMemo(() => {
    if (!selectedDocs.length) return true;

    // Permitir eliminar documentos ORIS
    return false;
  }, [selectedDocs]);
  const deleteSelectedDocs = async () => {
    await Promise.all(selectedDocs.map(async (doc) => {
      try {
        const response = await deletePendingDocuments(
          doc.idArchivo,
          user.idUsuarioC,
          user.access_token,
        );

        const { status, data: { mensaje } } = response;

        if (status === 200) {
          enqueueSnackbar(
            `Archivo ${doc.nombreArchivo} fue eliminado ${mensaje}`,
            { variant: 'success' },
          );
        } else {
          enqueueSnackbar(
            `archivo: ${doc.nombreArchivo} ${mensaje}`,
            { variant: 'error' },
          );
        }
      } catch (error) {
        enqueueSnackbar(
          `Error al eliminar ${doc.nombreArchivo}`,
          { variant: 'error' },
        );
      }
    }));
    setSelectedDocs([]);
    setShowDeleteModal(false);
    await refetch({ refetchPage: () => true });
  };

  useEffect(() => {
    if (isLoading) return;

    if (isError || data?.pages?.some((d) => d?.status !== 200)) {
      enqueueSnackbar(
        'Ocurrió un error al cargar los documentos pendientes de firma.',
        { variant: 'error' },
      );
    }
  }, [isError, data, isLoading, enqueueSnackbar]);

  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const source = urlParams.get('source');
    const auto = urlParams.get('auto');

    if (source === 'tyc' && auto === 'true') {
      setTimeout(() => {
        refetch({ refetchPage: () => true });
      }, 1500);

      // Limpiar los parámetros de la URL
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [router.asPath, refetch, enqueueSnackbar]);

  return (
    <>
      {
                showDeleteModal && (
                <Modal
                  onClose={() => setShowDeleteModal(false)}
                  title="Eliminar documentos"
                  isInline
                  type="secondary"
                >
                  <Paragraph>
                    ¿Está seguro que desea eliminar los documentos seleccionados?
                  </Paragraph>
                  <Spacer.Vertical size="sm" />
                  <ul className={styles['delete-details']}>
                    {selectedDocs.slice(0, 5).map((doc) => (
                      <li key={doc.idArchivo}>
                        <Paragraph size="xs">{doc.nombreArchivo}</Paragraph>
                      </li>
                    ))}
                  </ul>
                  {selectedDocs.length > 5 && (
                  <Paragraph size="xs">
                    {selectedDocs.length - 5}
                    {' '}
                    más
                  </Paragraph>
                  )}
                  <Spacer.Vertical size="sm" />
                  <div style={{ textAlign: 'right' }}>
                    <Button
                      type="base"
                      onClick={() => setShowDeleteModal(false)}
                      isInline
                    >
                      Cancelar
                    </Button>
                    <Spacer.Horizontal size="sm" />
                    <Button
                      onClick={deleteSelectedDocs}
                      type="danger"
                      size="sm"
                      disabled={!selectedDocs.length}
                      isInline
                    >
                      Eliminar
                    </Button>
                  </div>
                </Modal>
                )
            }
      <StatesHandler
        handleErrorButton={handleError}
        state={{ ...state, isLoading: isLoading || state?.isLoading }}
      >
        <SolicitarFirma handleClick={handleError} documentos={state}>
          <div className={styles['div-sticky']}>
            <Heading>Documentos pendientes de firma</Heading>

            <div className={styles['actions-container']}>
              {selectedDocs.length > 0 && (
                <Paragraph size="xs">
                  {`${selectedDocs.length} ${pluralize('documento', selectedDocs.length)} ${pluralize('seleccionado', selectedDocs.length)}`}
                </Paragraph>
              )}
              <Tooltip title="Firmar">
                <Icon
                  onClick={firmarDocumentos}
                  isClickable
                  isDisable={isSignDisabled}
                  name="pen"
                  size="md"
                  background="muted"
                  color="inverted"
                  data-testid="sign-button"
                />
              </Tooltip>
              <Tooltip title="Multiple Firma">
                <Icon
                  onClick={solicitarFirmas}
                  isDisable={isMultipleSignDisabled}
                  isClickable
                  name="multipleAccount"
                  size="md"
                  border="primary"
                  color="primary"
                  data-testid="multiple-sign-button"
                />
              </Tooltip>
              <Tooltip title="Información">
                <Icon
                  onClick={infoDocument}
                  isDisable={!selectedDocs.length}
                  isClickable
                  name="information"
                  size="md"
                  border="shinyShamrock"
                  color="shinyShamrock"
                  data-testid="information-button"
                />
              </Tooltip>
              <Tooltip title="Eliminar">
                <Icon
                  onClick={() => setShowDeleteModal(true)}
                  isDisable={isDeleteDisabled}
                  isClickable
                  name="trash"
                  size="md"
                  border="danger"
                  color="danger"
                  data-testid="delete-button"
                />
              </Tooltip>
              <Tooltip title="Actualizar">
                <Icon
                  onClick={() => refetch({ refetchPage: () => true })}
                  name="refresh"
                  size="md"
                  border="base"
                />
              </Tooltip>
            </div>
          </div>

          {parsedData}

          {hasNextPage && (
            <div style={{ textAlign: 'center' }}>
              <Tooltip title="Cargar más">
                <Icon
                  name="angleDown"
                  isClickable
                  onClick={() => fetchNextPage()}
                  isDisable={isFetchingNextPage}
                  border="base"
                />
              </Tooltip>
            </div>
          )}
        </SolicitarFirma>
      </StatesHandler>

      {/* Popup cuando no se ha registrado en la app */}
      {(state.showPopUp && state?.show) && (
        <Modal
          onClose={handleClose}
          type="tertiary"
          title="Debes completar tu registro"
          isInline
        >
          <Paragraph size="xs">
            Para continuar con la validación de tu usuario, es necesario
            que descargues la aplicación móvil de Firmese. Disponible en
            nuestras plataformas.
          </Paragraph>
          <Grid container justifyContent="flex-end" spacing={2}>
            <Grid item>
              <a
                target="_blank"
                href="https://play.google.com/store/apps/details?id=se.firme.app.android.firmese&hl=es_CO&gl=US"
                style={{ cursor: 'pointer' }}
                rel="noreferrer"
              >
                <Image alt="Play Store" src={playStore} width={135} height={40} />
              </a>
            </Grid>
            <Grid item>
              <a
                target="_blank"
                href="https://apps.apple.com/co/app/id1568786537"
                style={{ cursor: 'pointer' }}
                rel="noreferrer"
              >
                <Image alt="App Store" src={appStore} width={135} height={40} />
              </a>
            </Grid>
          </Grid>
        </Modal>
      )}
    </>
  );
}

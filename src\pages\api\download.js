import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function downloadRoute(req, res) {
  try {
    const { fileID } = req.query;
    const { data } = await fetchApi.post(
      `/validacion/registro/download/${fileID}`,
      undefined,
      {
        headers: getDefaultHeaders(req),
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(downloadRoute);

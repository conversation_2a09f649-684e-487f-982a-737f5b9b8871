import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';

import Icon from '@/components/atoms/Icon';
import Paragraph from '@/components/atoms/Paragraph';

import Spacer from '@/components/layout/Spacer';
import { options } from './constants';
import styles from './IconLabel.module.css';

const handleClick = ({ onClick }) => (event) => {
  onClick(event);
};

const sizesMap = {
  sm: {
    icon: 'sm',
    paragraph: 'xs',
  },
  md: {
    icon: 'md',
    paragraph: 'sm',
  },
};

export function IconLabel({
  getStyles,
  icon,
  label,
  onClick,
  isClickable,
  direction,
  isActive,
  size,
}) {
  return (
    <div
      className={getStyles('icon-label', {
        'is-clickable': isClickable || !!onClick,
        [`direction-${direction}`]: direction,
      })}
      onClick={onClick ? handleClick({ onClick }) : undefined}
    >
      <Icon
        name={icon}
        color={isActive ? 'primary' : 'base'}
        background={isActive ? 'highlight' : 'transparent'}
        size={sizesMap[size].icon}
      />
      {direction === 'horizontal' && <Spacer.Horizontal size="xs" />}
      <Paragraph
        size={sizesMap[size].paragraph}
        color={isActive ? 'primary' : 'base'}
        isInline
      >
        {label}
      </Paragraph>
    </div>
  );
}

IconLabel.propTypes = {
  getStyles: PropTypes.func.isRequired,
  icon: PropTypes.oneOf(options.icons).isRequired,
  label: PropTypes.string.isRequired,
  direction: PropTypes.oneOf(['vertical', 'horizontal']),
  isClickable: PropTypes.bool,
  onClick: PropTypes.func,
  isActive: PropTypes.bool,
  size: PropTypes.oneOf(options.sizes),
};

IconLabel.defaultProps = {
  direction: 'vertical',
  size: 'sm',
  isActive: false,
  getStyles: () => ({}),
  onClick: () => { /* */ },
};

export default withStyles(styles)(IconLabel);

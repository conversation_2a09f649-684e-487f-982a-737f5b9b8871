import React from 'react';
import { useRouter } from 'next/router';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';
import CenteredContent from '@/components/layout/CenteredContent';
import Icon from '@/components/atoms/Icon';
import Container from '@/components/layout/Container';

export default function NoEsTuTurno() {
  const router = useRouter();

  const handleVolver = () => {
    router.push('/');
  };

  return (
    <Container>
      <CenteredContent>
        <Icon name="clock" size="2xl" color="warning" />
        <Spacer.Vertical size="md" />
        <Heading size="lg" isCentered>
          No es tu turno para firmar
        </Heading>
        <Spacer.Vertical size="sm" />
        <Paragraph size="md" isCentered color="muted">
          Este documento tiene un orden secuencial de firma y aún no es tu turno.
        </Paragraph>
        <Spacer.Vertical size="xs" />
        <Paragraph size="sm" isCentered color="muted">
          Debes esperar a que el firmante anterior complete su proceso de firma
          para poder continuar con el tuyo.
        </Paragraph>
        <Spacer.Vertical size="lg" />
        <Button
          type="primary"
          onClick={handleVolver}
        >
          Volver al inicio
        </Button>
      </CenteredContent>
    </Container>
  );
}

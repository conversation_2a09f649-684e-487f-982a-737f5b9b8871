.login {
    width: 100%;
    max-width: var(--container-sm-max-width);
}

.forgot-password {
    margin: auto;
    text-align: center;
}

.actions {
    display: block;
}

.actions a {
    display: block;
}

@media (max-width: 992px) {
    .actions a:last-child {
        margin-top: calc(var(--spacing-md) * 1px);
    }
}

@media (min-width: 992px) {
    .actions {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: calc(var(--spacing-md) * 1px);
        align-items: center;
    }
}
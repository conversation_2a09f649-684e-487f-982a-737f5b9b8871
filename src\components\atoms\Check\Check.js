import React, { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { options as iconOptions } from '@/components/atoms/Icon/constants';

import withStyles from '@/hocs/withStyles';
import Icon from '@/components/atoms/Icon';

import styles from './Check.module.css';

export const Check = React.forwardRef(({
  isChecked,
  getStyles,
  onChange,
  onBlur,
  name,
  label,
  size,
  hasError,
  helperText,
  isCentered,
}, ref) => {
  const [checked, setChecked] = useState(isChecked);

  useEffect(() => {
    setChecked(isChecked);
  }, [isChecked]);

  const handleChange = (cb) => (e) => {
    setChecked(e?.target?.checked ?? !checked);
    cb(e);
  };

  return (
    <>
      <label className={getStyles('check-container', {
        'is-centered': isCentered,
      })}
      >
        <Icon
          name={checked ? 'check' : 'checkEmpty'}
          className={getStyles('check')}
          size={size}
          isClickable
        />
        <input
          type="checkbox"
          checked={checked}
          className={getStyles('checkbox', {
            'has-error': hasError,
          })}
          onChange={handleChange(onChange)}
          onBlur={onBlur}
          name={name}
          ref={ref}
        />
        <span className={getStyles('checkbox-label')}>{label}</span>
      </label>
      <p
        className={getStyles('helper-text', {
          'has-error': hasError,
          'is-centered': isCentered,
        })}
      >
        {helperText}
      </p>
    </>
  );
});

Check.propTypes = {
  getStyles: PropTypes.func.isRequired,
  isChecked: PropTypes.bool,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  name: PropTypes.string,
  label: PropTypes.oneOfType([PropTypes.node, PropTypes.string]),
  size: PropTypes.oneOf(iconOptions.sizes),
  hasError: PropTypes.bool,
  helperText: PropTypes.string,
  isCentered: PropTypes.bool,
};

Check.defaultProps = {
  getStyles: () => ({}),
  isChecked: false,
  onChange: () => { /* */ },
  onBlur: () => { /* */ },
  name: '',
  label: '',
  size: 'xs',
  hasError: false,
  helperText: '',
  isCentered: false,
};

export default withStyles(styles)(Check);

.grid-card {
    display: grid;
    gap: var(--doc-card-gap);
    grid-auto-rows: var(--doc-card-height);
    grid-template-columns: repeat(auto-fill, minmax(min(100%, var(--doc-card-min-width)), 1fr));
    margin-bottom: var(--doc-card-margin-bottom);
}

.actions-container {
    border: var(--border-width-thin) solid var(--color-base-transparent);
    height: 3rem;
    display: flex;
    align-items: center;
    gap: .5rem;
    justify-content: flex-end;
}

.delete-details {
    margin-left: 1rem;
    list-style-type: "\2705";
}

.div-sticky{
    background-color: var(--color-base-white);
    position: -webkit-sticky;
    position: sticky;
    top: 80px;
    padding-top: 10px;
}

@media (max-width: 600px) {
    .div-sticky{
        top: 65px;
    }
}

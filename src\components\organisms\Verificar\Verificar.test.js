import React from 'react';
import {
  fireEvent, render, waitFor, act,
} from '@testing-library/react';
import * as fileAdminUtils from '@/utils/fileAdmin';
import { checkAllValuesInDom } from '@/utils/forTesting';
import * as userService from '@/services/user';
import * as Notistack from 'notistack';
import Verificar from './index';

jest.mock('@/components/organisms/DocumentInfo', () => ({
  __esModule: true,
  default: ({ ...props }) => `DocumentInfo mock ${JSON.stringify(props)}`,
}));
describe('Tests Verificar', () => {
  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show snackbar when click on "Verificar" if a document is not uploaded', () => {
    const { getByText } = render(<Verificar />);
    const button = getByText('Verificar');
    button.click();

    expect(enqueueSnackbarMock).toHaveBeenCalledTimes(1);
    expect(enqueueSnackbarMock).toHaveBeenCalledWith('seleccione un documento', {
      variant: 'error',
    });
  });

  it('should call verifyDocument when click on "Verificar" if a document is uploaded', async () => {
    const fileName = 'chucknorris.pdf';
    const file = new File(['(⌐□_□)'], fileName, { type: 'application/pdf' });

    const verifyDocumentResponseMock = {
      nombreArchivo: fileName,
      fechaRegistroStr: '2020-01-01',
      cantidadConsultas: 1,
      firmas: [],
      ip: '***********',
      hashArchivo: 'hash',
      propietario: {},
      idArchivo: 'idArchivo',
    };
    const verifyDocumentMock = jest.fn().mockResolvedValue({
      status: 200,
      data: {
        data: verifyDocumentResponseMock,
      },
    });
    jest.spyOn(userService, 'verifyDocument').mockImplementation(verifyDocumentMock);

    const base64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
    const fileToBase64StringMock = jest.fn().mockResolvedValue(base64);
    jest.spyOn(fileAdminUtils, 'fileToBase64String').mockImplementation(fileToBase64StringMock);

    const { getByText, getByTestId, container } = render(<Verificar />);

    await act(() => {
      const fileInput = getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });
    });

    const doc = {
      nombreArchivo: fileName,
      cantidadFirmas: 1,
      idUsuario: 0,
      archivo64: base64.substring(base64.indexOf(',') + 1),
    };

    await waitFor(() => {
      expect(fileToBase64StringMock).toHaveBeenCalledTimes(1);
      expect(fileToBase64StringMock).toHaveBeenCalledWith(file);
    });

    await act(() => {
      const button = getByText('Verificar');
      button.click();
    });

    await waitFor(() => {
      expect(verifyDocumentMock).toHaveBeenCalledTimes(1);
      expect(verifyDocumentMock).toHaveBeenCalledWith(doc);
    });

    await waitFor(() => {
      expect(container).toHaveTextContent('DocumentInfo mock');
      const excludedKeys = ['firmas', 'propietario'];
      checkAllValuesInDom(verifyDocumentResponseMock, excludedKeys, { exact: false });
    });
  });

  it('should show error message if verifyDocument response status is not 200', async () => {
    const fileName = 'chucknorris.pdf';
    const file = new File(['(⌐□_□)'], fileName, { type: 'application/pdf' });

    const message = 'error message';
    const verifyDocumentMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'verifyDocument').mockImplementation(verifyDocumentMock);

    const base64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
    const fileToBase64StringMock = jest.fn().mockImplementation(() => Promise.resolve(base64));
    jest.spyOn(fileAdminUtils, 'fileToBase64String').mockImplementation(fileToBase64StringMock);

    const { getByText, getByTestId, findByText } = render(<Verificar />);

    await act(() => {
      const fileInput = getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });
    });

    await waitFor(() => {
      expect(fileToBase64StringMock).toHaveBeenCalledTimes(1);
    });

    await act(() => {
      const button = getByText('Verificar');
      button.click();
    });

    expect(await findByText(message)).toBeInTheDocument();
  });

  it('should catch verifyDocument request exception', async () => {
    const fileName = 'chucknorris.pdf';
    const file = new File(['(⌐□_□)'], fileName, { type: 'application/pdf' });

    const error = new Error('Error');
    const verifyDocumentMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'verifyDocument').mockImplementation(verifyDocumentMock);

    const base64 = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII=';
    const fileToBase64StringMock = jest.fn().mockImplementation(() => Promise.resolve(base64));
    jest.spyOn(fileAdminUtils, 'fileToBase64String').mockImplementation(fileToBase64StringMock);

    const logMock = jest.fn();
    jest.spyOn(console, 'log').mockImplementation(logMock);

    const { getByText, getByTestId } = render(<Verificar />);

    await act(() => {
      const fileInput = getByTestId('file-input');
      fireEvent.change(fileInput, { target: { files: [file] } });
    });

    await waitFor(() => {
      expect(fileToBase64StringMock).toHaveBeenCalledTimes(1);
    });

    await act(() => {
      const button = getByText('Verificar');
      button.click();
    });

    await waitFor(() => {
      expect(logMock).toHaveBeenCalledTimes(1);
      expect(logMock).toHaveBeenCalledWith(error);
    });
  });
});

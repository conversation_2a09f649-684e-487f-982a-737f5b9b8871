import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function enviarOtpEmailRoute(req, res) {
  try {
    const { id, tipo } = req.body;

    const { data } = await fetchApi.post(
      `/validacion/registro/enviar-otp-email?id=${id}&tipo=${tipo}`,
      {
        headers: getDefaultHeaders(req),
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(enviarOtpEmailRoute);

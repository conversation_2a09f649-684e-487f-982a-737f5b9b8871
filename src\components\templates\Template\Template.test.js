import React from 'react';
import { render } from '@testing-library/react';
import * as Recoil from 'recoil';
import mockAxios from 'axios';
import * as Router from 'next/router';
import Template from './Template';

jest.mock('@/components/molecules/Copyright', () => ({
  __esModule: true,
  default: jest.fn(() => 'Copyright mock'),
}));

describe('Tests Layout', () => {
  mockAxios.post.mockImplementation(() => Promise.resolve({ status: 200 }));
  jest.spyOn(Router, 'useRouter').mockImplementation(() => ({
    replace: jest.fn(),
  }));
  const component = (
    <Recoil.RecoilRoot>
      <Template>
        <span>children</span>
      </Template>
    </Recoil.RecoilRoot>
  );

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render not logged menu', () => {
    const { getByText } = render(component);
    expect(getByText(/Iniciar sesión/i)).toBeInTheDocument();
    expect(getByText(/Regístrese/i)).toBeInTheDocument();
  });

  test('should render logged menu', () => {
    const user = {
      email: '<EMAIL>',
      nombreUsuario: 'John Doe',
      idUsuarioA: 'idUsuarioA',
    };

    jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [user, jest.fn()]);

    const { getByText, container } = render(component);
    expect(getByText(/Mi unidad/i)).toBeInTheDocument();
    expect(getByText(/Subir documentos/i)).toBeInTheDocument();
    expect(getByText(/Documentos firmados/i)).toBeInTheDocument();
    expect(getByText(/Cerrar sesión/i)).toBeInTheDocument();

    expect(container).toHaveTextContent(user.nombreUsuario);
    expect(container).toHaveTextContent(user.idUsuarioA);
  });

  it('should call Copyright', () => {
    const { getByText } = render(component);
    expect(getByText(/Copyright mock/i)).toBeInTheDocument();
  });
});

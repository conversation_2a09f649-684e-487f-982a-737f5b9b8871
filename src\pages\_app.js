import React, { useEffect } from 'react';
import '@/styles/tokens.css';
import '@/styles/globals.css';
import PropTypes from 'prop-types';
import Head from 'next/head';
import { useRouter } from 'next/router';
import { RecoilRoot } from 'recoil';
import HandleError from '@/components/organisms/HandleError';
import defaultTheme from '@/styles/theme/defaultTheme';
import { SnackbarProvider } from 'notistack';
import { QueryClient, QueryClientProvider } from 'react-query';
import { CacheProvider } from '@emotion/react';
import { ThemeProvider, Slide } from '@mui/material';

import createEmotionCache from '@/utils/createEmotionCache';
import '@/utils/axiosInterceptor';

import dynamic from 'next/dynamic';

const Template = dynamic(() => import('@/components/templates/Template'), { ssr: false });

const queryClient = new QueryClient();

const clientSideEmotionCache = createEmotionCache();

function MyApp({ Component, pageProps, emotionCache = clientSideEmotionCache }) {
  const router = useRouter();

  useEffect(() => {
    // Manejar errores de router
    const handleRouteError = (err, url) => {
      console.log('Router error:', err, 'URL:', url);
    };

    const handleRouteStart = (url) => {
      console.log('Route starting:', url);
    };

    const handleRouteComplete = (url) => {
      console.log('Route completed:', url);
    };

    router.events.on('routeChangeError', handleRouteError);
    router.events.on('routeChangeStart', handleRouteStart);
    router.events.on('routeChangeComplete', handleRouteComplete);

    return () => {
      router.events.off('routeChangeError', handleRouteError);
      router.events.off('routeChangeStart', handleRouteStart);
      router.events.off('routeChangeComplete', handleRouteComplete);
    };
  }, [router]);

  return (
    <>
      <Head>
        <title>Firmese</title>
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>
      <QueryClientProvider client={queryClient}>
        <CacheProvider value={emotionCache}>
          <ThemeProvider theme={defaultTheme}>
            <SnackbarProvider
              anchorOrigin={{
                vertical: 'top',
                horizontal: 'right',
              }}
              TransitionComponent={Slide}
              maxSnack={6}
            >
              <RecoilRoot>
                <div>
                  <HandleError>
                    <Template>
                      <Component {...pageProps} />
                    </Template>
                  </HandleError>
                </div>
              </RecoilRoot>
            </SnackbarProvider>
          </ThemeProvider>
        </CacheProvider>
      </QueryClientProvider>
    </>
  );
}

MyApp.propTypes = {
  Component: PropTypes.elementType.isRequired,
  // eslint-disable-next-line react/forbid-prop-types
  emotionCache: PropTypes.object,
  // eslint-disable-next-line react/forbid-prop-types
  pageProps: PropTypes.object.isRequired,
};

export default MyApp;

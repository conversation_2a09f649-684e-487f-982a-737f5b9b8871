export const TOKEN_CHECK_INTERVAL = 30000; // 30 segundos
export const TOKEN_WARNING_TIME = 5 * 60 * 1000; // 5 minutos antes de expirar
export const TOKEN_REFRESH_TIME = 5 * 60 * 1000; // 5 minutos antes de expirar

// Función para decodificar JWT sin verificar
const decodeJWT = (token) => {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => `%${(`00${c.charCodeAt(0).toString(16)}`).slice(-2)}`)
        .join(''),
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    return null;
  }
};

export const isTokenExpired = () => {
  if (typeof window === 'undefined') return false;

  try {
    const userState = localStorage.getItem('userState');
    if (userState) {
      const user = JSON.parse(userState);
      if (user.access_token) {
        const decoded = decodeJWT(user.access_token);
        if (decoded && decoded.exp) {
          const now = Math.floor(Date.now() / 1000);
          return now >= decoded.exp;
        }
      }
    }

    // Fallback al método anterior
    const loginTime = localStorage.getItem('loginTime');
    const tokenDuration = localStorage.getItem('tokenDuration');

    if (!loginTime || !tokenDuration) {
      return false;
    }

    const loginTimestamp = parseInt(loginTime, 10);
    const duration = parseInt(tokenDuration, 10) * 1000;
    const now = Date.now();
    const elapsed = now - loginTimestamp;
    return elapsed >= duration;
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return false;
  }
};

export const getTokenTimeRemaining = () => {
  if (typeof window === 'undefined') return 0;

  try {
    const userState = localStorage.getItem('userState');
    if (userState) {
      const user = JSON.parse(userState);
      if (user.access_token) {
        const decoded = decodeJWT(user.access_token);
        if (decoded && decoded.exp) {
          const now = Math.floor(Date.now() / 1000);
          return Math.max(0, (decoded.exp - now) * 1000);
        }
      }
    }

    // Fallback al método anterior
    const loginTime = localStorage.getItem('loginTime');
    const tokenDuration = localStorage.getItem('tokenDuration');

    if (!loginTime || !tokenDuration) return 0;

    const loginTimestamp = parseInt(loginTime, 10);
    const duration = parseInt(tokenDuration, 10) * 1000;
    const now = Date.now();
    const elapsed = now - loginTimestamp;
    return Math.max(0, duration - elapsed);
  } catch (error) {
    console.error('Error getting token time remaining:', error);
    return 0;
  }
};

export const setTokenInfo = (expiresIn) => {
  if (typeof window === 'undefined') return;

  try {
    const loginTime = Date.now();
    localStorage.setItem('loginTime', loginTime.toString());
    localStorage.setItem('tokenDuration', expiresIn.toString());
  } catch (error) {
    console.error('Error setting token info:', error);
  }
};

export const clearTokenInfo = () => {
  if (typeof window === 'undefined') return;

  try {
    localStorage.removeItem('loginTime');
    localStorage.removeItem('tokenDuration');
  } catch (error) {
    console.error('Error clearing token info:', error);
  }
};

export const shouldRefreshToken = () => {
  if (typeof window === 'undefined') return false;

  try {
    const userState = localStorage.getItem('userState');
    if (userState) {
      const user = JSON.parse(userState);
      if (user.access_token && user.refresh_token) {
        const decoded = decodeJWT(user.access_token);
        if (decoded && decoded.exp) {
          const now = Math.floor(Date.now() / 1000);
          const timeUntilExpiry = (decoded.exp - now) * 1000;
          return timeUntilExpiry <= TOKEN_REFRESH_TIME && timeUntilExpiry > 0;
        }
      }
    }
    return false;
  } catch (error) {
    console.error('Error checking if should refresh token:', error);
    return false;
  }
};

export const canRefreshToken = () => {
  if (typeof window === 'undefined') return false;

  try {
    const userState = localStorage.getItem('userState');
    if (userState) {
      const user = JSON.parse(userState);
      return !!(user.refresh_token);
    }
    return false;
  } catch (error) {
    console.error('Error checking if can refresh token:', error);
    return false;
  }
};

import { stringify } from 'query-string';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import catchApiError from '@/utils/catchApiError';

// Función helper para decodificar JWT
function decodeJWT(token) {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map((c) => `%${(`00${c.charCodeAt(0).toString(16)}`).slice(-2)}`)
        .join(''),
    );
    return JSON.parse(jsonPayload);
  } catch (error) {
    throw new Error(`Error decoding JWT: ${error.message}`);
  }
}

async function refreshTokenRoute(req, res) {
  try {
    const { user } = req.session;

    if (!user?.refresh_token) {
      return res.status(401).json({
        error: 'No refresh token available',
        details: 'Sesión no contiene refresh_token',
      });
    }

    // Verificar si el refresh token está expirado
    try {
      const refreshTokenDecoded = decodeJWT(user.refresh_token);
      if (refreshTokenDecoded && refreshTokenDecoded.exp) {
        const now = Math.floor(Date.now() / 1000);
        const isRefreshExpired = now >= refreshTokenDecoded.exp;

        if (isRefreshExpired) {
          req.session.destroy();
          return res.status(401).json({
            error: 'Refresh token expired',
            details: 'El refresh token ha expirado',
          });
        }
      }
    } catch (decodeError) {
      // Continuar si no se puede decodificar el refresh token
    }

    const requestBody = {
      grant_type: 'refresh_token',
      refresh_token: user.refresh_token,
    };

    const response = await fetchApi.post(
      '/security/oauth/token',
      stringify(requestBody),
      {
        auth: {
          username: 'firmese',
          password: 'VnKfirm3Se',
        },
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          ...getDefaultHeaders(req),
        },
      },
    );

    const { data } = response;
    data.idUsuarioC = +data.idUsuarioC;

    // Actualizar sesión con el nuevo token
    req.session.user = {
      ...user,
      ...data,
    };

    await req.session.save();

    // Retornar toda la información actualizada
    return res.json({
      ...data,
      enableOris: user.enableOris,
    });
  } catch (error) {
    // Si el refresh token también expiró, limpiar sesión
    if (error.response?.status === 401) {
      req.session.destroy();
      return res.status(401).json({
        error: 'Refresh token expired',
        details: error.response?.data,
      });
    }
    return catchApiError(error, res);
  }
}

export default withSessionRoute(refreshTokenRoute);

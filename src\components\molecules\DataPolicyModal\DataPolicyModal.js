import React from 'react';
import PropTypes from 'prop-types';
import Modal from '@/components/atoms/Modal';
import Paragraph from '@/components/atoms/Paragraph';
import Link from '@/components/atoms/Link';
import Spacer from '@/components/layout/Spacer';

export function DataPolicyModal({ onClose }) {
  return (
    <Modal
      title="POLÍTICA DE TRATAMIENTO DE DATOS PERSONALES"
      type="tertiary"
      onClose={onClose}
    >

      <Paragraph size="xs" isJustify>
        En cumplimiento a la política de Tratamiento de Datos Personales y con el objetivo de
        garantizar el adecuado cumplimiento de la Ley 1581 de 2012 (por la cual se dictan
        disposiciones generales para la protección de datos personales) el Titular autoriza el
        uso y tratamiento de datos. El titular acepta la presente autorización de forma
        voluntaria y sin presión alguna, entendiendo estos datos facilitan la ejecución del
        proceso de firma electrónico. Para tal Para tal efecto, declaro que he sido informado
        sobre las políticas de tratamiento de datos personales de LEGAL SHIELD S.A.S. y que
        conozco los canales para ejercer el derecho de habeas data sobre mi información
        personal, conforme a los derechos y garantías consagrados en la Ley 1581 de 2012 y
        su Decreto Reglamentario reglamentarios, la cual se encuentra publicada en el
        {' '}
        <Link
          size="xs"
          href="https://legalshield.com.co/wp-content/uploads/2017/03/Politica-de-tratamiento-de-la-informacion-Legal-Shield.pdf"
          target="_blank"
          rel="noopener noreferrer"
        >
          link
        </Link>
        .
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <Paragraph size="xs" isJustify>
        <strong>
          CON LA ACEPTACIÓN DE LOS PRESENTES TÉRMINOS Y CONDICIONES DE USO, EL
          TITULAR DECLARA QUE LA INFORMACIÓN DILIGENCIADA POR ÉL Y CONTENIDA EN
          ESTE DOCUMENTO ES CIERTA Y QUE ASÍ LO HA VERIFICADO PERSONALMENTE
        </strong>
        . Así
        mismo autoriza a las sociedades antes indicada para: 1) Conservar toda la
        documentación o demás información que ha entregado con esta petición o que le
        entregará en el futuro, independientemente de la aceptación o rechazo de la misma. 2)
        Verificar toda la información entregada a través de los medios que estime pertinentes.
        Declara haber recibido toda la información necesaria sobre tales servicios, su nivel de
        confiabilidad, los límites de responsabilidad, y las obligaciones que el mismo asume.
      </Paragraph>

    </Modal>
  );
}

DataPolicyModal.propTypes = {
  onClose: PropTypes.func.isRequired,
};

export default DataPolicyModal;

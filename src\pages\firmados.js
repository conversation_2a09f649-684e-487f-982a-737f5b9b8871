import React, { useEffect, useMemo } from 'react';
import { useRecoilState } from 'recoil';
import DocumentosFirmados from '@/components/organisms/DocumentosFirmados';
import { userState } from '@/recoil/atoms';
import withAuthentication from '@/lib/withAuthentication';
import { useRouter } from 'next/router';

export const getServerSideProps = withAuthentication;

export default function Firmados({ user }) {
  const [, setUser] = useRecoilState(userState);
  const router = useRouter();
  const { tab } = router.query;

  useEffect(() => {
    setUser(user);
  }, [user]);

  const tabSelected = useMemo(() => {
    const tabs = ['propietario', 'firmados'];
    const index = tabs.indexOf(tab);

    return index === -1 ? 0 : index;
  }, [tab]);

  return (
    <DocumentosFirmados tab={tabSelected} />
  );
}

/**
 * Function to validate a string and toCapitalize it if necessary
 * @param {string} str
 */
function toPascalCase(str) {
  if (typeof str !== 'string') {
    throw new Error('toPascalCase: str must be a string');
  }

  const REG_EXP = /[A-Z]/;

  if (!REG_EXP.test(str)) {
    return str
      .replace(
        /\w\S*/g,
        (word) => word.charAt(0).toUpperCase() + word.substr(1).toLowerCase(),
      )
      .replace(/ /g, '');
  }
  return str;
}

module.exports = toPascalCase;

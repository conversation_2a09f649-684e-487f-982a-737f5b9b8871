import React, { useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import ValidaProcesoFirma from '@/components/organisms/ValidaProcesoFirma';
import icon from '@/assets/images/Logo3.png';

import StateHandler from '@/components/organisms/StateHandler';
import withAuthentication from '@/lib/withAuthentication';
import { userState } from '@/recoil/atoms';

export const getServerSideProps = withAuthentication;

export default function ValidaFirmaPage({ user }) {
  const [, setUser] = useRecoilState(userState);

  useEffect(() => {
    setUser(user);
  }, [user]);

  const [state, setState] = useState({
    isLoaded: false,
    haveError: false,
    isLoading: false,
    haveDocsPending: true,
    isVerified: false,
    cotp: '',
    loadMessage: 'Estamos Firmando tu documento',
    errorMessage: {
      header: '',
      content: 'este mensaje es automático',
      icon,
    },
  });

  const handleClickError = () => {
    setState({
      ...state,
      haveError: false,
      isLoaded: false,
      isLoading: false,
    });
  };

  return (
    <div>
      <StateHandler state={state} handleErrorButton={handleClickError}>
        <ValidaProcesoFirma />
      </StateHandler>
    </div>
  );
}

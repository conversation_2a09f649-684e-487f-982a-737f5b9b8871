.error-container {
    position: relative;
    height: 100vh;
}

.error {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.error {
    max-width: 520px;
    width: 100%;
    line-height: 1.4;
    text-align: center;
}

.error-description {
    position: relative;
    height: 200px;
    margin: 0 auto 20px;
    z-index: -1;
}

.error-description :global(h1) {
    font-family: 'Montserrat', sans-serif;
    font-size: 236px;
    font-weight: 200;
    margin: 0;
    color: #211b19;
    text-transform: uppercase;
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.error-description :global(h2) {
    font-family: 'Montserrat', sans-serif;
    font-size: 28px;
    font-weight: 400;
    text-transform: uppercase;
    color: #211b19;
    background: #fff;
    padding: 10px 5px;
    margin: auto;
    display: inline-block;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
}

.error :global(a) {
    font-family: 'Montserrat', sans-serif;
    display: inline-block;
    font-weight: 700;
    text-decoration: none;
    color: #fff;
    text-transform: uppercase;
    padding: 13px 23px;
    background: #33CC99;
    font-size: 18px;
    -webkit-transition: 0.2s all;
    transition: 0.2s all;
}

.error :global(a:hover) {
    color: #33CC99;
    background: #211b19;
}

@media only screen and (max-width: 767px) {
    .error-description-404 :global(h1) {
        font-size: 148px;
    }
}

@media only screen and (max-width: 480px) {
    .error-description {
        height: 148px;
        margin: 0 auto 10px;
    }

    .error-description :global(h1) {
        font-size: 86px;
    }

    .error-description :global(h2) {
        font-size: 16px;
    }

    .error :global(a) {
        padding: 7px 15px;
        font-size: 14px;
    }
}

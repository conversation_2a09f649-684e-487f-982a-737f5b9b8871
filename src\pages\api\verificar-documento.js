import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import catchApiError from '@/utils/catchApiError';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '20mb',
    },
  },
};

async function verificarDocumentoRoute(req, res) {
  try {
    const { data } = await fetchApi.post(
      '/validacion/registro/verificar-archivo-firmado-64',
      req.body,
      {
        headers: getDefaultHeaders(req),
      },
    );

    return res.send(data);
  } catch (error) {
    console.log({ error });
    return catchApiError(error, res);
  }
}

export default withSessionRoute(verificarDocumentoRoute);

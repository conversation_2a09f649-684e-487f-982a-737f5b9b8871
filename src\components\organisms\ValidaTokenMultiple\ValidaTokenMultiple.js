import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import clsx from 'clsx';
import { useRecoilState } from 'recoil';
import Image from 'next/image';
import Container from '@/components/layout/Container';
import { userState } from '@/recoil/atoms';
import { verificarTokenMultipleFirma, aceptarTokenMultipleFirma } from '@/services/user';
import icon from '@/assets/images/logo_principal.png';
import PropTypes from 'prop-types';
import StateHandler from '@/components/organisms/StateHandler';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';
import ValidateCode from '@/components/organisms/ValidateCode';
import UnregisteredUser from '@/components/organisms/UnregisteredUser';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import CenteredContent from '@/components/layout/CenteredContent';
import OtherSignerDocInfo from '@/components/organisms/OtherSignerDocInfo';
import styles from './ValidaTokenMultiple.module.css';

function ValidaTokenMultiple({ token }) {
  const [user] = useRecoilState(userState);
  const router = useRouter();

  const [state, setState] = useState({
    isLoading: true,
    haveError: false,
    isValidate: false,
    documentos: [],
    dataTable: [],
    stateSign: false,
    code: '',
    id: '',
    idUsuarioC: '',
    emailMessage: '',
    errorMessage: {
      header: 'Ha ocurrido un error la obtención del código',
      content: 'este mensaje es automático',
      icon,
    },
    requiredSigns: 0,
  });

  useEffect(() => {
    if (token != null) {
      verificarTokenMultipleFirma(token)
        .then((response) => {
          if (response.status === 200) {
            setState({
              ...state,
              isLoading: false,
              haveError: false,
              token: true,
              documentos: response.data.data,
              id: response.data.data.idUsuario,
              nuevo: response.data.data?.nuevo || false,
            });
          } else if (response.status === 500) {
            setState({
              ...state,
              isLoading: false,
              haveError: true,
              token: true,
              content: response.data.error,
              header: response.data.error,
              errorMessage: {
                content: response.data.mensaje,
                header: '',
                icon,
              },
            });
          } else if (response.status !== 200) {
            setState({
              ...state,
              isLoading: false,
              haveError: true,
              token: true,
              content: response.data.mensaje,
              header: response.data.mensaje,
              errorMessage: {
                content: response.data.mensaje,
                header: '',
                icon,
              },
            });
          }
        })
        .catch((err) => console.error(err));
    } else {
      setState({
        token: false,
        isLoading: false,
      });
    }

    const requiredSigns = localStorage.getItem('requiredSigns') || null;

    if (requiredSigns) {
      setState((prev) => ({
        ...prev,
        requiredSigns: +requiredSigns,
      }));
      localStorage.removeItem('requiredSigns');
    }
  }, []);

  // confirmar que quiero los documentos que me llegaron por correo
  const aceptar = () => {
    setState((prev) => ({
      ...prev,
      isLoading: true,
    }));
    aceptarTokenMultipleFirma(token)
      .then((response) => {
        if (response.status === 200) {
          setState({
            ...state,
            isValidate: true,
            isLoading: false,
            idUsuarioC: response.data.data,
            content: response.data.mensaje,
          });
        } else if (response.status !== 200) {
          setState({
            ...state,
            isLoading: false,
            haveError: true,
            content: response.data.mensaje,
            header: response.data.mensaje,
          });
        }
      })
      .catch((err) => console.error(err));
  };

  // redirigir a home (botón cuando ocurre un error)
  const GoHome = async () => {
    await router.push('/');
  };

  return (
    <StateHandler state={state}>
      {/* si hay token */}
      {token ? (
        <div>
          {/* si hay error */}
          {state.haveError ? (
            <div>
              {/* Si el error es porque la persona este registrada  */}
              {state?.content?.includes('No se pudo consultar en la base de datos') ? (
                <UnregisteredUser />
              ) : (
              // Cualquier otro error que traiga el servidor
                <CenteredContent>
                  <Image
                    alt="logo"
                    className={clsx('img-responsive', styles.iconFirmese)}
                    src={icon}
                  />
                  {' '}
                  <Spacer.Vertical size="sm" />
                  <Heading isCentered>
                    Lo sentimos
                  </Heading>
                  <Spacer.Vertical size="sm" />
                  <Paragraph size="sm" isCentered>
                    {state.content}
                  </Paragraph>

                  <Spacer.Vertical size="md" />
                  {router?.query?.view !== 'app' && (
                    <Button
                      onClick={GoHome}
                      isInline
                    >
                      Ir al inicio
                    </Button>
                  )}
                </CenteredContent>

              )}

            </div>
          ) : (
          /* No hay error con token */
            <Container>
              {/* Validado para enviar código */}
              {state.isValidate ? (
                <Container>
                  <ValidateCode
                    user={user}
                    haytoken={state.token}
                    alltoken={token}
                    userToken={state.idUsuarioC}
                    isNew={state.nuevo}
                    requiredSigns={state.requiredSigns}
                  />
                </Container>
              ) : <OtherSignerDocInfo docs={state.documentos} onAccept={aceptar} />}

            </Container>
          )}
        </div>

      ) : (
      // no hay token, es cuando la persona va firmar su propio documento, no se envió por correo
        <Container>
          <ValidateCode
            user={user}
            haytoken={state.token}
            alltoken={token}
            requiredSigns={state.requiredSigns}
          />
        </Container>
      )}
    </StateHandler>
  );
}

ValidaTokenMultiple.defaultProps = {
  token: null,
};

ValidaTokenMultiple.propTypes = {
  token: PropTypes.string,
};

export default ValidaTokenMultiple;

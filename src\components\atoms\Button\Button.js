import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import isEmpty from '@/utils/isEmpty';
import isObject from '@/utils/isObject';
import Heading from '@/components/atoms/Heading';
import { options as optionsBtn } from '@/components/atoms/Heading/constants';

import styles from './Button.module.css';
import { options } from './constants';

export const handleClick = ({ onClick }) => (event) => {
  onClick(event);
};

export const Button = React.forwardRef(({
  type,
  children,
  addons,
  isMuted,
  isInline,
  onClick,
  getStyles,
  buttonType,
  disabled,
  style,
  size,
  component,
  'data-testid': testId,
}, ref) => {
  const headingType = useMemo(() => {
    const colors = {
      primary: 'inverted',
      base: 'base',
      danger: 'inverted',
    };
    return colors[type] || 'primary';
  }, [type]);

  return React.createElement(
    component,
    {
      type: buttonType,
      className: getStyles('button', ['type'], {
        'is-inline': isInline || type === 'tertiary',
        'is-muted': isMuted && type === 'primary',
      }),
      'data-testid': testId,
      ref,
      style,
      onClick: onClick ? handleClick({ onClick }) : undefined,
      disabled,
    },
    <>
      {addons && addons.prepend}

      {typeof children === 'string' ? (
        <Heading
          color={headingType}
          isCentered
          isInline={isObject(addons) && !isEmpty(addons)}
          size={size}
        >
          {children}
        </Heading>
      ) : (
        children
      )}

      {addons && addons.append}
    </>,
  );
});

Button.propTypes = {
  children: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  getStyles: PropTypes.func.isRequired,
  type: PropTypes.oneOf(options.types),
  addons: PropTypes.shape({
    prepend: PropTypes.node,
    append: PropTypes.node,
  }),
  size: PropTypes.oneOf(optionsBtn.sizes),
  onClick: PropTypes.func,
  isInline: PropTypes.bool,
  isMuted: PropTypes.bool,
  buttonType: PropTypes.oneOf(['button', 'submit']),
  disabled: PropTypes.bool,
  component: PropTypes.string,
};

Button.defaultProps = {
  type: 'primary',
  getStyles: () => ({}),
  onClick: () => { /* */ },
  isInline: false,
  buttonType: 'button',
  disabled: false,
  component: 'button',
  size: 'xs',
};

export default withStyles(styles)(Button);

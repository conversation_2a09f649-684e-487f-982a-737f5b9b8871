.preview {
    height: auto;
    width: 100%;
    object-fit: cover;
    object-position: top left;
    background: var(--color-base-white);
}

.container {
    width: 100%;
    max-width: 1600px; 
    margin: 0 auto;
    padding: 0 16px;
}

.preview-container {
    position: sticky;
    top: 24px;
    height: calc(100vh - 48px);
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.preview-container :global(img) {
    width: 100%;
    height: 100%;
    object-fit: contain;
    background: #fff;
}

.preview-container :global(.centered-content) {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.grid {
    display: grid;
    grid-template-columns: 65% 35%;
    gap: 32px;
    margin: 24px 0;
    padding: 0;
}

.documentCard {
    background: #fff;
    border: 1px solid #33cc99;
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 24px;
}

.documentHeader {
    margin-bottom: 16px;
}

.documentHeader h2 {
    margin-bottom: 8px;
    font-size: 1.25rem;
    font-weight: 500;
    line-height: 1.6;
    color: rgba(0, 0, 0, 0.87);
}

.infoTable {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
}

.infoTable td {
    padding: 6px 16px;
    font-size: 0.875rem;
    line-height: 1.43;
    border-bottom: 1px solid rgba(224, 224, 224, 1);
}

.infoTable td:first-child {
    color: rgba(0, 0, 0, 0.6);
    width: 40%;
}

.signerCard {
    border: 1px solid #33cc99;
    border-radius: 4px;
    margin-bottom: 24px;
    overflow: hidden;
}

.signerHeader {
    padding: 16px;
    background: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.12);
    font-size: 1.125rem; 
    font-weight: 500; 
    color: rgba(0, 0, 0, 0.87);
}

.signerBody {
    padding: 16px;
}

.signerFooter {
    padding: 8px 16px;
    background: rgba(0, 0, 0, 0.03);
    font-size: 0.75rem;
    color: rgba(0, 0, 0, 0.6);
}

.actions-container {
    display: flex;
    gap: 16px;
    margin-top: 32px;
}

.hashContainer {
    margin-top: 40px;
}

.hashCard {
    background: #fff;
    border: 1px solid rgba(0, 0, 0, 0.12);
    border-radius: 4px;
    padding: 16px;
    margin-bottom: 24px;
}

.hashCard h2 {
    margin-bottom: 16px;
    font-size: 1.25rem;
    font-weight: 500;
}

@media (min-width: 768px) {
    .grid {
        grid-template-columns: 50% 50%;
    }
}

/* Ajustes para pantallas más pequeñas */
@media (max-width: 960px) {
    .grid {
        grid-template-columns: 100%; 
        margin: 16px;
        padding: 0;
    }

    .preview-container {
        position: static;
        height: 500px;
        margin-bottom: 24px;
    }
}

@media (max-width: 768px) {
    .container {
        max-width: 100%; 
        padding: 0 8px; 
    }
}

@media (max-width: 1024px) {
    .actions-container {
        flex-direction: column;
        justify-content: space-between;
        margin-top: 1rem;
    }

    .actions-container :global(button) {
        max-width: 100% !important;
    }
}

.table :global(th) {
    text-align: left;
    vertical-align: top;
}

import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import styles from './Input.module.css';
import { options } from './constants';

export const Input = React.forwardRef(({
  getStyles,
  type,
  isInline,
  placeholder,
  hasError,
  helperText,
  role,
  onChange,
  onBlur,
  name,
  id,
  label,
  disabled,
  required,
  disablePaste,
}, ref) => (
  <>
    <label
      htmlFor={id}
      className={getStyles('label', {
        'has-error': hasError,
      })}
    >
      {label}
    </label>
    <input
      className={getStyles('input', {
        'is-inline': isInline,
        'has-error': hasError,
      })}
      type={type}
      role={role}
      placeholder={placeholder}
      id={id}
      name={name}
      onChange={onChange}
      onBlur={onBlur}
      ref={ref}
      disabled={disabled}
      required={required}
      onPaste={disablePaste ? (e) => e.preventDefault() : null}
    />
    <p
      className={getStyles('helper-text', {
        'has-error': hasError,
      })}
    >
      {helperText}
    </p>
  </>
));

Input.propTypes = {
  onChange: PropTypes.func.isRequired,
  getStyles: PropTypes.func.isRequired,
  type: PropTypes.oneOf(options.types),
  value: PropTypes.string,
  placeholder: PropTypes.string,
  isInline: PropTypes.bool,
  register: PropTypes.func.isRequired,
  rules: PropTypes.shape({
    required: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.shape({
        value: PropTypes.bool,
        message: PropTypes.string,
      }),
    ]),
    maxLength: PropTypes.shape({
      value: PropTypes.number,
      message: PropTypes.string,
    }),
    minLength: PropTypes.shape({
      value: PropTypes.number,
      message: PropTypes.string,
    }),
    max: PropTypes.shape({
      value: PropTypes.number,
      message: PropTypes.string,
    }),
    min: PropTypes.shape({
      value: PropTypes.number,
      message: PropTypes.string,
    }),
    pattern: PropTypes.shape({
      value: PropTypes.instanceOf(RegExp),
      message: PropTypes.string,
    }),
    validate: PropTypes.oneOfType([
      PropTypes.func,
      PropTypes.objectOf(PropTypes.func),
    ]),
    valueAsNumber: PropTypes.bool,
    valueAsDate: PropTypes.bool,
    setValueAs: PropTypes.func,
    disabled: PropTypes.bool,
    onChange: PropTypes.func,
    onBlur: PropTypes.func,
    // eslint-disable-next-line react/forbid-prop-types
    value: PropTypes.any,
    shouldUnregister: PropTypes.bool,
    deps: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.arrayOf(PropTypes.string),
    ]),
  }),
  role: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  disablePaste: PropTypes.bool,
};

Input.defaultProps = {
  type: 'text',
  value: '',
  placeholder: '',
  isInline: false,
  onChange: () => { /* */ },
  getStyles: () => ({}),
  register: () => ({}),
  rules: {},
  role: 'textbox',
  disabled: false,
  required: false,
  disablePaste: false,
};

export default withStyles(styles)(Input);

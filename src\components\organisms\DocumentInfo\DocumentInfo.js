import React, { useMemo } from 'react';
import filterIp from '@/utils/filterIp';
import downloadPDF from '@/utils/downloadPDF';
import { downloadFileB64 } from '@/services/user';
import { useSnackbar } from 'notistack';
import PropTypes from 'prop-types';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import Icon from '@/components/atoms/Icon';
import dayjs from 'dayjs';
import Loading from '@/components/atoms/Loading';
import toCapitalize from '@/utils/toCapitalize';
import ShareDocumentModal from '@/components/molecules/ShareDocumentModal';
import ButtonIcon from '@/components/molecules/ButtonIcon';
import Container from '@/components/layout/Container';
import Divider from '@/components/atoms/Divider';
import DocPreview from '@/components/organisms/DocPreview';
import styles from './DocumentInfo.module.css';

import 'dayjs/locale/es';

dayjs.locale('es');

function DocumentInfo({ children, handleClick, documentInfo }) {
  const { enqueueSnackbar } = useSnackbar();
  const [showShareModal, setShowShareModal] = React.useState(false);

  const download = () => {
    downloadFileB64(documentInfo.idArchivo)
      .then((response) => {
        if (response.status === 200) {
          downloadPDF(response.data.data, documentInfo.nombreArchivo);
        } else {
          enqueueSnackbar(response.data.mensaje, { variant: 'error' });
        }
      })
      .catch((err) => console.error(err));
  };

  const loadedComponent = useMemo(() => {
    if (documentInfo?.isLoading || !documentInfo?.nombreArchivo) {
      return <Loading isShown />;
    }

    return (
      <div>
        <div style={{ textAlign: 'right' }}>
          <Button type="tertiary" isInline onClick={handleClick}>
            Volver al inicio
          </Button>
        </div>
        <Spacer.Vertical size="sm" />
        <Heading>Información detallada del archivo firmado</Heading>
        <Paragraph size="sm">
          Los datos que se muestran a continuación son el nombre, fecha de
          carga del archivo y el origen de donde se envió la instrucción
          de firma.
        </Paragraph>
        <Spacer.Vertical size="sm" />
        <div className={styles.grid}>
          <div>
            <div className={styles.documentCard}>
              <div className={styles.documentHeader}>
                <Heading size="sm">{toCapitalize(documentInfo.nombreArchivo)}</Heading>
                <Paragraph size="sm">
                  Documento cargado a la plataforma el día
                  {toCapitalize(dayjs(documentInfo.fechaRegistro).format(' dddd DD MMMM YYYY, hh:mm a '))} 
                  desde la dirección IP <em>{filterIp(documentInfo.ip.split(',')[0])}</em>
                </Paragraph>
              </div>
            </div>

            {documentInfo?.propietario && (
              <div className={styles.documentCard}>
                <Heading size="sm">Información de propietario</Heading>
                <table className={styles.infoTable}>
                  <tbody>
                    <tr>
                      <td>Nombre completo</td>
                      <td>{documentInfo?.propietario?.nombreCompleto}</td>
                    </tr>
                    <tr>
                      <td>Tipo de identificación</td>
                      <td>{documentInfo?.propietario?.tipoDocumento}</td>
                    </tr>
                    <tr>
                      <td>Número de identificación</td>
                      <td>{documentInfo?.propietario?.numeroDocumento}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            )}

            <Heading size="sm">Personas que intervienen en la firma del documento</Heading>

            {documentInfo.firmas.map((doc) => (
              <div key={doc.firma.numeroDocumento} className={styles.signerCard}>
                <div className={styles.signerHeader}>
                  {doc?.subioArchivo ? 'Firmante propietario de documento' : 'Firmante interviniente'}
                </div>
                <div className={styles.signerBody}>
                  <table className={styles.infoTable}>
                    <tbody>
                      <tr>
                        <td>Nombre completo</td>
                        <td>{doc.firma.nombreCompleto}</td>
                      </tr>
                      <tr>
                        <td>Tipo de identificación</td>
                        <td>{doc.firma.nombreTipoDocumento}</td>
                      </tr>
                      <tr>
                        <td>Número de identificación</td>
                        <td>{doc.firma.numeroDocumento}</td>
                      </tr>
                      <tr>
                        <td>Correo electrónico</td>
                        <td>{doc.firma.correoElectronico}</td>
                      </tr>
                      <tr>
                        <td>Fecha de firma de documento</td>
                        <td>{dayjs(doc.fechaFirmaStr).format('DD MMMM YYYY, hh:mm a')}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                <div className={styles.signerFooter}>
                  Navegador: {doc?.agenteNavegador}
                </div>
              </div>
            ))}

            <div className={styles['actions-container']}>
              <ButtonIcon onClick={download} icon="documentDownload" type="primary">
                Descargar
              </ButtonIcon>
              <ButtonIcon color="secondary" icon="share" onClick={() => setShowShareModal(true)}>
                Compartir
              </ButtonIcon>
            </div>
          </div>

          <div className={styles['preview-container']}>
            <DocPreview docId={documentInfo.idArchivo} />
          </div>
        </div>

        <div className={styles.hashContainer}>
          <div className={styles.hashCard}>
            <Heading size="sm">Código hash verificación (SHA512) de documento original</Heading>
            <Paragraph size="xs" isMonospace>{documentInfo.hashArchivo}</Paragraph>
          </div>

          <div className={styles.hashCard}>
            <Heading size="sm">Código hash de documento con tabla de firmantes</Heading>
            <Paragraph size="xs" isMonospace>{documentInfo.firmas[0].hashFirma}</Paragraph>
          </div>
        </div>
      </div>
    );
  }, [documentInfo]);

  return (
    <Container>
      {
        showShareModal && (
          <ShareDocumentModal
            onClose={() => setShowShareModal(false)}
            idArchivo={documentInfo?.idArchivo}
          />
        )
      }
      {documentInfo.showInfo ? (
        loadedComponent
      ) : (
        children
      )}
    </Container>
  );
}

DocumentInfo.propTypes = {
  children: PropTypes.node,
  handleClick: PropTypes.func.isRequired,
  documentInfo: PropTypes.shape({
    showInfo: PropTypes.bool,
    propietario: PropTypes.shape({
      nombreCompleto: PropTypes.string.isRequired,
      tipoDocumento: PropTypes.string.isRequired,
      numeroDocumento: PropTypes.string.isRequired,
    }),
    ip: PropTypes.string,
    fechaRegistro: PropTypes.string,
    cantidadConsultas: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    hashArchivo: PropTypes.string,
    idArchivo: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
    nombreArchivo: PropTypes.string,
    firmas: PropTypes.arrayOf(PropTypes.shape({
      firma: PropTypes.shape({
        nombreCompleto: PropTypes.string.isRequired,
        nombreTipoDocumento: PropTypes.string.isRequired,
        numeroDocumento: PropTypes.string.isRequired,
        correoElectronico: PropTypes.string.isRequired,
      }).isRequired,
      fechaFirmaStr: PropTypes.string.isRequired,
      ipFirma: PropTypes.string.isRequired,
      agenteNavegador: PropTypes.string.isRequired,
    })),
    size: PropTypes.number,
    previewB64: PropTypes.string,
    pdfPages: PropTypes.number,
  }).isRequired,
};

export default DocumentInfo;

import React, { useState } from 'react';
import useDigitInput from '@/hooks/useDigitInput';
import { useRouter } from 'next/router';
import clsx from 'clsx';
import { useSnackbar } from 'notistack';
import Image from 'next/image';
import PropTypes from 'prop-types';
import Container from '@/components/layout/Container';
import Grid from '@mui/material/Grid';
import { saveStorage, loadItemStorage } from '@/utils/utils';
import {
  verifiySignProcess,
  validarFirmanteDocumento,
  enviarCodigoPorCorreo,
  validarOrdenFirma,
} from '@/services/user';
import icon from '@/assets/images/logo_principal.png';
import Card from '@/components/atoms/Card';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import phoneIcon from '@/assets/images/phone.png';
import CenteredContent from '@/components/layout/CenteredContent';
import Spacer from '@/components/layout/Spacer';
import Button from '@/components/atoms/Button';
import StateHandler from '@/components/organisms/StateHandler';
import SignAgreementModal from '@/components/molecules/SignAgreementModal';
import UnregisteredUser from '@/components/organisms/UnregisteredUser';
import Check from '@/components/atoms/Check';
import { useForm } from 'react-hook-form';
import ButtonIcon from '@/components/molecules/ButtonIcon';
import { styled } from '@mui/material/styles';
import styles from './ValidateCode.module.css';

const SignAgreementCheck = styled('div')(({ theme }) => ({
  marginTop: theme.spacing(5),
  textAlign: 'center',
}));

const ConfirmButton = styled(Button)(({ theme }) => ({
  marginTop: theme.spacing(5),
  textAlign: 'center',
}));

const TextWarning = styled('p')(({ theme }) => ({
  marginTop: theme.spacing(3),
  marginBottom: theme.spacing(3),
  color: '#17a2b8',
  fontWeight: 'bold',
  fontSize: 15,
}));

const HelpText = styled('div')(({ theme }) => ({
  textAlign: 'center',
  marginTop: theme.spacing(3),
  marginBottom: theme.spacing(3),
}));

export default function ValidateCode({
  user, alltoken, userToken, haytoken, requiredSigns,
}) {
  const { enqueueSnackbar } = useSnackbar();
  const usuario = user;

  const router = useRouter();
  const [showAgreement, setShowAgreement] = useState(false);

  const [value, onChange] = React.useState('');
  const digits = useDigitInput({
    acceptedCharacters: /^[\da-zA-Z]$/,
    length: 6,
    value,
    onChange,
  });
  const { register, handleSubmit, formState: { errors } } = useForm({
    defaultValues: {
      agreement: false,
    },
  });

  const [documento, setDocumento] = useState({
    isSigned: false,
    message: '',
    icon: '',
    title: '',
    header: '',
    content: '',
    haveError: false,
    documentos: false,
    errorMessage: {
      title: '',
      header: '',
      content: '',
      icon,
    },
  });
  const [state, setState] = useState({
    // haveError: true,
    haveError: false,
    isValidate: false,
    token: false,
    documentos: [],
    dataTable: [],
    stateSign: false,
    showWarning: false,
    showTextWait: false,
    code: '',
    id: '',
    idUsuarioC: '',
    emailMessage: '',
    errorMessage: {
      header: 'Ha ocurrido un error la obtención del código',
      content: 'este mensaje es automático',
      icon,
    },
  });
  const [botonActivo, setBotonActivo] = useState(false);

  const handleMessage = async () => {
    setDocumento(
      {
        ...documento,
        haveError: false,
      },
    );
    await router.push('/firmados');
  };

  const handleMessageClick = () => {
    setState({
      ...state,
      haveError: false,
      isLoading: false,
    });
  };

  // Función para identificar documentos SINGLE
  const esDocumentoSingle = (doc) => {
    // Un documento es SINGLE si:
    const esSoloMio = (
      doc.esSolicitud === 'NO'
      || doc.esSolicitud === false
      || doc.esSolicitud == null
      || doc.esSolicitud === undefined
    );

    const esTipoSingle = doc.tipoFirma === 'SINGLE';
    const esMultiplePeroSolo = (
      doc.tipoFirma === 'MULTIPLE'
      && (doc.firmasRequeridas === 1 || doc.firmasRequeridas == null)
    );

    const soyPropietarioSinOtros = (
      doc.propietario === user?.email
      && doc.tipoFirma !== 'OTHERS'
      && (doc.totalFirmas == null || doc.totalFirmas <= 1)
    );

    const esSingle = esSoloMio
      || esTipoSingle
      || esMultiplePeroSolo
      || soyPropietarioSinOtros;

    console.log(`📋 ValidateCode - Análisis documento ${doc.idArchivo}:`, {
      esSolicitud: doc.esSolicitud,
      tipoFirma: doc.tipoFirma,
      firmasRequeridas: doc.firmasRequeridas,
      propietario: doc.propietario,
      userEmail: user?.email,
      RESULTADO_FINAL: esSingle ? '✅ ES SINGLE' : '❌ REQUIERE VALIDACIÓN',
    });
    return esSingle;
  };

  // la función validarOrdenAntesDeCodigo
  const validarOrdenAntesDeCodigo = async () => {
    const documentos = loadItemStorage('docsToSign');
    if (!documentos || documentos.length === 0) {
      console.log('❌ No hay documentos para validar');
      return true;
    }

    try {
      const documentosConOrden = documentos.filter((doc) => {
        const esSingle = esDocumentoSingle(doc);
        return !esSingle; // Solo validar los que NO son SINGLE
      });

      // Si no hay documentos que requieran validación, permitir continuar
      if (documentosConOrden.length === 0) {
        return true;
      }

      // Solo validar los documentos que realmente requieren validación
      const ordenResults = await Promise.all(
        documentosConOrden.map(async (doc) => {
          console.log(`🔍 Validando orden para: ${doc.idArchivo}`);
          const result = await validarOrdenFirma(
            doc.idArchivo,
            user?.email,
            user?.access_token,
          );
          console.log(`📊 Resultado ${doc.idArchivo}:`, {
            status: result.status,
            puedeFirmar: result.data?.data?.puedeFirmar,
          });
          return result;
        }),
      );

      const invalidOrden = ordenResults.find(
        (result) => result.status !== 200 || !result.data?.data?.puedeFirmar,
      );

      if (invalidOrden) {
        console.log('❌ DOCUMENTOS QUE NO PUEDE FIRMAR ENCONTRADOS');
        setState({
          ...state,
          haveError: true,
          errorMessage: {
            ...state.errorMessage,
            header: 'Turno de firma no disponible',
            content: 'No puedes generar un código de verificación en este momento porque aún no es tu turno para firmar. Debes esperar a que el firmante anterior complete su proceso de firma.',
          },
        });
        return false;
      }

      console.log('✅ TODOS LOS DOCUMENTOS PUEDEN SER FIRMADOS');
      return true;
    } catch (error) {
      console.error('❌ Error al validar orden antes de código:', error);
      return true;
    }
  };
  // Función para enviar el código por correo, WhatsApp o SMS
  const handleSubmitEmail = async (typeSignature) => {
    // NO validar orden para reenvío
    // const puedeGenerarCodigo = await validarOrdenAntesDeCodigo();
    // if (!puedeGenerarCodigo) {
    //   return;
    // }

    setState({
      ...state,
      isLoading: true,
    });

    // comprobar si tengo token o no
    let body;
    if (haytoken === false) {
      body = {
        id: usuario.idUsuarioC,
        tipo: typeSignature,
      };
    } else {
      body = {
        id: userToken,
        tipo: typeSignature,
      };
    }
    try {
      const { data } = await enviarCodigoPorCorreo(body);
      setState({
        ...state,
        isLoading: false,
        emailMessage: data.data,
      });
      enqueueSnackbar('Se ha enviado tu código nuevamente.', { variant: 'success' });
    } catch (error) {
      console.error('Error al enviar código:', error);
      setState({
        ...state,
        isLoading: false,
        haveError: true,
        errorMessage: {
          ...state.errorMessage,
          header: 'Error al enviar código',
          content: 'No se pudo enviar el código de verificación. Por favor, intenta nuevamente.',
        },
      });
    }
  };

  const onSubmit = async () => {
    // Validar orden antes de procesar la firma
    const puedeGenerarCodigo = await validarOrdenAntesDeCodigo();
    if (!puedeGenerarCodigo) {
      return; // No procesar si no es el turno
    }

    // Continuar con el proceso normal de firma
    if (digits.length === 5) {
      setBotonActivo(true);
    }

    if (digits[5].value !== '') {
      setState({
        ...state,
        isLoading: true,
        showWarning: false,
        showTextWait: true,
      });

      validarFirmanteDocumento(alltoken, userToken, value)
        .then(async (response) => {
          if (response.status === 200) {
            setState({
              ...state,
              isLoading: true,
              isAuth: true,
            });
            saveStorage('x-dta', response.data);
            await router.push(`/firmaExitosa${router?.query?.view === 'app' ? '?view=app' : ''}`);
          } else if (response.status !== 200) {
            // Verificar si es un error de orden de firma
            const isOrderError = response.data?.mensaje?.includes('turno')
                                || response.data?.mensaje?.includes('orden')
                                || response.status === 500;
            setState({
              ...state,
              isLoading: false,
              haveError: true,
              errorMessage: {
                ...state.errorMessage,
                header: isOrderError ? 'Turno de firma no disponible' : 'Validación de código único de verificación',
                content: isOrderError
                  ? 'No puedes firmar en este momento porque aún no es tu turno. Debes esperar a que el firmante anterior complete su proceso de firma.'
                  : response.data.mensaje,
              },
            });
          }
        })
        .catch((error) => console.log(error));
    } else {
      setState({
        ...state,
        isLoading: false,
        showWarning: true,
        showTextWait: false,
      });
    }
  };

  const handleSubmitNoToken = async () => {
    // MANTENER: Validar orden antes de procesar (NO para reenvío)
    const puedeGenerarCodigo = await validarOrdenAntesDeCodigo();
    if (!puedeGenerarCodigo) {
      return; // No procesar si no es el turno
    }

    setState({
      ...state,
      isLoading: true,
    });

    verifiySignProcess(usuario.idUsuarioC, value, user?.access_token)
      .then(async (response) => {
        console.log({ response });
        if (response.status === 200) {
          setState({
            ...state,
            isLoading: true,
            isAuth: true,
          });
          saveStorage('x-dta', response.data);
          await router.push(`/firmaExitosa${router?.query?.view === 'app' ? '?view=app' : ''}`);
        } else {
          const isOrderError = response.data?.mensaje?.includes('turno')
                              || response.data?.mensaje?.includes('orden')
                              || response.status === 500;

          setState({
            ...state,
            isLoading: false,
            haveError: true,
            errorMessage: {
              ...state.errorMessage,
              header: isOrderError ? 'Turno de firma no disponible' : 'Validación de código único de verificación',
              content: isOrderError
                ? 'No puedes firmar en este momento porque aún no es tu turno. Debes esperar a que el firmante anterior complete su proceso de firma.'
                : response.data.mensaje,
            },
          });
        }
      })
      .catch((error) => console.log(error));
  };

  // redirigir a home (botón cuando ocurre un error)
  const GoHome = async () => {
    saveStorage('docsToSign', '');
    await router.push('/');
  };

  const toggleShowAgreement = () => {
    setShowAgreement(!showAgreement);
  };

  return (
    <div>
      {showAgreement && (
        <SignAgreementModal
          onClose={toggleShowAgreement}
        />
      )}
      {/* si hay error */}
      {state.haveError ? (
        <Container>
          {/* Si el error es porque la persona este registrada  */}
          {state?.errorMessage?.content?.includes('No se pudo consultar en la base de datos') ? (

            <UnregisteredUser />
          ) : (
          // Cualquier otro error que traiga el servidor
            <Grid container justifyContent="center" style={{ textAlign: 'center' }}>
              <Grid item lg={12}>
                <Image className="img-responsive" alt="Firmese" src={icon} id="iconFirmese" />
                {' '}
                <Spacer.Vertical size="sm" />
                <Heading color="secondary">
                  {state.errorMessage.header || 'Lo sentimos'}
                </Heading>
                <Spacer.Vertical size="sm" />
                <Paragraph size="sm" color="secondary">
                  {state.errorMessage.content}
                </Paragraph>
                <Spacer.Vertical size="sm" />

                {router?.query?.view !== 'app' && (
                <Button
                  type="secondary"
                  isInline
                  id="buttonRegister"
                  onClick={GoHome}
                >
                  Ir al inicio
                </Button>
                )}
              </Grid>
            </Grid>

          )}
        </Container>
      ) : (
        <div>
          <StateHandler
            handleErrorButton={haytoken ? handleMessage : handleMessageClick}
            state={haytoken ? documento : state}
          >
            <Grid container>
              <Grid item lg={12}>
                <Card>
                  <Paragraph size="lg" weight="semibold">
                    Validación de proceso de firma
                  </Paragraph>
                  <Paragraph size="sm">
                    Te llegará un mensaje de texto al celular registrado con un
                    código único de verificación el cual debes digitar a
                    continuación.
                  </Paragraph>
                  <Spacer.Vertical size="lg" />
                  <CenteredContent>
                    <Image src={phoneIcon} height="150px" width="150px" alt="Teléfono" />
                  </CenteredContent>

                  <Spacer.Horizontal size="sm" />

                  <form onSubmit={handleSubmit(haytoken ? onSubmit : handleSubmitNoToken)}>
                    <div
                      className={clsx('input-group text-center', styles['container-digits'])}
                    >
                      <input
                        className={styles.digits}
                        autoFocus
                        {...digits[0]}
                      />
                                            &nbsp;
                      <input
                        className={styles.digits}
                        {...digits[1]}
                      />
                                            &nbsp;
                      <input
                        className={styles.digits}
                        {...digits[2]}
                      />
                                            &nbsp;
                      <input
                        className={styles.digits}
                        {...digits[3]}
                      />
                                            &nbsp;
                      <input
                        className={styles.digits}
                        {...digits[4]}
                      />
                                            &nbsp;
                      <input
                        className={styles.digits}
                        {...digits[5]}
                        data-is-last="true"
                      />
                    </div>
                    {
                        (userToken || requiredSigns > 1) && (
                        <SignAgreementCheck>
                          <Check
                            isCentered
                            label={(
                              <span>
                                Acepto el
                                {' '}
                                <a
                                  href="#"
                                  onClick={toggleShowAgreement}
                                >
                                  acuerdo de firma electrónica
                                </a>
                              </span>
                            )}
                            {
                                ...register('agreement', {
                                  required: {
                                    value: !!userToken
                                            || requiredSigns > 1,
                                    message: 'Debe aceptar el acuerdo de firma electrónica',
                                  },
                                })
                            }
                            hasError={Object.keys(errors).includes('agreement')}
                            helperText={
                              errors.agreement && errors.agreement.message
                            }
                          />
                        </SignAgreementCheck>
                        )
                    }
                    <Spacer.Horizontal size="sm" />
                    <div className="text-center">
                      <ConfirmButton
                        buttonType="submit"
                        id="btnAcceptSMS"
                        disabled={botonActivo}
                        isInline
                      >
                        Confirmar
                      </ConfirmButton>
                      {state.showTextWait
                        ? (
                          <TextWarning
                            id="textWait"
                          >
                            Espera un
                            momento. Estamos Firmando tu documento
                          </TextWarning>
                        )
                        : (
                          <p />
                        )}

                      {state.showWarning
                        ? (
                          <TextWarning
                            id="textWarning"
                          >
                            Debes ingresar un código para poder firmar tu documento.
                          </TextWarning>
                        )
                        : (
                          <p />
                        )}
                    </div>
                  </form>
                </Card>
              </Grid>
            </Grid>
          </StateHandler>
          {!state.isLoading && (
            <HelpText>
              <Paragraph size="sm">
                ¿No te ha llegado el mensaje de texto?
                Selecciona por donde deseas que tu código se envíe nuevamente.
              </Paragraph>
              <Spacer.Vertical size="md" />
              <div className={styles.sectionBtn}>
                <ButtonIcon color="secondary" icon="whatsapp" onClick={() => handleSubmitEmail('WTS')}>
                  <p className={styles.btnSecondary}>Whatsapp</p>
                </ButtonIcon>
                <Spacer.Horizontal size="lg" />
                <ButtonIcon color="secondary" icon="email" onClick={() => handleSubmitEmail('EML')}>
                  <p className={styles.btnSecondary}>Correo</p>
                </ButtonIcon>
                <Spacer.Horizontal size="lg" />
                <ButtonIcon color="secondary" icon="phone" onClick={() => handleSubmitEmail('SMS')}>
                  <p className={styles.btnSecondary}>SMS</p>
                </ButtonIcon>
              </div>
            </HelpText>
          )}
        </div>
      )}
    </div>
  );
}

ValidateCode.defaultProps = {
  alltoken: null,
  userToken: null,
  isNew: false,
  requiredSigns: 0,
};

ValidateCode.propTypes = {
  user: PropTypes.shape({
    email: PropTypes.string,
    expiresIn: PropTypes.number,
    idUsuarioC: PropTypes.number,
    idUsuarioA: PropTypes.string,
    access_token: PropTypes.string,
    refresh_token: PropTypes.string,
  }),
  alltoken: PropTypes.string,
  userToken: PropTypes.string,
  haytoken: PropTypes.bool.isRequired,
  isNew: PropTypes.bool,
  requiredSigns: PropTypes.number,
};

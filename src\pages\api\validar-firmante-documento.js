import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import catchApiError from '@/utils/catchApiError';

async function validarFirmanteDocumentoRoute(req, res) {
  try {
    const { usms, csms } = req.query;
    const { code } = req.body;
    const { data } = await fetchApi.post(
      '/validacion/registro/validar-firmante-documento',
      code,
      {
        headers: {
          'Content-Type': 'text/plain',
          ...getDefaultHeaders(req),
        },
        params: {
          usms,
          csms,
        },
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(validarFirmanteDocumentoRoute);

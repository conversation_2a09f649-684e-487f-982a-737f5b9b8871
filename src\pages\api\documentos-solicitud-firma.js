import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import path from 'path';
import fs from 'fs';
import catchApiError from '@/utils/catchApiError';
import getPDFPreview from '@/utils/getPDFPreview';

async function DocumentosSolicitudFirmaRoute(req, res) {
  try {
    const { user } = req.session;
    if (!user?.access_token) {
      return res.status(401).send();
    }

    const { data } = await fetchApi.post(
      '/firma/manager/solicitudes-usuario',
      undefined,
      {
        headers: {
          Authorization: `Bearer ${user.access_token}`,
          ...getDefaultHeaders(req),
        },
      },
    );
    const docs = Promise.all(data?.data?.map(async (doc) => ({
      ...doc,
      archivos: await Promise.all(doc?.archivos?.map(async (file) => {
        const pdfDir = path.join(
          __dirname,
          '../../../../tmp/',
        );

        if (!fs.existsSync(pdfDir)) {
          fs.mkdirSync(pdfDir);
        }
        const pdfPath = path.join(
          pdfDir,
          `${Math.floor(new Date().getTime() * Math.random())}.pdf`,
        );
        try {
          await fs.writeFileSync(pdfPath, file.archivo64, { encoding: 'base64' });
          const previewB64 = await getPDFPreview(pdfPath);

          await fs.unlinkSync(pdfPath);
          return {
            ...file,
            archivo64: previewB64,
          };
        } catch (e) {
          console.error(e);

          if (fs.existsSync(pdfPath)) {
            fs.unlinkSync(pdfPath);
          }

          return {
            ...file,
            archivo64: null,
          };
        }
      })),
    })));

    return res.send(await docs);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(DocumentosSolicitudFirmaRoute);

import React, { useState } from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import isEmpty from '@/utils/isEmpty';
import useMedia from '@/hooks/useMedia';
import Icon from '@/components/atoms/Icon';
import Heading from '@/components/atoms/Heading';
import styles from './Modal.module.css';
import { options } from './constants';

// Sync with ./Modal.module.css#L13-L17
const FADE_OUT_ANIMATION_TIME = 400;

const createHandlerClick = () => (event) => {
  event?.stopPropagation();
};

const createHandlerClose = ({ onClose, setOnFadeOut }) => () => {
  setOnFadeOut(true);
  setTimeout(onClose, FADE_OUT_ANIMATION_TIME);
};

const handleSecondaryAction = ({ onSecondaryAction }) => () => {
  onSecondaryAction();
};

export function Modal({
  onClose,
  secondaryAction,
  children,
  getStyles,
  type,
  isPlayground,
  title,
  isInline,
  notPadded,
  isPermanent,
}) {
  const isDesktop = useMedia(['(min-width: 992px)'], [true]);
  const [onFadeOut, setOnFadeOut] = useState(false);

  const handleClose = createHandlerClose({ onClose, setOnFadeOut });

  return (
    <div
      className={getStyles('backdrop', {
        'is-playground': isPlayground,
        'on-fade-out': onFadeOut,
      })}
      {
        ...(!isPermanent && {
          onClick: handleClose,
        })
      }
    >
      <div
        className={getStyles('modal', ['type'], {
          'is-inline': isInline,
          'not-padded': notPadded,
        })}
        onClick={createHandlerClick()}
      >
        <div className={getStyles('heading')}>
          {title && typeof title === 'string' ? <Heading size="sm">{title}</Heading> : title}
          {!!onClose && (
          <Icon
            color={isDesktop && ['secondary', 'tertiary'].includes(type) ? 'primary' : 'inverted'}
            name={isDesktop ? 'cross' : 'angleLeft'}
            background={isDesktop ? 'transparent' : 'muted'}
            onClick={handleClose}
            size={isDesktop ? 'lg' : 'md'}
          />
          )}
          {secondaryAction && !isEmpty(secondaryAction) && (
          <Icon
            color="inverted"
            name={secondaryAction?.icon}
            background="muted"
            onClick={handleSecondaryAction({
              onSecondaryAction: secondaryAction?.handler,
            })}
          />
          )}
        </div>
        <div>{children}</div>
      </div>
    </div>
  );
}

Modal.propTypes = {
  children: PropTypes.node.isRequired,
  getStyles: PropTypes.func.isRequired,
  onClose: PropTypes.func,
  secondaryAction: PropTypes.shape({
    icon: PropTypes.string,
    handler: PropTypes.func,
  }),
  type: PropTypes.oneOf(options.types),
  isPlayground: PropTypes.bool,
  title: PropTypes.oneOf([PropTypes.string, PropTypes.node]),
  isInline: PropTypes.bool,
  notPadded: PropTypes.bool,
  isPermanent: PropTypes.bool,
};

Modal.defaultProps = {
  getStyles: () => ({}),
  type: 'primary',
  title: null,
  isInline: false,
  notPadded: false,
  isPermanent: false,
};

export default withStyles(styles)(Modal);

import React from 'react';
import { render } from '@testing-library/react';
import UnregisteredUser from './UnregisteredUser';

jest.mock('@/components/organisms/RegisterForm', () => ({
  __esModule: true,
  default: jest.fn(() => 'RegisterForm mock'),
}));

describe('Tests UnregisteredUser', () => {
  it('should call RegisterForm', () => {
    const { getByText } = render(<UnregisteredUser />);
    expect(getByText('RegisterForm mock')).toBeInTheDocument();
  });
});

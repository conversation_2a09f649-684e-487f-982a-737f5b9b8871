import React, { useEffect } from 'react';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import UploadContainer from '@/components/organisms/UploadContainer';
import withAuthentication from '@/lib/withAuthentication';

export const getServerSideProps = withAuthentication;

export default function Upload({ user }) {
  const [, setUser] = useRecoilState(userState);

  useEffect(() => {
    setUser(user);
  }, [user]);

  return (
    <UploadContainer />
  );
}

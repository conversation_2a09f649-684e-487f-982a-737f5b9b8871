import React from 'react';
import {
  fireEvent, render, within, waitFor, act,
} from '@testing-library/react';
import * as Recoil from 'recoil';
import * as Notistack from 'notistack';
import * as userService from '@/services/user';
import SolicitarFirma from './SolicitarFirma';

describe('Tests SolicitarFirma', () => {
  const userMock = {
    idUsuarioC: 'idUsuarioC',
    access_token: 'access_token',
  };
  jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);

  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should render children', () => {
    const documents = {
      isMultiple: false,
    };
    const content = 'content';
    const { getByText } = render(<SolicitarFirma documentos={documents}>{content}</SolicitarFirma>);
    expect(getByText(content)).toBeInTheDocument();
  });

  it('should render selected documents', () => {
    const documents = {
      isMultiple: true,
      files: [
        {
          idArchivo: '1',
          nombreArchivo: 'chucknorris.pdf',
          fechaRegistro: '2020-01-01',
        },
        {
          idArchivo: '2',
          nombreArchivo: 'albert.pdf',
          fechaRegistro: '2022-01-01',
        },
      ],
    };
    const { getByText } = render(<SolicitarFirma documentos={documents}>Hola</SolicitarFirma>);

    documents.files.forEach((file) => {
      expect(getByText(file.nombreArchivo)).toBeInTheDocument();
    });
  });

  it('should works autocomplete', () => {
    const documents = {
      isMultiple: true,
      files: [
        {
          idArchivo: '1',
          nombreArchivo: 'chucknorris.pdf',
          fechaRegistro: '2020-01-01',
        },
        {
          idArchivo: '2',
          nombreArchivo: 'albert.pdf',
          fechaRegistro: '2022-01-01',
        },
      ],
    };
    const { getByTestId } = render(<SolicitarFirma documentos={documents}>Hola</SolicitarFirma>);
    const emails = ['<EMAIL>', '<EMAIL>'];

    const autocomplete = getByTestId('autocomplete');
    const input = within(autocomplete).getByRole('combobox');

    autocomplete.focus();
    emails.forEach(async (email) => {
      await act(() => {
        fireEvent.change(input, { target: { value: email } });
        fireEvent.keyDown(autocomplete, { key: 'ArrowDown' });
        fireEvent.keyDown(autocomplete, { key: 'Enter' });
      });
      const chip = within(autocomplete).getByRole('button', { name: email });
      expect(chip).toHaveClass('MuiAutocomplete-tag');
    });
  });

  it('should call solicitarMultipleFirma', async () => {
    const documents = {
      isMultiple: true,
      files: [
        {
          idArchivo: '1',
          nombreArchivo: 'chucknorris.pdf',
          fechaRegistro: '2020-01-01',
        },
        {
          idArchivo: '2',
          nombreArchivo: 'albert.pdf',
          fechaRegistro: '2022-01-01',
        },
      ],
    };
    const email = '<EMAIL>';

    const solicitarMultipleFirmaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: [],
    }));
    jest.spyOn(userService, 'solicitarMultipleFirma').mockImplementation(solicitarMultipleFirmaMock);

    const { getByRole, getByTestId } = render(<SolicitarFirma documentos={documents} />);

    const autocomplete = getByTestId('autocomplete');
    const input = within(autocomplete).getByRole('combobox');

    autocomplete.focus();

    await act(() => {
      fireEvent.change(input, { target: { value: email } });
      fireEvent.keyDown(autocomplete, { key: 'ArrowDown' });
      fireEvent.keyDown(autocomplete, { key: 'Enter' });
    });

    await act(() => {
      fireEvent.click(getByRole('button', { name: /Solicitar firmas/i }));
    });

    const state = {
      firmantes: [
        {
          email,
        },
      ],
      documentos: documents.files.map((file) => ({
        idArchivo: file.idArchivo,
        idUsuario: userMock.idUsuarioC,
      })),
      tipoFirma: 'MULTIPLE',
    };

    await waitFor(() => {
      expect(solicitarMultipleFirmaMock).toHaveBeenCalledTimes(1);
      expect(solicitarMultipleFirmaMock).toHaveBeenCalledWith(state, userMock.access_token);
    });
  });

  it('should show error message if click in button and not select any signer', () => {
    const documents = {
      isMultiple: true,
      files: [
        {
          idArchivo: '1',
          nombreArchivo: 'chucknorris.pdf',
          fechaRegistro: '2020-01-01',
        },
        {
          idArchivo: '2',
          nombreArchivo: 'albert.pdf',
          fechaRegistro: '2022-01-01',
        },
      ],
    };

    const { getByRole } = render(<SolicitarFirma documentos={documents} />);
    fireEvent.click(getByRole('button', { name: /Solicitar firmas/i }));

    expect(enqueueSnackbarMock).toHaveBeenCalledTimes(1);
    expect(enqueueSnackbarMock).toHaveBeenCalledWith('Debe seleccionar al menos un firmante', { variant: 'error' });
  });

  it('should show error message if response status not is 200', async () => {
    const documents = {
      isMultiple: true,
      files: [
        {
          idArchivo: '1',
          nombreArchivo: 'chucknorris.pdf',
          fechaRegistro: '2020-01-01',
        },
        {
          idArchivo: '2',
          nombreArchivo: 'albert.pdf',
          fechaRegistro: '2022-01-01',
        },
      ],
    };
    const email = '<EMAIL>';

    const message = 'Error';
    const solicitarMultipleFirmaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'solicitarMultipleFirma').mockImplementation(solicitarMultipleFirmaMock);

    const { getByRole, getByTestId } = render(<SolicitarFirma documentos={documents} />);

    const autocomplete = getByTestId('autocomplete');
    const input = within(autocomplete).getByRole('combobox');

    autocomplete.focus();
    await act(() => {
      fireEvent.change(input, { target: { value: email } });
      fireEvent.keyDown(autocomplete, { key: 'ArrowDown' });
      fireEvent.keyDown(autocomplete, { key: 'Enter' });
    });

    fireEvent.click(getByRole('button', { name: /Solicitar firmas/i }));

    await waitFor(() => {
      expect(enqueueSnackbarMock).toHaveBeenCalledTimes(1);
      expect(enqueueSnackbarMock).toHaveBeenCalledWith(message, { variant: 'error' });
    });
  });

  it('should catch request exception', async () => {
    const documents = {
      isMultiple: true,
      files: [
        {
          idArchivo: '1',
          nombreArchivo: 'chucknorris.pdf',
          fechaRegistro: '2020-01-01',
        },
        {
          idArchivo: '2',
          nombreArchivo: 'albert.pdf',
          fechaRegistro: '2022-01-01',
        },
      ],
    };
    const email = '<EMAIL>';

    const error = new Error('Error');
    const solicitarMultipleFirmaMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'solicitarMultipleFirma').mockImplementation(solicitarMultipleFirmaMock);

    const logMock = jest.fn();
    jest.spyOn(console, 'log').mockImplementation(logMock);

    const { getByRole, getByTestId } = render(<SolicitarFirma documentos={documents} />);

    const autocomplete = getByTestId('autocomplete');
    const input = within(autocomplete).getByRole('combobox');

    autocomplete.focus();
    await act(() => {
      fireEvent.change(input, { target: { value: email } });
      fireEvent.keyDown(autocomplete, { key: 'ArrowDown' });
      fireEvent.keyDown(autocomplete, { key: 'Enter' });
    });

    fireEvent.click(getByRole('button', { name: /Solicitar firmas/i }));

    await waitFor((() => {
      expect(logMock).toHaveBeenCalled();
      expect(logMock).toHaveBeenCalledWith(error);
    }));
  });

  it('should call handleClick on click in back button', () => {
    const documents = {
      isMultiple: true,
      files: [
        {
          idArchivo: '1',
          nombreArchivo: 'chucknorris.pdf',
          fechaRegistro: '2020-01-01',
        },
        {
          idArchivo: '2',
          nombreArchivo: 'albert.pdf',
          fechaRegistro: '2022-01-01',
        },
      ],
    };
    const handleClickMock = jest.fn();
    const { getByRole } = render(<SolicitarFirma
      handleClick={handleClickMock}
      documentos={documents}
    />);
    fireEvent.click(getByRole('button', { name: /Volver al inicio/i }));
    expect(handleClickMock).toHaveBeenCalledTimes(1);
  });
});

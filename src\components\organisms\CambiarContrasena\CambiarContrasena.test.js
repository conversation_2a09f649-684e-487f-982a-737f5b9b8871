import React from 'react';
import {
  render, screen, fireEvent, waitFor,
} from '@testing-library/react';
import * as Notistack from 'notistack';
import * as userService from '@/services/user';
import CambiarContrasena from './index';

describe('Test CambiarContrasena', () => {
  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));

  beforeEach(() => {
    render(<CambiarContrasena />);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    expect(screen.getByText(/Solicitud de cambio de contraseña/i)).toBeInTheDocument();

    expect(screen.getByPlaceholderText(/Correo electrónico/i)).toBeInTheDocument();
  });

  it('should works input', () => {
    const emailInput = screen.getByPlaceholderText(/Correo electrónico/i);

    const email = '<EMAIL>';
    fireEvent.change(emailInput, { target: { value: email } });
    expect(emailInput.value).toBe(email);
  });

  it('should not send request if email is empty', async () => {
    const button = screen.getByRole('button');

    fireEvent.click(button);

    expect(await screen.findByText(/El correo electrónico es requerido/i)).toBeInTheDocument();
  });

  it('should send request if email is not empty', async () => {
    const emailInput = screen.getByPlaceholderText(/Correo electrónico/i);
    const button = screen.getByRole('button');
    const solicitarCambioContrasenaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: [],
      },
    }));
    jest.spyOn(userService, 'solicitarCambioContrasena').mockImplementation(solicitarCambioContrasenaMock);

    const email = '<EMAIL>';

    fireEvent.change(emailInput, { target: { value: email } });
    fireEvent.click(button);
    await waitFor(() => {
      expect(solicitarCambioContrasenaMock).toHaveBeenCalled();
      expect(solicitarCambioContrasenaMock).toHaveBeenCalledWith({ username: email });
    });
  });

  it('should show warning notification if server response status not is 200', async () => {
    const emailInput = screen.getByPlaceholderText(/Correo electrónico/i);
    const button = screen.getByRole('button');
    const message = 'Error';
    const solicitarCambioContrasenaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'solicitarCambioContrasena').mockImplementation(solicitarCambioContrasenaMock);
    const params = [message, { variant: 'warning' }];

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(button);

    await waitFor(() => {
      expect(enqueueSnackbarMock).toHaveBeenCalledTimes(1);
      expect(enqueueSnackbarMock).toHaveBeenCalledWith(...params);
    });
  });

  it('should catch request exception', async () => {
    const emailInput = screen.getByPlaceholderText(/Correo electrónico/i);
    const button = screen.getByRole('button');
    const error = new Error('Error');
    const solicitarCambioContrasenaMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'solicitarCambioContrasena').mockImplementation(solicitarCambioContrasenaMock);

    const logMock = jest.fn();
    jest.spyOn(console, 'log').mockImplementation(logMock);

    fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
    fireEvent.click(button);

    await waitFor(() => {
      expect(logMock).toHaveBeenCalled();
      expect(logMock).toHaveBeenCalledWith(error);
    });
  });
});

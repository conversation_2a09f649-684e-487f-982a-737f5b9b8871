import * as Recoil from 'recoil';
import DocsToSign from '@/components/organisms/DocsToSign/DocsToSign';
import * as userService from '@/services/user';
import * as utils from '@/utils/utils';
import * as Router from 'next/router';
import { render, waitFor } from '@testing-library/react';
import { checkAllValuesInDom } from '@/utils/forTesting';

jest.mock('@/components/organisms/StateHandler', () => ({
  __esModule: true,
  // eslint-disable-next-line max-len
  default: ({ state: { haveError, errorMessage }, children }) => (haveError ? errorMessage.content : children),
}));

const signatureRequestsMock = [
  {
    token: 'token12334',
    propietario: {
      nombreCompleto: '<PERSON>',
      numeroDocumento: 123456789,
    },
    archivos: [
      {
        idArchivo: 'Peter1',
        nombreArchivo: 'ShoppingList.pdf',
        ip: '0.0.0.0',
      },
    ],
  },
  {
    token: 'token2',
    propietario: {
      nombreCompleto: '<PERSON>',
      numeroDocumento: 987654321,
    },
    archivos: [
      {
        idArchivo: '<PERSON>1',
        nombreArchivo: 'ArkManual.pdf',
        ip: '*******',
      },
    ],
  },
];

describe('Tests DocsToSign', () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  const userMock = {
    idUsuarioC: 'idUsuarioC',
    access_token: 'access_token',
  };

  jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);

  const component = (
    <Recoil.RecoilRoot>
      <DocsToSign />
    </Recoil.RecoilRoot>
  );

  it('should execute `documentosSolicitudFirma` when the component is mount', () => {
    const documentosSolicitudFirmaMock = jest.fn().mockResolvedValue({
      status: 200,
    });

    jest.spyOn(userService, 'documentosSolicitudFirma').mockImplementation(documentosSolicitudFirmaMock);

    render(component);

    expect(documentosSolicitudFirmaMock).toHaveBeenCalledTimes(1);
    expect(documentosSolicitudFirmaMock).toHaveBeenCalledWith(userMock.access_token);
  });

  it('should show error message if `documentosSolicitudFirma` response status is not 200', async () => {
    const documentosSolicitudFirmaMock = jest.fn().mockResolvedValue({
      status: 500,
    });

    jest.spyOn(userService, 'documentosSolicitudFirma').mockImplementation(documentosSolicitudFirmaMock);

    const { findByText } = render(component);

    const errorMessage = await findByText('Lo sentimos ocurrió un error al traer la información del documento, por favor, intenta más tarde.');

    expect(errorMessage).toBeInTheDocument();
  });

  it('should show info message when there are not documents signature requests', async () => {
    const documentosSolicitudFirmaMock = jest.fn().mockResolvedValue({
      status: 200,
      data: {
        data: [],
      },
    });

    jest.spyOn(userService, 'documentosSolicitudFirma').mockImplementation(documentosSolicitudFirmaMock);

    const { findByText } = render(component);

    const infoMessage = await findByText('Por el momento no cuentas con solicitud de firmas.');
    expect(infoMessage).toBeInTheDocument();
  });

  it('should show documents signature requests info', async () => {
    const documentosSolicitudFirmaMock = jest.fn().mockResolvedValue({
      status: 200,
      data: signatureRequestsMock,
    });

    jest.spyOn(userService, 'documentosSolicitudFirma').mockImplementation(documentosSolicitudFirmaMock);

    render(component);

    signatureRequestsMock.forEach(async (request) => {
      await waitFor(() => {
        checkAllValuesInDom(request, ['idArchivo', 'token']);
      });
    });
  });

  it('should redirect to `/informacion-documento` when click in the document signature request info icon', async () => {
    const documentosSolicitudFirmaMock = jest.fn().mockResolvedValue({
      status: 200,
      data: {
        data: signatureRequestsMock,
      },
    });

    jest.spyOn(userService, 'documentosSolicitudFirma').mockImplementation(documentosSolicitudFirmaMock);

    const saveStorageMock = jest.fn();
    jest.spyOn(utils, 'saveStorage').mockImplementation(saveStorageMock);

    const pushMock = jest.fn();
    jest.spyOn(Router, 'useRouter').mockReturnValue({ push: pushMock });

    const { findAllByTestId } = render(component);

    const infoIcons = await findAllByTestId('information-button');

    const infoIcon = infoIcons[0];

    infoIcon.click();

    const signatureRequest = signatureRequestsMock[0];
    expect(saveStorageMock).toHaveBeenCalledWith('element', signatureRequest);
    expect(saveStorageMock).toHaveBeenCalledWith('docsToShow', signatureRequest.archivos);
    expect(saveStorageMock).toHaveBeenCalledTimes(2);

    expect(pushMock).toHaveBeenCalledWith('/informacion-documento');
    expect(pushMock).toHaveBeenCalledTimes(1);
  });
});

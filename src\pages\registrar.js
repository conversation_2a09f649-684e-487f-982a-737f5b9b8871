import React from 'react';
import ReactGA from 'react-ga';
import { withSessionSsr } from '@/lib/withSession';
import RegisterForm from '@/components/organisms/RegisterForm';

export const getServerSideProps = withSessionSsr(
  async ({ req }) => {
    const { user = null } = req.session;

    if (user?.email) {
      return {
        redirect: {
          destination: '/',
          permanent: false,
        },
      };
    }

    return {
      props: {
        user,
      },
    };
  },
);

export default function Registrar() {
  ReactGA.pageview('/registrar');

  return (<RegisterForm />);
}

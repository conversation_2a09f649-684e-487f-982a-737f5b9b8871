import React from 'react';
import PropTypes from 'prop-types';
import withStyles from '@/hocs/withStyles';
import { mapSize } from './helpers';

import styles from './Icon.module.css';
import { options, iconsMap } from './constants';

const handleClick = ({ onClick }) => (event) => {
  onClick(event);
};

export function Icon({
  id,
  className,
  onClick,
  name,
  size,
  isClickable,
  getStyles,
  background,
  isDisable,
  isCentered,
  notPadded,
  'data-testid': dataTestId,
}) {
  const icon = iconsMap[name];
  const mappedSize = mapSize(size);

  return (
    <div
      id={id}
      className={getStyles(className, 'icon', ['color', 'size', 'background', 'border'], {
        'is-clickable': isClickable || !!onClick,
        'is-disable': isDisable && (isClickable || !!onClick),
        'is-centered': isCentered,
        'not-padded': notPadded,
      })}
      style={{
        width: background === 'brand' && size === 'lg' ? 42 : mappedSize,
        height: background === 'brand' && size === 'lg' ? 42 : mappedSize,
      }}
      data-testid={dataTestId}
      onClick={(!isDisable && onClick) ? handleClick({ onClick }) : undefined}
    >
      <svg
        viewBox={icon.viewBox}
        {...Object.keys(icon).reduce((acc, key) => {
          if (['viewBox', 'svg'].includes(key)) return acc;
          return { ...acc, [key]: icon[key] };
        }, {})}
        xmlns="http://www.w3.org/2000/svg"
        width={background === 'brand' && size === 'lg' ? 42 : mappedSize}
        height={background === 'brand' && size === 'lg' ? 42 : mappedSize}
      >
        {icon.svg}
      </svg>
    </div>
  );
}

Icon.propTypes = {
  name: PropTypes.oneOf(options.names).isRequired,
  getStyles: PropTypes.func.isRequired,
  onClick: PropTypes.func,
  size: PropTypes.oneOf(options.sizes),
  color: PropTypes.oneOf(options.colors),
  background: PropTypes.oneOf(options.backgrounds),
  id: PropTypes.string,
  className: PropTypes.string,
  isClickable: PropTypes.bool,
  isDisable: PropTypes.bool,
  border: PropTypes.oneOf(options.border),
  notPadded: PropTypes.bool,
};

Icon.defaultProps = {
  size: 'md',
  color: 'base',
  background: 'transparent',
  isClickable: false,
  getStyles: () => ({}),
  isDisable: false,
  border: 'none',
  notPadded: false,
};

export default withStyles(styles)(Icon);

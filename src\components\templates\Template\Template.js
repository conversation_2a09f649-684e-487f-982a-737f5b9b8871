import React, { useEffect, useMemo, useState } from 'react';
import CssBaseline from '@mui/material/CssBaseline';
import MuiDrawer from '@mui/material/Drawer';
import MuiAppBar from '@mui/material/AppBar';
import Toolbar from '@mui/material/Toolbar';
import List from '@mui/material/List';
import Divider from '@/components/atoms/Divider';
import IconButton from '@mui/material/IconButton';
import MenuIcon from '@mui/icons-material/Menu';
import ListItem from '@mui/material/ListItem';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import { useRouter } from 'next/router';
// import { solicitarFirmaPlantillasMultiples } from '@/services/user';
import Image from 'next/image';
import { useRecoilState } from 'recoil';
import Link from 'next/link';
import Logo from '@/assets/images/logo_principal.png';
import LogoLight from '@/assets/images/alt_logo.png';
import { userState } from '@/recoil/atoms';
import PropTypes from 'prop-types';
import VerifyIcon from '@/assets/images/verify.png';
import SignDocumentsIcon from '@/assets/images/sign_documents.png';
import UploadIcon from '@/assets/images/upload.png';
import SignedDocumentsIcon from '@/assets/images/signed_documents.png';
import Copyright from '@/components/molecules/Copyright';
import Container from '@/components/layout/Container';
import Icon from '@/components/atoms/Icon';
import { decisions } from '@/tokens/index';
import Spacer from '@/components/layout/Spacer';
import useMedia from '@/hooks/useMedia';
import UserMenu from '@/components/organisms/UserMenu';
import { styled } from '@mui/material/styles';
import { useTheme } from '@mui/material';
// eslint-disable-next-line no-unused-vars
import AlertTitle from '@mui/material/AlertTitle';
import CloseIcon from '@mui/icons-material/Close';
import { useTyc } from '@/hooks/useTyc';
import { useTokenManager } from '@/hooks/useTokenManager';
import { useSnackbar } from 'notistack';
import styles from './Template.module.css';

const drawerWidth = 240;
const openedMixin = (theme) => ({
  width: drawerWidth,
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.enteringScreen,
  }),
  overflowX: 'hidden',
});

const closedMixin = (theme) => ({
  transition: theme.transitions.create('width', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  overflowX: 'hidden',
  width: `calc(${theme.spacing(7)} + 1px)`,
  [theme.breakpoints.up('sm')]: {
    width: `calc(${theme.spacing(8)} + 1px)`,
  },
});
const AppBar = styled(MuiAppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})(({ theme, open }) => ({
  zIndex: theme.zIndex.drawer + 1,
  transition: theme.transitions.create(['width', 'margin'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  ...(open && {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(['width', 'margin'], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  }),
}));
const Drawer = styled(MuiDrawer, { shouldForwardProp: (prop) => prop !== 'open' })(
  ({ theme, open }) => ({
    width: drawerWidth,
    flexShrink: 0,
    whiteSpace: 'nowrap',
    boxSizing: 'border-box',
    ...(open && {
      ...openedMixin(theme),
      '& .MuiDrawer-paper': openedMixin(theme),
    }),
    ...(!open && {
      ...closedMixin(theme),
      '& .MuiDrawer-paper': closedMixin(theme),
    }),
  }),
);

const MenuButton = (Component) => styled(Component, {
  shouldForwardProp: (prop) => prop !== 'open',
})(({ hide: open, theme }) => ({
  [theme.breakpoints.up('sm')]: {
    marginRight: 36,
  },
  ...(open && {
    display: 'none !important',
  }),
}));

const IconMenuButton = MenuButton(IconButton);

const ImageMenuButton = MenuButton(Image);

const AppBarSpacer = styled('div')(({ theme }) => theme.mixins.toolbar);

export function Template({ children }) {
  const router = useRouter();
  const theme = useTheme();
  const isMobile = useMedia(['(max-width: 600px)'], [true]);
  const [open, setOpen] = useState(false);
  const [user, setUser] = useRecoilState(userState);
  const [tycDismissed, setTycDismissed] = useState(false);

  // Hook para verificar TyC
  const tycState = useTyc(user);

  // Hook para manejar expiración del token
  useTokenManager();

  const { enqueueSnackbar } = useSnackbar();

  const handleLogout = async (event) => {
    event.preventDefault();

    try {
      console.log('Iniciando logout manual...');
      setUser({
        email: null,
        expires_in: null,
        idUsuarioC: null,
        idUsuarioA: null,
        access_token: null,
        refresh_token: null,
        enableOris: false,
      });

      // 2. Limpiar localStorage y sessionStorage
      localStorage.clear();
      sessionStorage.clear();

      // 3. Llamar al endpoint de logout
      const Axios = (await import('axios')).default;
      await Axios.post('/api/logout');

      // 4. Redirigir al login
      await router.replace('/login');
    } catch (error) {
      localStorage.clear();
      sessionStorage.clear();
      // Forzar redirección
      window.location.href = '/login';
    }
  };

  const handleFirmarTyC = async () => {
    if (!user?.access_token || !user?.idUsuarioC) {
      return;
    }

    try {
      const { solicitarFirmaPlantillas } = await import('@/services/user');

      const firmante = {
        email: user.email,
        nombreCompleto: user.nombreUsuario || user.nombreCompleto || user.email.split('@')[0],
        rol: 'Firmante',
        orden: 1,
      };

      const solicitudTyC = {
        idUsuario: user.idUsuarioC,
        plantillas: [
          {
            idPlantilla: 1,
            descripcion: 'Términos y Condiciones',
          },
          {
            idPlantilla: 2,
            descripcion: 'Autorización Datos Personales',
          },
        ],
        tipoOrden: 'PARALELO',
        fechaVigencia: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        descripcionPersonalizada: 'Firma de TyC y Tratamiento de Datos Personales',
        tipoFirma: 'MULTIPLE',
        firmantes: [firmante],
      };

      const response = await solicitarFirmaPlantillas(solicitudTyC, user.access_token);

      if (response.status === 200) {
        enqueueSnackbar(
          'La solicitud ha sido creada, revisa tu correo para realizar el proceso de firma.',
          { variant: 'success' },
        );
        setTycDismissed(true);
      } else {
        enqueueSnackbar(
          'Ocurrió un error al crear la solicitud de firma.',
          { variant: 'error' },
        );
      }
    } catch (error) {
      enqueueSnackbar(
        'Ocurrió un error al crear la solicitud de firma.',
        { variant: 'error' },
      );
    }
  };

  // Función para descartar el banner temporalmente (solo para la sesión actual)
  const handleDismissTyC = () => {
    setTycDismissed(true);
  };

  useEffect(() => {
    // Limpiar dismissed cuando cambia la ruta
    const handleRouteChange = () => {
      if (tycState.requiereFirmar) {
        setTycDismissed(false);
      }
    };

    router.events.on('routeChangeComplete', handleRouteChange);

    return () => {
      router.events.off('routeChangeComplete', handleRouteChange);
    };
  }, [router.events, tycState.requiereFirmar]);

  // Limpiar dismissed cuando el usuario ya no requiere firmar TyC
  useEffect(() => {
    if (!tycState.requiereFirmar && !tycState.isLoading) {
      setTycDismissed(false);
      // Limpiar localStorage si existe
      localStorage.removeItem('tycBannerDismissed');
    }
  }, [tycState.requiereFirmar, tycState.isLoading]);

  // Limpiar dismissed cuando hay una nueva consulta exitosa que requiere firmar
  useEffect(() => {
    if (tycState.requiereFirmar && !tycState.isLoading) {
      // Si requiere firmar, asegurar que el banner esté visible
      setTycDismissed(false);
    }
  }, [tycState.requiereFirmar, tycState.isLoading]);

  useEffect(() => {
    // LOGOUT AUTOMÁTICO - COMENTADO PORQUE AHORA USA useTokenManager
    /*
    if (user?.email && user?.idUsuarioC == null) {
      (async () => {
        const Axios = (await import('axios')).default;
        Axios.post('/api/logout').then(() => router.replace('/login'));
      })();
    }
    */
  }, [user, router.pathname]);

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = () => {
    setOpen(false);
  };

  const goHome = async () => {
    await router.replace('/');
  };

  const imageSize = useMemo(() => {
    const originalWidth = 180;
    const originalHeight = 48;
    const aspectRatio = originalWidth / originalHeight;

    if (!isMobile) {
      return {
        width: originalWidth,
        height: originalHeight,
      };
    }

    const newWidth = 120;
    const newHeight = Math.floor(newWidth / aspectRatio);

    return {
      width: newWidth,
      height: newHeight,
    };
  }, [isMobile]);

  if (router?.query?.view === 'app') {
    return (
      <div className={styles.container}>
        <CssBaseline />
        <main className={styles.content}>
          <Container>
            {children}
          </Container>
        </main>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <CssBaseline />

      {/* Banner de TyC global - aparece arriba de todo */}
      {user?.email && tycState.requiereFirmar && !tycDismissed && !tycState.isLoading && (
        <TycBanner
          mensaje={tycState.mensaje}
          onFirmarClick={handleFirmarTyC}
          onDismiss={handleDismissTyC}
        />
      )}

      <AppBar
        open={open}
        style={{
          background: decisions.color.secondary,
          // Agregar margen superior si hay banner de TyC
          marginTop: (user?.email && tycState.requiereFirmar && !tycDismissed && !tycState.isLoading) ? '48px' : 0,
        }}
        position="absolute"
      >
        <Toolbar sx={{ paddingRight: 24 }}>
          <IconMenuButton
            edge="start"
            color="inherit"
            aria-label="open drawer"
            onClick={handleDrawerOpen}
            open={open}
          >
            <MenuIcon />
          </IconMenuButton>
          <Container>
            <div style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              alignContent: 'center',
            }}
            >
              <ImageMenuButton
                src={LogoLight}
                alt="Logo2"
                open={open}
                height={imageSize.height}
                width={imageSize.width}
                onClick={goHome}
              />
              {user?.email && (
                <UserMenu />
              )}
            </div>
          </Container>
        </Toolbar>
      </AppBar>

      <Drawer
        variant="permanent"
        open={open}
      >
        <div style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          padding: '0 8px',
          ...theme.mixins.toolbar,
        }}
        >
          <IconButton
            onClick={handleDrawerClose}
          >
            <Image src={Logo} alt="Logo" height={50} width={189} />
            <Icon name="angleLeft" size="sm" />
          </IconButton>
        </div>
        <Divider />
        <List sx={{
          '& .MuiListItemText-primary': {
            color: 'initial',
          },
        }}
        >
          {user?.email && (
            <>
              <Link href="/" passHref legacyBehavior>
                <a style={{ textDecoration: 'none', color: 'inherit' }}>
                  <ListItem button>
                    <ListItemIcon>
                      <Image src={SignDocumentsIcon} alt="Firmar" height={40} width={40} />
                    </ListItemIcon>
                    <ListItemText primary="Mi unidad" />
                  </ListItem>
                </a>
              </Link>

              <Link href="/upload" passHref legacyBehavior>
                <a style={{ textDecoration: 'none', color: 'inherit' }}>
                  <ListItem button>
                    <ListItemIcon>
                      <Image src={UploadIcon} alt="Subir" height={40} width={40} />
                    </ListItemIcon>
                    <ListItemText primary="Subir documentos" />
                  </ListItem>
                </a>
              </Link>
              <Link href="/firmados" passHref legacyBehavior>
                <a style={{ textDecoration: 'none', color: 'inherit' }}>
                  <ListItem button>
                    <ListItemIcon>
                      <Image src={SignedDocumentsIcon} alt="Firmados" height={40} width={40} />
                    </ListItemIcon>
                    <ListItemText primary="Documentos firmados" />
                  </ListItem>
                </a>
              </Link>
              {!!user?.enableOris && (
                <Link href="/oris" passHref legacyBehavior>
                  <a style={{ textDecoration: 'none', color: 'inherit' }}>
                    <ListItem button>
                      <ListItemIcon>
                        <Icon background="brand" name="pen" size="md" />
                      </ListItemIcon>
                      <ListItemText primary="ORIS" />
                    </ListItem>
                  </a>
                </Link>
              )}
            </>
          )}

          <Link href="/verificar" passHref legacyBehavior>
            <a style={{ textDecoration: 'none', color: 'inherit' }}>
              <ListItem button>
                <ListItemIcon>
                  <Image src={VerifyIcon} alt="Verificar" height={40} width={40} />
                </ListItemIcon>
                <ListItemText primary="Verificar documento" />
              </ListItem>
            </a>
          </Link>
          {!user?.email ? (
            <>
              <Link href="/registrar" passHref legacyBehavior>
                <a style={{ textDecoration: 'none', color: 'inherit' }}>
                  <ListItem button>
                    <ListItemIcon>
                      <Icon background="brand" name="userAdd" size="md" />
                    </ListItemIcon>
                    <ListItemText primary="Regístrese" />
                  </ListItem>
                </a>
              </Link>
              <Link href="/login" passHref legacyBehavior>
                <a style={{ textDecoration: 'none', color: 'inherit' }}>
                  <ListItem button>
                    <ListItemIcon>
                      <Icon background="brand" name="logIn" size="md" />
                    </ListItemIcon>
                    <ListItemText primary="Iniciar sesión" />
                  </ListItem>
                </a>
              </Link>
            </>
          ) : (
            <ListItem button onClick={handleLogout}>
              <ListItemIcon>
                <Icon background="brand" name="logOut" size="md" />
              </ListItemIcon>
              <ListItemText primary="Cerrar sesión" />
            </ListItem>
          )}
        </List>
      </Drawer>
      <main className={styles.content}>
        <AppBarSpacer />

        <Container>
          <div>
            <Spacer.Vertical size="lg" />
            {children}
            <Spacer.Vertical size="lg" />
          </div>
          <div>
            <Copyright />
          </div>
        </Container>
      </main>
    </div>
  );
}

Template.propTypes = {
  children: PropTypes.node.isRequired,
};

export default Template;

function TycBanner({ mensaje, onFirmarClick, onDismiss }) {
  const procesarMensaje = (mensajeOriginal) => {
    if (!mensajeOriginal) {
      return 'Debes firmar los TyC y el tratamiento de datos personales para continuar usando la plataforma.';
    }
    return mensajeOriginal
      .replace(/Usuario requiere firmar los Términos y Condiciones/gi, 'Debes firmar los TyC y el tratamiento de datos personales')
      .replace(/Términos y Condiciones/gi, 'TyC')
      .replace(/términos y condiciones/gi, 'TyC');
  };

  return (
    <div
      style={{
        backgroundColor: '#ff9800',
        color: 'white',
        padding: '8px 16px',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 9999,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        fontSize: '14px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        minHeight: '48px',
      }}
    >
      <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
        <span style={{ fontSize: '16px' }}>⚠️</span>
        <div>
          <strong>TyC y Tratamiento de Datos Pendientes:</strong>
          <span style={{ marginLeft: '8px' }}>
            {procesarMensaje(mensaje)}
          </span>
        </div>
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <button
          type="button"
          onClick={onFirmarClick}
          style={{
            backgroundColor: 'rgba(255, 255, 255, 0.2)',
            color: 'white',
            border: '1px solid rgba(255, 255, 255, 0.3)',
            padding: '6px 16px',
            borderRadius: '4px',
            cursor: 'pointer',
            fontSize: '13px',
            fontWeight: '500',
          }}
        >
          Crear solicitud de firma
        </button>
        {onDismiss && (
          <IconButton
            size="small"
            onClick={onDismiss}
            style={{ color: 'white', padding: '4px' }}
          >
            <CloseIcon fontSize="small" />
          </IconButton>
        )}
      </div>
    </div>
  );
}

TycBanner.propTypes = {
  mensaje: PropTypes.string,
  onFirmarClick: PropTypes.func.isRequired,
  onDismiss: PropTypes.func,
};

TycBanner.defaultProps = {
  mensaje: '',
  onDismiss: null,
};

import React from 'react';
import CambiarContrasena from '@/components/organisms/CambiarContrasena';
import { withSessionSsr } from '@/lib/withSession';

export const getServerSideProps = withSessionSsr(
  async ({ req }) => {
    const { user = null } = req.session;

    if (user?.email) {
      return {
        redirect: {
          destination: '/',
          permanent: false,
        },
      };
    }

    return {
      props: {
        user,
      },
    };
  },
);

export default function OlvideContrasena() {
  return (
    <CambiarContrasena />
  );
}

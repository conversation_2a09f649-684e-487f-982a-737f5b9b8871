import React from 'react';
import { render, waitFor } from '@testing-library/react';
import { documentInfoMock } from '@/utils/forTesting';
import * as servicioUsuario from '@/services/user';
import DocumentQR from './index';

const qr = '123456789';
const savedLocation = window.location;
describe('Tests DocumentQR', () => {
  beforeEach(() => {
    delete window.location;
    window.location = Object.assign(new URL('http://test.firme.se:3191/'), {
      search: `?qr=${qr}`,
    });
  });

  afterEach(() => {
    window.location = savedLocation;
  });

  it('should verify qr when component is mounted', () => {
    const getVerifyQRMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: [
          {
            ...documentInfoMock,
            fechaRegistroStr: '2020-01-01',
          },
        ],
        codigoTransaccion: '123456789',
      },
    }));
    jest.spyOn(servicioUsuario, 'getVerifyQR').mockImplementation(getVerifyQRMock);

    render(<DocumentQR />);

    expect(getVerifyQRMock).toHaveBeenCalled();
    expect(getVerifyQRMock).toHaveBeenCalledWith(qr);
  });

  it('should catch request exception', async () => {
    const error = new Error('Error');
    const getVerifyQRMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(servicioUsuario, 'getVerifyQR').mockImplementation(getVerifyQRMock);

    const logErrorMock = jest.fn();
    jest.spyOn(console, 'error').mockImplementation(logErrorMock);

    render(<DocumentQR />);

    await waitFor(() => {
      expect(logErrorMock).toHaveBeenCalled();
      expect(logErrorMock).toHaveBeenCalledWith(error);
    });
  });
});

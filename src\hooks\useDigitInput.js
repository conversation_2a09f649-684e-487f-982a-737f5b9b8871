import { useRef } from 'react';

function padValue(value, length) {
  let val = value;
  while (val.length < length) {
    val += ' ';
  }
  return val.substring(0, length);
}

function setSelectionRange(currentTarget) {
  if (currentTarget.value.trim() !== '' && currentTarget.attributes['data-is-last']?.value === 'true') {
    // Double the length because Opera is inconsistent about
    // whether a carriage return is one character or two.
    const len = currentTarget.value.length * 2;
    currentTarget.setSelectionRange(len, len);
    return;
  }
  currentTarget.setSelectionRange(0, 1);
}

function isTextSelected(input) {
  if (typeof input.selectionStart === 'number') {
    return input.selectionStart === 0 && input.selectionEnd === input.value.length;
  } if (typeof document.selection !== 'undefined') {
    input.focus();
    return document.selection.createRange().text === input.value;
  }
}

export default function useDigitInput({
  acceptedCharacters,
  length,
  value,
  onChange,
}) {
  const val = padValue(value, length);
  const inputs = useRef(Array.from({ length }));
  const inputRefSetters = useRef(
    Array.from({ length }),
  );

  const props = [];
  // eslint-disable-next-line no-plusplus
  for (let i = 0; i < length; i++) {
    // eslint-disable-next-line no-multi-assign
    const ref = (inputRefSetters.current[i] = inputRefSetters.current[i]
            || ((input) => {
              inputs.current[i] = input || undefined;
            }));
    const digitValue = val[i] === ' ' ? '' : val[i];

    props.push({
      ref,
      value: digitValue,
      onClick: ({ currentTarget }) => {
        window.requestAnimationFrame(() => {
          currentTarget.setSelectionRange(0, 1);
        });
      },
      onFocus: ({ currentTarget }) => {
        setSelectionRange(currentTarget);
      },
      onKeyDown: (e) => {
        switch (e.key) {
          case 'Backspace':
            e.preventDefault();
            if (!digitValue) {
              if (i > 0) {
                // this digit is empty, so backspace removes the previous digit
                // and focuses it
                onChange(`${val.substring(0, i - 1)} ${val.substring(i)}`);
                const previousInput = inputs.current[i - 1];
                if (previousInput) {
                  previousInput.focus();
                }
              }
            } else {
              // this digit is not empty, so backspace removes that digit
              onChange(`${val.substring(0, i)} ${val.substring(i + 1)}`);
            }
            break;
          case 'ArrowUp':
          case 'ArrowLeft':
            e.preventDefault();
            if (i > 0) {
              const previousInput = inputs.current[i - 1];
              if (previousInput) {
                previousInput.focus();
                window.requestAnimationFrame(() => {
                  previousInput.setSelectionRange(0, 1);
                });
              }
            }
            break;
          case 'ArrowDown':
          case 'ArrowRight':
            e.preventDefault();
            if (i + 1 < length) {
              const nextInput = inputs.current[i + 1];
              if (nextInput) {
                nextInput.focus();
                window.requestAnimationFrame(() => {
                  nextInput.setSelectionRange(0, 1);
                });
              }
            }
            break;
          default:
            if (e.key.length === 1 && !(e.metaKey || e.altKey || e.ctrlKey)) {
              e.preventDefault();
              if (acceptedCharacters.test(e.key)) {
                if (e.target.value.trim() !== '' && e.target.attributes['data-is-last']?.value === 'true' && !isTextSelected(e.target)) {
                  return;
                }
                onChange(val.substring(0, i) + e.key + val.substring(i + 1));
                if (i + 1 < length) {
                  const nextInput = inputs.current[i + 1];
                  if (nextInput) {
                    nextInput.focus();
                    setSelectionRange(nextInput);
                  }
                } else {
                  const { currentTarget } = e;

                  setSelectionRange(currentTarget);
                }
              }
            }
        }
      },
      onChange: (e) => {
        const v = e.target.value
          .split('')
          .filter((c) => acceptedCharacters.test(c))
          .join('');
        onChange(
          (val.substring(0, i) + v + val.substring(i + v.length)).substr(
            0,
            length,
          ),
        );
        if (i < length - 1) {
          const nextInput = inputs.current[i + v.length < length ? i + v.length : length - 1];
          if (nextInput) {
            nextInput.focus();
            setSelectionRange(nextInput);
          }
        }
      },
    });
  }
  return props;
}

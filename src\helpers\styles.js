import clsx from 'clsx';

export function getDynamicClasses(cssModule = {}, props = {}, classes = []) {
  return classes.reduce((classesObject, classKey) => {
    const propValue = props[classKey];
    const className = cssModule[`${classKey}-${propValue}`];

    return className && propValue
      ? { ...classesObject, [className]: propValue }
      : classesObject;
  }, {});
}

export function getModuleClasses(cssModule, classKey) {
  return (cssModule && cssModule[classKey]) || classKey;
}

export function getObjectClasses(cssModule, object) {
  return Object.keys(object).reduce((classes, classKey) => {
    const className = cssModule[classKey];
    return className ? { ...classes, [className]: object[classKey] } : classes;
  }, {});
}

export function getClasses(cssModule = {}) {
  return (props) => (...args) => clsx(
    args.map((arg) => {
      if (Array.isArray(arg)) {
        return getDynamicClasses(cssModule, props, arg);
      }
      if (typeof arg === 'string') {
        return getModuleClasses(cssModule, arg);
      }
      if (typeof arg === 'object') {
        return getObjectClasses(cssModule, arg);
      }
      return {};
    }),
  );
}

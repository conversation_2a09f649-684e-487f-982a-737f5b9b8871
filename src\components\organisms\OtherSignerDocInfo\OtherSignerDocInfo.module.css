.user-info {
    border-spacing: 0;
    border-collapse: collapse;
}
.user-info :global(tbody) {
    display: table-row-group;
}

.user-info :global(tr) {
    color: inherit;
    display: table-row;
    outline: 0;
    vertical-align: middle;
}

.user-info :global(tr td), .user-info :global(tr th) {
    display: table-cell;
    padding: 16px;
    border-bottom: 1px solid rgba(224,224,224, 1);
}

.user-info :global(tr th) {
    text-align: left;
}

.grid-card {
    display: grid;
    gap: var(--doc-card-gap);
    grid-template-columns: 100%;
    margin-bottom: var(--doc-card-margin-bottom);
}

@media (min-width: 768px) {
    .grid-card {
        grid-template-columns: 50% 50%;
    }
}


.card-header {
    padding: 12px 10px;
    background: var(--color-secondary);
    border-radius: var(--border-radius-sm) var(--border-radius-sm) 0 0;
    max-width: 100%;
}

.card-header h1 {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.preview {
    width: 100%;
    max-height: 15rem;
    object-fit: cover;
    object-position: top left;
    background: var(--color-base-white);
}
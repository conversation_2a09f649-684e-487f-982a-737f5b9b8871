import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import catchApiError from '@/utils/catchApiError';

async function solicitarTokenRegistroRoute(req, res) {
  try {
    const { codigo } = req.body;

    const { data } = await fetchApi.post(
      `/token/registro/solicitar-token-registro?codigo=${codigo}`,
      undefined,
      {
        headers: getDefaultHeaders(req),
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(solicitarTokenRegistroRoute);

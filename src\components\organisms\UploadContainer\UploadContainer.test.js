import React from 'react';
import { render } from '@testing-library/react';
import { RecoilRoot } from 'recoil';
import UploadContainer from './UploadContainer';

jest.mock('@/components/organisms/DropZone', () => ({
  __esModule: true,
  default: jest.fn(() => 'DropZone mock'),
}));

describe('Tests UploadContainer', () => {
  it('should call DropZone', () => {
    const { getByText } = render(<RecoilRoot><UploadContainer /></RecoilRoot>);
    expect(getByText('DropZone mock')).toBeInTheDocument();
  });
});

import { PDFImage } from 'pdf-image';
import fs from 'fs';

export default async function getPDFPreview(pdfPath, { encoding = 'base64', pageNumber = 0 } = {}) {
  if (!fs) {
    throw new Error('fs module is not available');
  }

  if (!fs.existsSync(pdfPath)) {
    throw new Error(`File ${pdfPath} does not exist`);
  }

  const pdfImage = new PDFImage(pdfPath);
  const previewPath = await pdfImage.convertPage(pageNumber || 0);
  const preview = fs.readFileSync(previewPath, { encoding });

  if (fs.existsSync(previewPath)) {
    await fs.unlinkSync(previewPath);
  }

  return preview;
}

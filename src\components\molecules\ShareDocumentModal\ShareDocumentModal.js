import React, { useMemo } from 'react';
import Autocomplete, { createFilterOptions } from '@mui/material/Autocomplete';
import TextField from '@mui/material/TextField';
import Modal from '@/components/atoms/Modal';
import PropTypes from 'prop-types';
import isValidEmail from '@/utils/isValidEmail';
import { sendNotification } from '@/services/user';
import { useSnackbar } from 'notistack';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';

export function ShareDocumentModal({
  onClose,
  idArchivo,
}) {
  const [user] = useRecoilState(userState);

  const defaultShareTo = useMemo(() => {
    if (!user?.idUsuarioA) {
      return [{ email: '' }];
    }

    return [{ email: user.idUsuarioA }];
  }, [user]);

  const filter = createFilterOptions();

  const { enqueueSnackbar } = useSnackbar();
  const [shareTo, setShareTo] = React.useState(defaultShareTo);
  const shareDocument = async (e) => {
    e.preventDefault();
    try {
      const emails = shareTo?.map(({ email }) => email) || [];
      if (!emails?.length) {
        return enqueueSnackbar('Debes seleccionar al menos una persona', {
          variant: 'error',
        });
      }

      const errors = emails?.filter((email) => !isValidEmail(email)) || [];

      if (errors?.length) {
        return enqueueSnackbar(`Correos no validos: ${errors.join(', ')}`, {
          variant: 'error',
        });
      }
      const files = [{
        idArchivo,
      }];

      const payload = {
        correos: emails.join(','),
        archivos: files,
      };

      const { status, data: response } = await sendNotification(payload, user?.access_token);
      if (status === 200) {
        enqueueSnackbar(response.mensaje, { variant: 'success' });
        onClose();
      } else {
        enqueueSnackbar(response.mensaje, { variant: 'error' });
      }
    } catch (error) {
      console.error(error);
    }
  };
  return (
    <Modal
      onClose={onClose}
      type="tertiary"
      title="Compartir Documento"
      isInline
    >
      <form onSubmit={shareDocument}>
        <Autocomplete
          multiple
          id="tags-outlined"
          options={[{ email: '' }]}
          getOptionLabel={({ email }) => email}
          defaultValue={[{ email: user?.idUsuarioA }]}
          onChange={
            (_event, newValue) => {
              setShareTo([...new Set(newValue.map((nv) => nv?.email))].map((email) => ({ email })));
            }
          }
          filterOptions={(options, params) => {
            const filtered = filter(options, params);
            const selectedEmails = shareTo?.map(({ email }) => email?.toLowerCase()) || [];

            // Suggest the creation of a new value
            if (params.inputValue !== '' && !selectedEmails.includes(params.inputValue?.toLowerCase())) {
              filtered.push({
                email: `${params.inputValue}`,
              });
            }

            return filtered;
          }}
          renderInput={(params) => (
            <TextField
              {...params}
              variant="outlined"
              label="Correo electrónico"
              placeholder="Agrega correo electrónico de firmante"
            />
          )}
        />
        <Spacer.Vertical size="sm" />
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            type="primary"
            buttonType="submit"
            isInline
          >
            Enviar
          </Button>
        </div>
      </form>
    </Modal>
  );
}

ShareDocumentModal.propTypes = {
  onClose: PropTypes.func.isRequired,
  idArchivo: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,
};

export default ShareDocumentModal;

import React, { useState, useEffect } from 'react';
import <PERSON><PERSON><PERSON><PERSON>on from '@mui/material/Button';
import Grid from '@mui/material/Grid';
import Popper from '@mui/material/Popper';
import Grow from '@mui/material/Grow';
import Paper from '@mui/material/Paper';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import MenuList from '@mui/material/MenuList';
import MenuItem from '@mui/material/MenuItem';
import { useRouter } from 'next/router';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import useMedia from '@/hooks/useMedia';
import Icon from '@/components/atoms/Icon';
import Paragraph from '@/components/atoms/Paragraph';
import { styled } from '@mui/material/styles';

const UserInfoComponent = styled(Grid, {
  container: true,
  item: true,
  direction: 'column',
})(({ theme }) => ({
  width: 'max-content',
  [theme.breakpoints.down('sm')]: {
    display: 'none',
  },
}));

export function UserMenu() {
  const isMobile = useMedia(['(max-width: 600px)'], [true]);
  const router = useRouter();
  const [user] = useRecoilState(userState);

  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const handleToggleUserMenu = () => {
    setUserMenuOpen((prevOpen) => !prevOpen);
  };

  const anchorRef = React.useRef(null);
  const handleUserMenuClose = (event) => {
    if (anchorRef.current && anchorRef.current.contains(event.target)) {
      return;
    }

    setUserMenuOpen(false);
  };

  function handleUserMenuListKeyDown(event) {
    if (event.key === 'Tab') {
      event.preventDefault();
      setUserMenuOpen(false);
    }
  }

  const prevUserMenuOpen = React.useRef(userMenuOpen);
  useEffect(() => {
    if (prevUserMenuOpen.current === true && userMenuOpen === false) {
      anchorRef.current.focus();
    }

    prevUserMenuOpen.current = userMenuOpen;
  }, [userMenuOpen]);

  const handleProfileClick = async () => {
    await router.push('/perfil');
    setUserMenuOpen(false);
  };

  return (
    <div
      style={{ textAlign: 'right' }}
    >
      <MuiButton
        ref={anchorRef}
        aria-controls="simple-menu"
        aria-haspopup="true"
        onClick={handleToggleUserMenu}
        style={{ minWidth: 'max-content' }}
      >
        <Grid
          container
          direction="row"
          spacing={1}
          justifyContent="flex-end"
          alignItems="center"
          style={{ textAlign: 'right', color: '#fff' }}
        >
          <Grid item>
            <Icon name="userCircle" size={isMobile ? 'md' : 'lg'} color="inverted" notPadded />
          </Grid>
          <UserInfoComponent container item direction="column">
            <Grid item>
              <Paragraph size="xs" color="inverted">
                {user?.nombreUsuario || ''}
              </Paragraph>
            </Grid>
            <Grid item>
              <Paragraph size="xs" color="inverted">
                {user?.idUsuarioA || ''}
              </Paragraph>
            </Grid>
          </UserInfoComponent>
          <Grid item>
            <Icon name="angleDown" color="inverted" size={isMobile ? 'sm' : 'md'} />
          </Grid>
        </Grid>
      </MuiButton>
      <Popper
        open={userMenuOpen}
        anchorEl={anchorRef.current}
        role={undefined}
        transition
        disablePortal
        placement="bottom-end"
      >
        {({ TransitionProps }) => (
          <Grow
            {...TransitionProps}
          >
            <Paper>
              <ClickAwayListener onClickAway={handleUserMenuClose}>
                <MenuList
                  autoFocusItem={userMenuOpen}
                  id="menu-list-grow"
                  onKeyDown={handleUserMenuListKeyDown}
                >
                  <MenuItem onClick={handleProfileClick}>Perfil</MenuItem>
                </MenuList>
              </ClickAwayListener>
            </Paper>
          </Grow>
        )}
      </Popper>
    </div>
  );
}

export default UserMenu;

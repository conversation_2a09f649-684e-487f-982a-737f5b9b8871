import React, { useEffect } from 'react';
import { useRouter } from 'next/router';
import { useRecoilState } from 'recoil';
import { withSessionSsr } from '@/lib/withSession';
import ValidaTokenMultiple from '@/components/organisms/ValidaTokenMultiple';
import { userState } from '@/recoil/atoms';

export const getServerSideProps = withSessionSsr(
  async ({ req }) => {
    const { user = null } = req.session;

    return {
      props: {
        user,
      },
    };
  },
);

export default function MultipleFirmaToken({ user }) {
  const router = useRouter();
  const { token } = router.query;
  const [, setUser] = useRecoilState(userState);

  useEffect(() => {
    setUser(user);
  }, [user]);

  return (
    <ValidaTokenMultiple token={token} />
  );
}

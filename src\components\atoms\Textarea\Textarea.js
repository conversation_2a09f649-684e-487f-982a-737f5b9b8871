import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import styles from './Textarea.module.css';

const DEFAULT_TEXTAREA_ROWS = 5;

export const Textarea = React.forwardRef(({
  children,
  rows,
  placeholder,
  onChange,
  getStyles,
  required,
  ...props
}, ref) => (
  <textarea
    className={getStyles('textarea')}
    rows={rows}
    placeholder={placeholder}
    onChange={onChange}
    ref={ref}
    required={required}
    {...props}
  >
    {children}
  </textarea>
));

Textarea.propTypes = {
  children: PropTypes.node,
  getStyles: PropTypes.func.isRequired,
  onChange: PropTypes.func.isRequired,
  rows: PropTypes.number,
  placeholder: PropTypes.string,
  required: PropTypes.bool,
};

Textarea.defaultProps = {
  getStyles: () => ({}),
  onChange: () => { /* */ },
  rows: DEFAULT_TEXTAREA_ROWS,
  required: false,
};

export default withStyles(styles)(Textarea);

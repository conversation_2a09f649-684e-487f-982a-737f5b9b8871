import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function validarOrdenFirmaRoute(req, res) {
  try {
    const { user } = req.session;
    if (!user?.access_token) {
      return res.status(401).send();
    }

    const { idArchivo, emailFirmante } = req.query;

    const { data } = await fetchApi.post(
      `/firma/manager/v2/validar-orden-firma?idArchivo=${idArchivo}&emailFirmante=${emailFirmante}`,
      undefined,
      {
        headers: {
          Authorization: `Bearer ${user.access_token}`,
          ...getDefaultHeaders(req),
        },
      },
    );

    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(validarOrdenFirmaRoute);

import React, { useState } from 'react';
import icon from '@/assets/images/Logo3.png';
import { register as registerUser } from '@/services/user';
import Container from '@/components/layout/Container';
import Grid from '@mui/material/Grid';
import { useRouter } from 'next/router';
import { useForm } from 'react-hook-form';
import Button from '@/components/atoms/Button';
import Dropdown from '@/components/molecules/Dropdown';
import Input from '@/components/atoms/Input';
import Check from '@/components/atoms/Check';
import DataPolicyModal from '@/components/molecules/DataPolicyModal';
import TermsModal from '@/components/molecules/TermsModal';
import StateHandler from '@/components/organisms/StateHandler';
import Spacer from '@/components/layout/Spacer';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';

function RegisterForm() {
  const router = useRouter();
  const {
    handleSubmit, formState: { errors }, register, reset, getValues,
  } = useForm({
    defaultValues: {
      tipoDocumento: '',
      numeroDocumento: '',
      fechaExpedicion: '',
      correoElectronico: '',
      numeroCelular: '',
      contrasena: '',
      terms: false,
      dataPrivacy: false,
    },
  });

  const [showTerms, setShowTerms] = useState(false);
  const [showDataPolicy, setShowDataPolicy] = useState(false);
  const [state, setState] = useState({
    haveError: false,
    isLoading: false,
    loadMessage: 'Te estamos registrando',
    errorMessage: {
      header: 'No se pudo completar el registro',
      content: 'este mensaje es automático',
      icon,
    },
    aceptaTyC: false,
  });

  const onSubmit = async (data) => {
    const { terms, dataPrivacy, ...formValues } = data;
    setState({
      ...state,
      isLoading: true,
    });
    const response = await registerUser(formValues);

    if (response?.status === 200 && response?.status !== undefined) {
      return router.push(`/aviso/${formValues.correoElectronico}`);
    }
    reset();
    return setState({
      ...state,
      isLoading: false,
      haveError: true,
      errorMessage: {
        ...state.errorMessage,
        content: response?.data?.mensaje || '',
      },
    });
  };

  const handleMessageClick = () => {
    setState({
      ...state,
      haveError: false,
      isLoading: false,
    });
  };

  const openModal = (modalName) => () => {
    if (modalName === 'terms') {
      return setShowTerms(true);
    }
    return setShowDataPolicy(true);
  };

  const closeModal = (modalName) => () => {
    if (modalName === 'terms') {
      return setShowTerms(false);
    }
    return setShowDataPolicy(false);
  };

  return (
    <Container>
      <StateHandler handleErrorButton={handleMessageClick} state={state}>
        {showTerms && (
          <TermsModal
            onClose={closeModal('terms')}
          />
        )}
        {showDataPolicy && (
          <DataPolicyModal
            onClose={closeModal('data-policy')}
          />
        )}
        <Grid container>
          <Grid item lg={12}>
            <Heading>Formulario de registro de usuario</Heading>
            <Paragraph size="xs">
              Asigna los datos requeridos a continuación para crear una cuenta
              en la aplicación
              {' '}
              <strong>Fírmese</strong>
            </Paragraph>
          </Grid>
        </Grid>
        <Spacer.Vertical size="sm" />
        <Grid container>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid item container xs={12} spacing={1}>
              <Grid item xs={12} md={6}>
                <Dropdown
                  id="tipoDocumento"
                  label="Tipo de documento"
                  data-testid="tipoDocumento"
                  hasError={Object.keys(errors).includes('tipoDocumento')}
                  helperText={errors.tipoDocumento && errors.tipoDocumento.message}
                  options={[
                    {
                      text: 'Selecciona tipo de documento',
                      value: '',
                    },
                    {
                      text: 'Cédula de ciudadanía',
                      value: 'CC',
                    },
                    {
                      text: 'Cédula de extranjería',
                      value: 'CE',
                    },
                  ]}
                  {
                    ...register('tipoDocumento', {
                      required: 'El Tipo de Documento es obligatorio',
                    })
                  }
                />
                <Input
                  label="Número de documento"
                  type="number"
                  {
                    ...register('numeroDocumento', {
                      required: 'El número de documento es obligatorio',
                      pattern: {
                        value: /^\d*$/,
                        message: 'El número de documento debe ser numérico',
                      },
                    })
                  }
                  hasError={Object.keys(errors).includes('numeroDocumento')}
                  helperText={errors.numeroDocumento && errors.numeroDocumento.message}
                  id="numeroDocumento"
                />

                <Input
                  type="date"
                  label="Fecha de expedición"
                  {
                    ...register('fechaExpedicion', {
                      required: 'La fecha de expedición es obligatoria',
                    })
                  }
                  hasError={Object.keys(errors).includes('fechaExpedicion')}
                  helperText={errors.fechaExpedicion && errors.fechaExpedicion.message}
                  id="fechaExpedicion"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Input
                  label="Número de celular"
                  type="number"
                  {
                    ...register('numeroCelular', {
                      required: 'El número de celular es obligatorio',
                      pattern: {
                        value: /^\d*$/,
                        message: 'El número de celular debe ser numérico',
                      },
                    })
                  }
                  id="numeroCelular"
                  hasError={Object.keys(errors).includes('numeroCelular')}
                  helperText={errors.numeroCelular && errors.numeroCelular.message}
                />
                <Input
                  label="Correo electrónico"
                  type="email"
                  {
                    ...register('correoElectronico', {
                      required: 'El correo electrónico es obligatorio',
                      pattern: {
                        value: /[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+/,
                        message: 'El correo electrónico no es válido',
                      },
                    })
                 }
                  id="correoElectronico"
                  hasError={Object.keys(errors).includes('correoElectronico')}
                  helperText={errors.correoElectronico && errors.correoElectronico.message}
                />
                <Grid item container xs={12} spacing={1}>
                  <Grid item xs={12} md={6}>
                    <Input
                      label="Contraseña"
                      type="password"
                      {
                    ...register('contrasena', {
                      required: 'La contraseña es obligatoria',
                      minLength: {
                        value: 6,
                        message: 'La contraseña debe tener entre 6-20 caracteres',
                      },
                      maxLength: {
                        value: 20,
                        message: 'La contraseña debe tener entre 6-20 caracteres',
                      },
                    })
                  }
                      id="contrasena"
                      hasError={Object.keys(errors).includes('contrasena')}
                      helperText={errors.contrasena && errors.contrasena.message}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Input
                      label="Confirmar Contraseña"
                      type="password"
                      {
                      ...register('cpasswd', {
                        required: 'La confirmación de la contraseña es obligatoria',
                        validate: (value) => {
                          const { contrasena } = getValues();
                          return contrasena === value || 'Las contraseñas deben coincidir';
                        },
                      })
                    }
                      id="cpasswd"
                      hasError={Object.keys(errors).includes('cpasswd')}
                      helperText={errors.cpasswd && errors.cpasswd.message}
                    />
                  </Grid>
                </Grid>
              </Grid>
            </Grid>
            <Grid item sm={12}>
              <Check
                size="sm"
                hasError={Object.keys(errors).includes('terms')}
                helperText={errors.terms && errors.terms.message}
                label={(
                  <>
                    Acepto los
                    {' '}
                    <a href="#" onClick={openModal('terms')}>
                      términos y condiciones
                    </a>
                  </>
                    )}
                {...register('terms', {
                  required: 'Debe aceptar los términos y condiciones',
                })}
              />
            </Grid>
            <Grid item sm={12}>
              <Check
                size="sm"
                label={(
                  <>
                    Acepto la
                    {' '}
                    <a href="#" onClick={openModal('data-privacy')}>
                      política de tratamiento de datos personales
                    </a>
                  </>
                        )}
                hasError={Object.keys(errors).includes('dataPrivacy')}
                helperText={errors.dataPrivacy && errors.dataPrivacy.message}
                {
                      ...register('dataPrivacy', {
                        required: 'Debe aceptar la política de tratamiento de datos',
                      })
                }
              />
            </Grid>
            <Spacer.Vertical size="sm" />
            <Grid item xs={12}>
              <Button
                buttonType="submit"
                type="primary"
              >
                Enviar registro
              </Button>
            </Grid>
          </form>
        </Grid>
      </StateHandler>
    </Container>
  );
}

export default RegisterForm;

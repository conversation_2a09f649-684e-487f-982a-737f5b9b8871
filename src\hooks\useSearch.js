import { useState, useEffect, useCallback } from 'react';
//
export const useSearch = (config={}) => {
  const { initialValue = '',
  debounceMs = 500,
  minLength = 0,
  onSearchChange,
  validateInput} = config;

  const [docName, setdocName] = useState(initialValue);
  const [searchName, setsearchName] = useState(initialValue);
  const [isSearching, setIsSearching] = useState(false);

  const handleSearchChange = useCallback((e) => {
    const value = e.target.value;
    setdocName(value);
    setIsSearching(true);
  }, []);

  useEffect(() => {
    const timer = setTimeout(() => {
      const searchNameTrim = docName.trim();
      // Validación personalizada si se proporciona
      if (validateInput && !validateInput(searchNameTrim)) {
        setIsSearching(false);
        return;
      }

      // Solo se actualiza si tiene la longitud mínima definida y
      if (searchNameTrim.length >= minLength && searchNameTrim !== searchName) {
        setsearchName(searchNameTrim);
        onSearchChange?.(searchNameTrim);
      }
      setIsSearching(false);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [docName, searchName, debounceMs, minLength, validateInput, onSearchChange]);

  return {
    docName,
    searchName,
    isSearching,
    handleSearchChange,
    hasActiveSearch: searchName.length > 0,
  };
};
import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

async function solicitarCambioRoute(req, res) {
  try {
    const { data } = await fetchApi.post(
      '/token/registro/solicitud-cambio',
      req.body,
      {
        headers: getDefaultHeaders(req),
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(solicitarCambioRoute);

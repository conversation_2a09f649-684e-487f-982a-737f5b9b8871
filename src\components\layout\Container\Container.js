import React from 'react';
import PropTypes from 'prop-types';
import withStyles from '@/hocs/withStyles';
import clsx from 'clsx';
import styles from './Container.module.css';

export function Container({ getStyles, children, isPlayground }) {
  return (
    <div
      className={clsx(getStyles({
        'is-playground': isPlayground,
      }), 'container')}
    >
      <div className={getStyles('content')}>
        {children}
      </div>
    </div>
  );
}

Container.propTypes = {
  children: PropTypes.node.isRequired,
  getStyles: PropTypes.func.isRequired,
  isPlayground: PropTypes.bool,
};

Container.defaultProps = {
  isPlayground: false,
  getStyles: () => ({}),
};

export default withStyles(styles)(Container);

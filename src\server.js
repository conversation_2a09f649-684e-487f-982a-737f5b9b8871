const { createServer } = require('https');
const { parse } = require('url');
const next = require('next');
const fs = require('fs');
const path = require('path');

const port = parseInt(process.env.PORT || '3000', 10);
const dev = process.env.NODE_ENV !== 'production';
const app = next({ dev });
const handle = app.getRequestHandler();

app.prepare()
  .then(() => {
    if (!process.env.CERT_NAME || !process.env.CERT_PASS) {
      console.error('[Fatal Error]: env variables CERT_NAME and CERT_PASS are required');
      process.exit(1);
    }
    const options = {
      pfx: fs.readFileSync(path.join(__dirname, '..', 'certs', process.env.CERT_NAME)),
      passphrase: process.env.CERT_PASS,
    };

    createServer(options, (req, res) => {
      const parsedUrl = parse(req.url, true);
      handle(req, res, parsedUrl);
    }).listen(port);

    console.log(
      `> Server listening at https://0.0.0.0:${port} as ${
        dev ? 'development' : process.env.NODE_ENV
      }`,
    );
  });

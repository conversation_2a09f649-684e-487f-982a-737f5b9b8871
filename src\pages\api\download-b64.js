import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '20mb',
    },
  },
};

async function downloadB64Route(req, res) {
  try {
    const { fileID } = req.query;
    const { data } = await fetchApi.post(
      `/validacion/registro/downloadB64/${fileID}`,
      {
        headers: getDefaultHeaders(req),
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(downloadB64Route);

import React from 'react';
import Image from 'next/image';
import Container from '@/components/layout/Container';
import Grid from '@mui/material/Grid';
import logo1 from '@/assets/images/ICONOS-LANDING-PAGE-03.png';
import logo2 from '@/assets/images/ICONOS-LANDING-PAGE-04.png';
import logo3 from '@/assets/images/ICONO-ESCANER.png';
import PropTypes from 'prop-types';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import Spacer from '@/components/layout/Spacer';

function MensajeAvisoCorreo({ mail }) {
  return (
    <div>
      <Spacer.Vertical size="lg" />
      <Container>
        <Spacer.Vertical size="sm" />
        <Heading isCentered weight="bold">
          Te hemos enviado un correo electrónico a la cuenta
          {' '}
          {mail}
          {' '}
          por favor revisa tu bandeja de entrada y confirma el registro
        </Heading>
        <Spacer.Vertical size="sm" />
        <Paragraph size="sm" isCentered>
          Ahora debes completar los siguientes pasos para activar tu cuenta
          en
          {' '}
          <strong>Fírmese</strong>
          , para esto, debes tener instalada la aplicación móvil
          {' '}
          <strong>Fírmese</strong>
          {' '}
          para completar el registro.
        </Paragraph>
        <Spacer.Vertical size="sm" />
        <Spacer.Vertical size="sm" />
        <Grid container spacing={2}>
          <Grid item lg={4}>
            <Image className="img-fluid" src={logo1} alt="Banner" />
          </Grid>
          <Grid item lg={8}>
            <Heading color="primary">
              Paso 1. Verificación correo electrónico
            </Heading>
            <Paragraph size="xs" isJustify>
              A tu correo llegará un enlace al que debes acceder para hacer la
              confirmación de correo electrónico, esta misma página te asignará un código de
              transacción para que puedas continuar desde la aplicación móvil
              {' '}
              <strong>Fírmese</strong>
              .  Una vez hayas abierto la aplicación, debes ir a la opción
              {' '}
              <strong><em>Completar registro</em></strong>
              {' '}
              donde debes digitar el código asignado.

            </Paragraph>

          </Grid>
        </Grid>
        <Spacer.Vertical size="sm" />
        <Spacer.Vertical size="sm" />
        <Grid container spacing={2}>
          <Grid item lg={4}>
            <Image className="img-fluid" src={logo2} alt="Banner" />
          </Grid>
          <Grid item lg={8}>
            <Heading color="primary">
              Paso 2. Verificación número de celular
            </Heading>
            <Paragraph isJustify size="xs">
              Una vez hayas completado el paso 1, la aplicación te pedirá 2 dígitos
              del número de celular asignado en el registro, para que mediante un mensaje
              de texto puedas confirmar la validez de este número.
            </Paragraph>
          </Grid>
        </Grid>
        <Spacer.Vertical size="sm" />
        <Spacer.Vertical size="sm" />
        <Grid container spacing={2}>
          <Grid item lg={4}>
            <Image className="img-fluid" src={logo3} alt="Banner" />
          </Grid>
          <Grid item lg={8}>
            <Heading color="primary">
              Paso 3. Verificación documento de identidad
            </Heading>
            <Paragraph isJustify size="xs">
              En este paso se abrirá el escáner de la aplicación
              {' '}
              <strong>Fírmese</strong>
              , aquí tu debes dirigirlo a la parte posterior de tu documento de
              identidad para que puedas escanear la información del documento y
              dar de alta el registro en la aplicación
              {' '}
              {' '}
              <strong>Fírme.se</strong>
            </Paragraph>
          </Grid>
        </Grid>
        <Spacer.Vertical size="sm" />
      </Container>
    </div>
  );
}

MensajeAvisoCorreo.defaultProps = {
  mail: '',
};

MensajeAvisoCorreo.propTypes = {
  mail: PropTypes.string,
};

export default MensajeAvisoCorreo;

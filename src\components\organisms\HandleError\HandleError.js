import React, { Component } from 'react';
import Link from 'next/link';
import PropTypes from 'prop-types';
import styles from './HandleError.module.css';

class HandleError extends Component {
  constructor(props) {
    super(props);
    this.state = {
      handleError: false,
    };
  }

  // eslint-disable-next-line no-unused-vars
  componentDidCatch(_error, _info) {
    this.setState({
      handleError: true,
    });
  }

  render() {
    // eslint-disable-next-line react/destructuring-assignment
    if (this.state.handleError) {
      return (
        <div className={styles['error-container']}>
          <div className={styles.error}>
            <div className={styles['error-description']}>
              <h1>Oops!</h1>
              <h2>Algo ha salido mal</h2>
            </div>
            <Link href="/">Volver al inicio</Link>
          </div>
        </div>
      );
    }
    // eslint-disable-next-line react/destructuring-assignment
    return this.props.children;
  }
}

HandleError.propTypes = {
  children: PropTypes.node.isRequired,
};

export default HandleError;

import { stringify } from 'query-string';
import { withSessionRoute } from '@/lib/withSession';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import catchApiError from '@/utils/catchApiError';
import path from 'path';
import fs from 'fs';
import pdfPageCounter from 'pdf-page-counter';
import getPDFPreview from '@/utils/getPDFPreview';

export const config = {
  api: {
    bodyParser: {
      sizeLimit: '50mb',
    },
  },
};

async function consultarByHashRoute(req, res) {
  try {
    const { data } = await fetchApi.post(
      '/firma/manager/consutlar-by-hash',
      stringify(req.body),
      {
        headers: getDefaultHeaders(req),
      },
    );

    const docs = Promise.all(data?.data?.map(async (doc) => {
      const pdfDir = path.join(
        __dirname,
        '../../../../tmp/',
      );

      if (!fs.existsSync(pdfDir)) {
        fs.mkdirSync(pdfDir);
      }

      const pdfPath = path.join(
        pdfDir,
        `${Math.floor(new Date().getTime() * Math.random())}.pdf`,
      );
      try {
        await fs.writeFileSync(pdfPath, doc.b64, { encoding: 'base64' });
        const pdf = await fs.readFileSync(pdfPath);
        const { size } = fs.statSync(pdfPath);
        const previewB64 = await getPDFPreview(pdfPath);

        await fs.unlinkSync(pdfPath);

        const pdfPages = await pdfPageCounter(pdf).then((meta) => meta.numpages);
        return {
          ...doc,
          previewB64,
          size,
          pdfPages,
        };
      } catch (e) {
        console.error(e);

        if (fs.existsSync(pdfPath)) {
          fs.unlinkSync(pdfPath);
        }

        return {
          ...doc,
          previewB64: null,
        };
      }
    }));

    return res.send({
      ...data,
      data: await docs,
    });
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(consultarByHashRoute);

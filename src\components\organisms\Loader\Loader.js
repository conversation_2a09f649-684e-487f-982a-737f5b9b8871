import React from 'react';
import Grid from '@mui/material/Grid';
import PropTypes from 'prop-types';
import Heading from '@/components/atoms/Heading';

import styles from './Loader.module.css';

function Loader({ message }) {
  return (
    <div>
      <Grid container justifyContent="center" style={{ textAlign: 'center' }}>
        <Grid item lg={12}>
          <div className={styles['sk-cube-grid']}>
            <div className={styles['sk-cube1']} />
            <div className={styles['sk-cube2']} />
            <div className={styles['sk-cube3']} />
            <div className={styles['sk-cube4']} />
            <div className={styles['sk-cube5']} />
            <div className={styles['sk-cube7']} />
            <div className={styles['sk-cube8']} />
            <div className={styles['sk-cube8']} />
            <div className={styles['sk-cube9']} />
          </div>
          <Heading size="sm">
            {message}
          </Heading>
        </Grid>
      </Grid>
    </div>
  );
}

Loader.defaultProps = {
  message: '',
};

Loader.propTypes = {
  message: PropTypes.string,
};

export default Loader;

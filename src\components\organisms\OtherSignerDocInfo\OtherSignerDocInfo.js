import React from 'react';
import toCapitalize from '@/utils/toCapitalize';
import { choices } from '@/tokens/index';
import filterIp from '@/utils/filterIp';
import dayjs from 'dayjs';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import Spacer from '@/components/layout/Spacer';
import downloadPDF from '@/utils/downloadPDF';
import { downloadFileB64 } from '@/services/user';
import PropTypes from 'prop-types';
import Button from '@/components/atoms/Button';
import Card from '@/components/atoms/Card';
import Icon from '@/components/atoms/Icon';
import DocPreview from '@/components/organisms/DocPreview';
import styles from './OtherSignerDocInfo.module.css';
import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
function OtherSignerDocInfo({ docs, onAccept }) {
  const download = ({ idArchivo, b64, nombreArchivo }) => () => {
    if(b64===null){
      downloadFileB64(idArchivo)
      .then((response) => {
        if (response.status === 200) {
          downloadPDF(response.data.data, nombreArchivo);
        } else {
          enqueueSnackbar(response.data.mensaje, { variant: 'error' });
        }
      })
      .catch((err) => console.error(err));
    }else{
    downloadPDF(b64, nombreArchivo);
  }
  };
  return (
    <>
      <Heading>
        Solicitud de firma de documentos
      </Heading>
      <Paragraph size="sm">
        El solicitante definido a continuación requiere de tu firma
        electrónica
      </Paragraph>
      <Spacer.Vertical size="sm" />
      <table className={styles['user-info']}>
        <tbody>
          <tr>
            <th>Nombre</th>
            <td>
              {toCapitalize(docs?.solicitante)}
            </td>
          </tr>
          <tr>
            <th>Correo electrónico</th>
            <td>
              {docs?.email}
            </td>
          </tr>
        </tbody>
      </table>
      <Spacer.Vertical size="lg" />
      <Heading size="sm">Documentos pendientes de firma</Heading>
      <Spacer.Vertical size="sm" />
      <div className={styles['grid-card']}>
        {Object.keys(docs?.archivos || {}).map((datos) => (
          <Card
            border="secondary"
            color="base"
            key={`${docs?.archivos[datos]?.ip}-${docs?.archivos[datos]?.nombreArchivo}`}
            style={{ padding: 0, background: choices.color.gray[100] }}
          >
            <div className={styles['card-header']}>
              <Heading size="xs" color="inverted">
                {toCapitalize(docs?.archivos[datos]?.nombreArchivo)}
              </Heading>
            </div>
            <DocPreview
              docId={docs?.archivos[datos]?.idArchivo}
              className={styles.preview}
              isFlat
            />
            {/* {docs?.archivos[datos]?.previewB64 ? ( */}
            {/*  <img */}
            {/*    alt="preview" */}
            {/*    src={`data:image/jpg;base64,${docs?.archivos[datos]?.previewB64}`} */}
            {/*    className={styles.preview} */}
            {/*  /> */}
            {/* ) : ( */}
            {/*  <div style={{ */}
            {/*    display: 'flex', */}
            {/*    alignItems: 'center', */}
            {/*    justifyContent: 'center', */}
            {/*    flexDirection: 'column', */}
            {/*    background: '#fff', */}
            {/*    padding: '10px 0', */}
            {/*  }} */}
            {/*  > */}
            {/*    <Icon name="verifyDocument" color="primary" size="2xl" /> */}
            {/*    <Spacer size="sm" /> */}
            {/*    <Paragraph size="sm" color="muted" isCentered> */}
            {/*      No se pudo obtener la vista previa del documento. */}
            {/*    </Paragraph> */}
            {/*  </div> */}
            {/* )} */}
            {/* <Icon name="verifyDocument" color="primary" size="2xl" /> */}
            <div style={{
              padding: '12px 10px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'end',
              flexWrap: 'wrap',
            }}
            >
              <div>
                <div style={{ display: 'flex' }}>
                  <Paragraph size="xs">
                    <strong>IP del solicitante:</strong>
                    {' '}
                    {filterIp(docs?.archivos[datos]?.ip)}
                  </Paragraph>
                </div>
                <div style={{ display: 'flex' }}>
                  <Paragraph size="xs">
                    <strong>Firmas requeridas:</strong>
                    {' '}
                    {docs?.archivos[datos]?.cantidadFirmas}
                  </Paragraph>
                </div>
                <div style={{ display: 'flex' }}>
                  <Paragraph size="xs">
                    <strong>Fecha de carga:</strong>
                    {' '}
                    {dayjs(docs?.archivos[datos]?.fechaRegistroStr).format('DD/MM/YYYY hh:mm a')}
                  </Paragraph>
                </div>
                {!!docs?.archivos[datos]?.descripcion && (
                  <div style={{ display: 'flex' }}>
                    <Paragraph size="xs">
                      <strong>Descripción:</strong>
                      {' '}
                      {docs?.archivos[datos]?.descripcion}
                    </Paragraph>
                  </div>
                )}
              </div>
              <Icon
                name="documentDownload"
                size="md"
                background="muted"
                color="inverted"
                isClickable
                onClick={download(docs?.archivos[datos])}
              />
            </div>
          </Card>
        ))}
      </div>
      <Spacer.Vertical size="sm" />
      <Button
        onClick={onAccept}
        isInline
      >
        Aceptar firma de documento
      </Button>
    </>
  );
}

OtherSignerDocInfo.propTypes = {
  docs: PropTypes.shape({
    solicitante: PropTypes.string,
    email: PropTypes.string,
    archivos: PropTypes.shape({
      ip: PropTypes.string,
      nombreArchivo: PropTypes.string,
      previewB64: PropTypes.string,
      cantidadFirmas: PropTypes.number,
      fechaRegistroStr: PropTypes.string,
    }),
  }).isRequired,
  onAccept: PropTypes.func.isRequired,
};

export default OtherSignerDocInfo;

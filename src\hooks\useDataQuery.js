import { useCallback, useMemo } from 'react';
import { useRecoilValue } from 'recoil';
import { useInfiniteQuery } from 'react-query';
import { userState } from '@/recoil/atoms';
import { docsFactory } from '@/lib/factories/docsFactory';
import { docRepository } from '@/lib/repositories/docRepository';
import { useSearch } from './useSearch';

export const useDataQuery = ({ dataType, searchConfig = {} }) => {
  const user = useRecoilValue(userState);
  const search = useSearch(searchConfig);
  const repository = useMemo(() => {
    console.log(`Creando repositorio para estrategia: ${dataType}`);
    try {
      const strategy = docsFactory.getStrategy(dataType);
      const repo = docRepository(strategy);
      console.log(`Repositorio creado exitosamente`);
      return repo;
    } catch (error) {
      console.error(`<PERSON><PERSON><PERSON> al crear el repositorio: `, error);
      throw error;
    }
  }, [dataType]);

  const queryFn = useCallback(async ({ pageParam = 0 }) => {
    console.log(`[useDataQuery] Ejecutando query - Página: ${pageParam}, Búsqueda activa: ${search.hasActiveSearch}`);
    const params = { page: pageParam, pageSize: 12, user, searchName: search.searchName };
    try {
      console.log(`Obteniendo datos paginados`);
      return await repository.fetchData(params);
    } catch (error) {
      console.error(`Error en queryFn:`, error);
      throw error;
    }
  }, [repository, search.searchName, user]);

  const queryKey = useMemo(() => {
    const key = repository.getQueryKey(search.searchName, user?.idUsuarioC);
    return key;
  }, [repository, search.searchName, user?.idUsuarioC]);

  const query = useInfiniteQuery({
    queryKey,
    queryFn,
    enabled: !!user?.idUsuarioC && !!user?.access_token && !!user?.email,
    getNextPageParam: (lastPage) => {
      return lastPage.nextPage;
    },
    retry: (failureCount, error) => {
      console.log(`[useDataQuery] Reintento ${failureCount} - Error:`, error?.response?.status);
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        console.warn(`[useDataQuery] No reintentando - Error de autenticación`);
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: 1000,
  });

  return {
    ...query,
    search,
    isSearchActive: search.hasActiveSearch,
  };
};

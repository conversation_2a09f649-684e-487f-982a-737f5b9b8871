import { act, fireEvent, render } from '@testing-library/react';
import OrisSignersTable from '@/components/organisms/OrisSignersTable/OrisSignersTable';
import { checkAllValuesInDom } from '@/utils/forTesting';

const signersMock = [
  {
    numeroDocumento: 'numeroDocumento1',
    tipoDocumento: 'tipoDocumento1',
    nombreCompleto: 'nombreCompleto1',
    email: 'email1',
  },
  {
    numeroDocumento: 'numeroDocumento2',
    tipoDocumento: 'tipoDocumento2',
    nombreCompleto: 'nombreCompleto2',
    email: 'email2',
  },
];

describe('Tests OrisSignersTable', () => {
  it('should show signer info', () => {
    render(<OrisSignersTable signers={signersMock} handleDelete={jest.fn()} />);
    signersMock.forEach((signer) => {
      checkAllValuesInDom(signer);
    });
  });

  it('should show default text when there are no signers', () => {
    const { getByText } = render(<OrisSignersTable signers={[]} handleDelete={jest.fn()} />);

    expect(getByText('No se han agregado firmantes')).toBeInTheDocument();
  });

  it('should show delete modal when click on delete icon', async () => {
    const component = <OrisSignersTable signers={[signersMock[0]]} handleDelete={jest.fn()} />;
    const { getByTestId, getByText } = render(component);

    const deleteIcon = getByTestId('delete');

    await act(() => {
      fireEvent.click(deleteIcon);
    });

    expect(getByText(`¿Está seguro que desea eliminar el firmante ${signersMock[0].nombreCompleto}?`)).toBeInTheDocument();
  });

  it('should call handleDelete when click on confirm delete button', async () => {
    const handleDelete = jest.fn();
    const component = <OrisSignersTable signers={[signersMock[0]]} handleDelete={handleDelete} />;
    const { getByTestId, getByText } = render(component);

    const deleteIcon = getByTestId('delete');

    await act(() => {
      fireEvent.click(deleteIcon);
    });

    const confirmDeleteButton = getByText('Eliminar');

    await act(() => {
      fireEvent.click(confirmDeleteButton);
    });

    expect(handleDelete).toHaveBeenCalled();
    expect(handleDelete).toHaveBeenCalledWith(signersMock[0].numeroDocumento);
  });
});

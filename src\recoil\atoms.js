import { atom } from 'recoil';
import { setTokenInfo, clearTokenInfo } from '@/utils/tokenManager';

export const userState = atom({
  key: 'userState_unique_2025',
  default: {
    email: null,
    expires_in: null,
    idUsuarioC: null,
    idUsuarioA: null,
    access_token: null,
    refresh_token: null,
    enableOris: false,
  },
  effects: [
    ({ setSelf, onSet }) => {
      if (typeof window !== 'undefined') {
        try {
          const savedState = localStorage.getItem('userState');
          if (savedState) {
            const parsedState = JSON.parse(savedState);
            if (parsedState.email && parsedState.access_token) {
              setSelf(parsedState);
              // Restaurar token info si existe expires_in
              if (parsedState.expires_in) {
                setTokenInfo(parsedState.expires_in);
              }
            }
          }
        } catch (error) {
          console.error('Error loading userState:', error);
        }

        onSet((newValue) => {
          try {
            localStorage.setItem('userState', JSON.stringify(newValue));
            // Manejar información del token
            if (newValue.email && newValue.access_token && newValue.expires_in) {
              console.log('Guardando token info desde userState:', newValue.expires_in);
              setTokenInfo(newValue.expires_in);
            } else if (!newValue.email || !newValue.access_token) {
              // Si se limpia el usuario, limpiar también la info del token
              clearTokenInfo();
            }
          } catch (error) {
            console.error('Error saving userState:', error);
          }
        });
      }
    },
  ],
});

export const userLocationState = atom({
  key: 'userLocationState_unique_2025',
  default: {},
});

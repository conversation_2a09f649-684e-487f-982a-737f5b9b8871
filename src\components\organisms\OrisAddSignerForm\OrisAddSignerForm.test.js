import {
  render, fireEvent, act,
} from '@testing-library/react';
import * as userService from '@/services/user';
import OrisAddSignerForm from '@/components/organisms/OrisAddSignerForm/OrisAddSignerForm';

const userMock = {
  tipoDocumento: 'CC',
  numeroDocumento: '1234567890',
  fechaExpedicionDocumento: '2021-01-01',
  correoElectronico: '<EMAIL>',
  numeroCelular: '1234567890',
  nombreCompleto: 'John Do<PERSON>',
  firmas: 1,
};
describe('Tests OrisAddSignerForm', () => {
  describe('search input is blured', () => {
    it('shouldn\'t call get user when search input is empty', async () => {
      const responseMock = {
        status: 200,
        data: {
          data: userMock,
        },
      };
      const getUserMock = jest.spyOn(userService, 'getUserByDocumentNumberOrEmail').mockResolvedValue(responseMock);

      const { getByLabelText } = render(<OrisAddSignerForm handleCreate={jest.fn()} />);

      const searchInput = getByLabelText(/Buscar por número de documento o Email/i);

      await act(() => {
        fireEvent.blur(searchInput);
      });

      expect(getUserMock).not.toHaveBeenCalled();
    });

    it('should call get user when search input is not empty', async () => {
      const responseMock = {
        status: 200,
        data: {
          data: userMock,
        },
      };
      const getUserMock = jest.spyOn(userService, 'getUserByDocumentNumberOrEmail').mockResolvedValue(responseMock);

      const { getByLabelText } = render(<OrisAddSignerForm handleCreate={jest.fn()} />);

      const searchInput = getByLabelText(/Buscar por número de documento o Email/i);

      await act(() => {
        fireEvent.change(searchInput, { target: { value: userMock.numeroDocumento } });
        fireEvent.blur(searchInput);
      });

      expect(getUserMock).toHaveBeenCalled();
      expect(getUserMock).toHaveBeenCalledWith(userMock.numeroDocumento);
    });

    it('should set all values form when user exists', async () => {
      const responseMock = {
        status: 200,
        data: {
          data: userMock,
        },
      };
      const getUserMock = jest.spyOn(userService, 'getUserByDocumentNumberOrEmail').mockResolvedValue(responseMock);

      const component = <OrisAddSignerForm handleCreate={jest.fn()} />;
      const { getByLabelText, getByRole, getByTestId } = render(component);

      const searchInput = getByLabelText(/Buscar por número de documento o Email/i);

      await act(() => {
        fireEvent.change(searchInput, { target: { value: userMock.numeroDocumento } });
        fireEvent.blur(searchInput);
      });

      expect(getUserMock).toHaveBeenCalled();
      expect(getUserMock).toHaveBeenCalledWith(userMock.numeroDocumento);

      const numeroDocumento = getByRole('textbox', { name: /Número de documento/ });
      expect(numeroDocumento.value).toEqual(userMock.numeroDocumento);
      expect(numeroDocumento).toBeDisabled();

      const tipoDocumento = getByTestId('tipoDocumento');
      expect(tipoDocumento.value).toEqual(userMock.tipoDocumento);
      expect(tipoDocumento).toBeDisabled();

      const numeroCelular = getByRole('textbox', { name: /Número de celular/ });
      expect(numeroCelular.value).toEqual(userMock.numeroCelular);
      expect(numeroCelular).toBeDisabled();

      const nombreCompleto = getByRole('textbox', { name: /Nombre completo/ });
      expect(nombreCompleto.value).toEqual(userMock.nombreCompleto);
      expect(nombreCompleto).toBeDisabled();

      const correoElectronico = getByRole('textbox', { name: /Correo electrónico/ });
      expect(correoElectronico.value).toEqual(userMock.correoElectronico);
      expect(correoElectronico).toBeDisabled();
    });

    it('should disabled all fields except email when user exists and not has signs', async () => {
      const userMockWithoutSigns = {
        ...userMock,
        firmas: 0,
      };
      const responseMock = {
        status: 200,
        data: {
          data: userMockWithoutSigns,
        },
      };
      const getUserMock = jest.spyOn(userService, 'getUserByDocumentNumberOrEmail').mockResolvedValue(responseMock);

      const component = <OrisAddSignerForm handleCreate={jest.fn()} />;
      const { getByLabelText, getByRole, getByTestId } = render(component);

      const searchInput = getByLabelText(/Buscar por número de documento o Email/i);

      await act(() => {
        fireEvent.change(searchInput, { target: { value: userMock.numeroDocumento } });
        fireEvent.blur(searchInput);
      });

      expect(getUserMock).toHaveBeenCalled();
      expect(getUserMock).toHaveBeenCalledWith(userMock.numeroDocumento);

      const numeroDocumento = getByRole('textbox', { name: /Número de documento/ });
      expect(numeroDocumento.value).toEqual(userMock.numeroDocumento);
      expect(numeroDocumento).toBeDisabled();

      const tipoDocumento = getByTestId('tipoDocumento');
      expect(tipoDocumento.value).toEqual(userMock.tipoDocumento);
      expect(tipoDocumento).toBeDisabled();

      const numeroCelular = getByRole('textbox', { name: /Número de celular/ });
      expect(numeroCelular.value).toEqual(userMock.numeroCelular);
      expect(numeroCelular).toBeDisabled();

      const nombreCompleto = getByRole('textbox', { name: /Nombre completo/ });
      expect(nombreCompleto.value).toEqual(userMock.nombreCompleto);
      expect(nombreCompleto).toBeDisabled();

      const correoElectronico = getByRole('textbox', { name: /Correo electrónico/ });
      expect(correoElectronico.value).toEqual(userMock.correoElectronico);
      expect(correoElectronico).toBeEnabled();
    });

    it('should only set email when user not exists and search input is email', async () => {
      const responseMock = {
        status: 404,
        data: {
          data: null,
        },
      };
      const getUserMock = jest.spyOn(userService, 'getUserByDocumentNumberOrEmail').mockResolvedValue(responseMock);

      const component = <OrisAddSignerForm handleCreate={jest.fn()} />;
      const { getByLabelText, getByRole, getByTestId } = render(component);

      const searchInput = getByLabelText(/Buscar por número de documento o Email/i);

      await act(() => {
        fireEvent.change(searchInput, { target: { value: userMock.correoElectronico } });
        fireEvent.blur(searchInput);
      });

      expect(getUserMock).toHaveBeenCalled();
      expect(getUserMock).toHaveBeenCalledWith(userMock.correoElectronico);

      const numeroDocumento = getByRole('textbox', { name: /Número de documento/ });
      expect(numeroDocumento.value).toEqual('');
      expect(numeroDocumento).toBeEnabled();

      const tipoDocumento = getByTestId('tipoDocumento');
      expect(tipoDocumento.value).toEqual('');
      expect(tipoDocumento).toBeEnabled();

      const numeroCelular = getByRole('textbox', { name: /Número de celular/ });
      expect(numeroCelular.value).toEqual('');
      expect(numeroCelular).toBeEnabled();

      const nombreCompleto = getByRole('textbox', { name: /Nombre completo/ });
      expect(nombreCompleto.value).toEqual('');
      expect(nombreCompleto).toBeEnabled();

      const correoElectronico = getByRole('textbox', { name: /Correo electrónico/ });
      expect(correoElectronico.value).toEqual(userMock.correoElectronico);
      expect(correoElectronico).toBeEnabled();
    });

    it('should only set numeroDocumento when user not exists and search input is numeroDocumento', async () => {
      const responseMock = {
        status: 404,
        data: {
          data: null,
        },
      };
      const getUserMock = jest.spyOn(userService, 'getUserByDocumentNumberOrEmail').mockResolvedValue(responseMock);

      const component = <OrisAddSignerForm handleCreate={jest.fn()} />;
      const { getByLabelText, getByRole, getByTestId } = render(component);

      const searchInput = getByLabelText(/Buscar por número de documento o Email/i);

      await act(() => {
        fireEvent.change(searchInput, { target: { value: userMock.numeroDocumento } });
        fireEvent.blur(searchInput);
      });

      expect(getUserMock).toHaveBeenCalled();
      expect(getUserMock).toHaveBeenCalledWith(userMock.numeroDocumento);

      const numeroDocumento = getByRole('textbox', { name: /Número de documento/ });
      expect(numeroDocumento.value).toEqual(userMock.numeroDocumento);
      expect(numeroDocumento).toBeEnabled();

      const tipoDocumento = getByTestId('tipoDocumento');
      expect(tipoDocumento.value).toEqual('');
      expect(tipoDocumento).toBeEnabled();

      const numeroCelular = getByRole('textbox', { name: /Número de celular/ });
      expect(numeroCelular.value).toEqual('');
      expect(numeroCelular).toBeEnabled();

      const nombreCompleto = getByRole('textbox', { name: /Nombre completo/ });
      expect(nombreCompleto.value).toEqual('');
      expect(nombreCompleto).toBeEnabled();

      const correoElectronico = getByRole('textbox', { name: /Correo electrónico/ });
      expect(correoElectronico.value).toEqual('');
      expect(correoElectronico).toBeEnabled();
    });
  });

  describe('handleCreate', () => {
    it('should call handleCreate when form submit', async () => {
      const responseMock = {
        status: 200,
        data: {
          data: userMock,
        },
      };

      jest.spyOn(userService, 'getUserByDocumentNumberOrEmail').mockResolvedValue(responseMock);

      const handleCreateMock = jest.fn();
      const component = <OrisAddSignerForm handleCreate={handleCreateMock} />;
      const { getByLabelText, getByRole } = render(component);

      const searchInput = getByLabelText(/Buscar por número de documento o Email/i);

      await act(() => {
        fireEvent.change(searchInput, { target: { value: userMock.numeroDocumento } });
        fireEvent.blur(searchInput);
      });

      const submitButton = getByRole('button', { name: /Agregar firmante a la tabla/i });

      await act(() => {
        fireEvent.click(submitButton);
      });

      expect(handleCreateMock).toHaveBeenCalled();
      expect(handleCreateMock).toHaveBeenCalledWith({
        email: userMock.correoElectronico,
        nombreCompleto: userMock.nombreCompleto,
        numeroCelular: userMock.numeroCelular,
        numeroDocumento: userMock.numeroDocumento,
        tipoDocumento: userMock.tipoDocumento,
        fechaExpedicion: userMock.fechaExpedicionDocumento,
      });
    });

    it('should show errors when form submit and has errors', async () => {
      const responseMock = {
        status: 200,
        data: {
          data: userMock,
        },
      };

      jest.spyOn(userService, 'getUserByDocumentNumberOrEmail').mockResolvedValue(responseMock);

      const errorMock = new Error('Firmante ya existe');
      const handleCreateMock = jest.fn().mockImplementation(() => {
        throw errorMock;
      });
      const component = <OrisAddSignerForm handleCreate={handleCreateMock} />;
      const { getByLabelText, getByRole, getByText } = render(component);

      const searchInput = getByLabelText(/Buscar por número de documento o Email/i);

      await act(() => {
        fireEvent.change(searchInput, { target: { value: userMock.numeroDocumento } });
        fireEvent.blur(searchInput);
      });

      const submitButton = getByRole('button', { name: /Agregar firmante a la tabla/i });

      await act(() => {
        fireEvent.click(submitButton);
      });

      const error = getByText(errorMock.message);
      expect(error).toBeInTheDocument();
    });
  });
});

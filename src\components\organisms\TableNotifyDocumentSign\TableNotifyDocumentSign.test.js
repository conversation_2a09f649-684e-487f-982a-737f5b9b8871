import React from 'react';
import {
  render,
} from '@testing-library/react';
import * as Notistack from 'notistack';
import * as utils from '@/utils/utils';
import TableNotifyDocumentSign from './index';

const docInfo = {
  idArchivo: 303,
  nombreArchivo: 'Firmese.pdf',
  hashArchivo: 'd8031178d89264a25c3bef41a59b2dd41da9191e623b254d6f837f3144c13c574455b7364eb0b7b77cfa86d5aeee3faa7a47f0656b9ce18d99ad77326f1c3ad0',
  ipOrigen: null,
  cantidadFirmas: 1,
  cantidadFirmado: 0,
  fechaRegistro: '2022-04-11T14:14:59.000+0000',
  fechaRegistroStr: '2022-04-11 09:14',
  cantidadConsultas: 0,
  ip: '***********',
  observacion: null,
  resultadoFirma: 'OK',
  estado: 'Firmado',
  firmas: [],
  b64: null,
  emailFirmantes: null,
  tipoFirma: 'SINGLE',
  propietario: null,
};

describe('Tests TableNotifyDocumentSign', () => {
  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should show error message if x-dta is undefined', async () => {
    const loadItemStorageMock = jest.fn().mockImplementation(() => undefined);
    jest.spyOn(utils, 'loadItemStorage').mockImplementation(loadItemStorageMock);

    const { findByText } = render(<TableNotifyDocumentSign />);

    expect(await findByText(/Error/i)).toBeInTheDocument();
  });

  it('should show x-dta data', async () => {
    const loadItemStorageMock = jest.fn().mockImplementation(() => ({
      codigo: '000',
      mensaje: 'OK',
      data: [docInfo],
    }));
    jest.spyOn(utils, 'loadItemStorage').mockImplementation(loadItemStorageMock);

    const { findByText } = render(<TableNotifyDocumentSign />);

    expect(await findByText(docInfo.nombreArchivo)).toBeInTheDocument();
  });
});

// eslint-disable-next-line import/no-extraneous-dependencies
import { screen } from '@testing-library/react';

export const checkAllValuesInDom = (
  obj,
  excludedKeys = [],
  { exact = true, insensitive = false } = {},
) => {
  Object.keys(obj).forEach((key) => {
    if (!excludedKeys.includes(key)) {
      if (typeof obj[key] === 'object') {
        checkAllValuesInDom(obj[key], excludedKeys, { exact, insensitive });
      } else {
        expect(screen.getByText(new RegExp(obj[key], `${insensitive ? 'i' : ''}`), { exact })).toBeInTheDocument();
      }
    }
  });
};

export const documentInfoMock = {
  showInfo: true,
  propietario: {
    nombreCompleto: '<PERSON>',
    tipoDocumento: 'CC',
    numeroDocumento: '123456789',
  },
  ip: '***********',
  fechaRegistro: '2020-01-01',
  cantidadConsultas: '999',
  hashArchivo: 'ASASKLKA8766',
  idArchivo: '13579',
  nombreArchivo: 'archivo.pdf',
  firmas: [
    {
      firma: {
        nombreCompleto: 'John Doe',
        nombreTipoDocumento: 'CE',
        numeroDocumento: '987654321',
        correoElectronico: '<EMAIL>',
      },
      fechaFirmaStr: '2021-01-01',
      ipFirma: '***********',
      agenteNavegador: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36',
    },
  ],
};

import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import Paragraph from '@/components/atoms/Paragraph';
import styles from './Link.module.css';
import { options } from './constants';

export const Link = React.forwardRef(({
  children, size, color, getStyles, href, rel, target,
}, ref) => (
  <a ref={ref} href={href} className={getStyles('link', ['color'])} rel={rel} target={target}>
    <Paragraph size={size} color={color} weight="semibold" isInline>
      {children}
    </Paragraph>
  </a>
));

Link.propTypes = {
  children: PropTypes.node.isRequired,
  getStyles: PropTypes.func.isRequired,
  color: PropTypes.oneOf(options.colors),
  size: PropTypes.oneOf(options.sizes),
  href: PropTypes.string.isRequired,
  rel: PropTypes.string,
  target: PropTypes.string,
};

Link.defaultProps = {
  getStyles: () => ({}),
  color: 'primary',
  size: 'md',
};

export default withStyles(styles)(Link);

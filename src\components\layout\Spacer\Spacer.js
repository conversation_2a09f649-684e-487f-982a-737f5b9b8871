import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import { Vertical, Horizontal } from './components';

import styles from './Spacer.module.css';
import { mapSize } from './helpers';

export function Spacer({ getStyles, size, isPlayground }) {
  return (
    <div
      className={getStyles('spacer', {
        'is-playground': isPlayground,
      })}
      style={{
        display: 'inline-block',
        width: mapSize(size),
        height: mapSize(size),
      }}
    />
  );
}

Spacer.propTypes = {
  size: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  getStyles: PropTypes.func.isRequired,
  isPlayground: PropTypes.bool,
};

Spacer.defaultProps = {
  size: 'none',
  isPlayground: false,
  getStyles: () => ({}),
};

const SpacerWithStyles = withStyles(styles)(Spacer);

SpacerWithStyles.Vertical = Vertical;
SpacerWithStyles.Horizontal = Horizontal;

export default SpacerWithStyles;

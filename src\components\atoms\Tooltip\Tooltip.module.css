.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip-title {
  visibility: hidden;
  background: #555;
  color: var(--color-base-white);
  padding: 2px 5px;
  border-radius: var(--border-radius-xs);
  text-align: center;

  position: absolute;
  z-index: var(--z-index-10);
}

.tooltip:hover .tooltip-title {
  visibility: visible;
}

.position-top {
  width: max-content;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.position-bottom {
  width: max-content;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
}

.position-right {
  top: 50%;
  transform: translateY(-50%);
  left: 105%;
}

.position-left {
  top: 50%;
  transform: translateY(-50%);
  right: 105%;
}

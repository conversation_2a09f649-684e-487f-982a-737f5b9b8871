import React from 'react';
import { render, waitFor } from '@testing-library/react';
import * as Router from 'next/router';
import * as userService from '@/services/user';
import * as Notistack from 'notistack';
import ValidarCuenta from './ValidarCuenta';

const tkn = '123456789';
const savedLocation = window.location;
describe('Tests ValidarCuenta', () => {
  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));

  beforeEach(() => {
    delete window.location;
    window.location = Object.assign(new URL('http://test.firme.se:3191/'), {
      search: `?tkn=${tkn}`,
    });
  });

  afterEach(() => {
    window.location = savedLocation;
    jest.clearAllMocks();
  });

  it('should call getTokenCode when component is mounted', async () => {
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: {
          codigoTransaccion: '987654321',
        },
      },
    }));

    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);

    render(<ValidarCuenta />);

    await waitFor(() => {
      expect(getTokenCodeMock).toHaveBeenCalled();
      expect(getTokenCodeMock).toHaveBeenCalledWith(tkn);
    });
  });

  it('should show error if getTokenCode response status is not 200', async () => {
    const message = 'Error';
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);

    const { findByText } = render(<ValidarCuenta />);

    expect(await findByText(message)).toBeInTheDocument();
  });

  it('should catch getTokenCode request exception', async () => {
    const error = new Error('Error');
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);

    const errorMock = jest.fn();
    jest.spyOn(console, 'error').mockImplementation(errorMock);

    render(<ValidarCuenta />);

    await waitFor(() => {
      expect(errorMock).toHaveBeenCalled();
      expect(errorMock).toHaveBeenCalledWith(error);
    });
  });

  it('should show send link token button if getTokenCode response status is not 200, message includes "Token supera la fecha de vencimiento" and textButton not includes "Ir al Registrar"', async () => {
    const message = 'Token supera la fecha de vencimiento';
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);

    const { findByText } = render(<ValidarCuenta />);

    expect(await findByText('Enviar nuevo link de verificación por correo')).toBeInTheDocument();
  });

  it('should send link with token when click on button "Enviar nuevo link de verificación por correo"', async () => {
    const message = 'Token supera la fecha de vencimiento';
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);

    const { findByText } = render(<ValidarCuenta />);

    const newTokenToValidateAccountMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        mensaje: 'OK',
      },
    }));
    jest.spyOn(userService, 'newTokenToValidateAccount').mockImplementation(newTokenToValidateAccountMock);

    const btn = await findByText('Enviar nuevo link de verificación por correo');
    btn.click();

    await waitFor(() => {
      expect(newTokenToValidateAccountMock).toHaveBeenCalled();
      expect(newTokenToValidateAccountMock).toHaveBeenCalledWith({ codigo: tkn });
    });

    expect(await findByText('Se envió el link nuevamente con éxito. Revisa tu correo.')).toBeInTheDocument();
  });

  it('should show error message and button redirect to register route if newTokenToValidateAccount response status is not 200', async () => {
    const message = 'Token supera la fecha de vencimiento';
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);

    const pushMock = jest.fn();
    jest.spyOn(Router, 'useRouter').mockImplementation(() => ({
      push: pushMock,
    }));

    const { findByText } = render(<ValidarCuenta />);

    const newTokenToValidateAccountMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
    }));
    jest.spyOn(userService, 'newTokenToValidateAccount').mockImplementation(newTokenToValidateAccountMock);

    const btn = await findByText('Enviar nuevo link de verificación por correo');
    btn.click();

    expect(await findByText('El link de verificación no es valido. Intenta registrarte nuevamente.')).toBeInTheDocument();

    const goToRegisterButton = await findByText(/Ir al Registrar/i);
    goToRegisterButton.click();

    await waitFor(() => {
      expect(pushMock).toHaveBeenCalled();
      expect(pushMock).toHaveBeenCalledWith('/registrar');
    });
  });

  it('should catch newTokenToValidateAccount request exception', async () => {
    const message = 'Token supera la fecha de vencimiento';
    const getTokenCodeMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message,
      },
    }));
    jest.spyOn(userService, 'getTokenCode').mockImplementation(getTokenCodeMock);

    const { findByText } = render(<ValidarCuenta />);

    const errorMock = jest.fn();
    jest.spyOn(console, 'error').mockImplementation(errorMock);

    const error = new Error('Error');
    const newTokenToValidateAccountMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'newTokenToValidateAccount').mockImplementation(newTokenToValidateAccountMock);

    const btn = await findByText('Enviar nuevo link de verificación por correo');
    btn.click();

    await waitFor(() => {
      expect(errorMock).toHaveBeenCalled();
      expect(errorMock).toHaveBeenCalledWith(error);
    });
  });
});

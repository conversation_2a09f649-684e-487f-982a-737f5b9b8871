import React from 'react';

import Grid from '@mui/material/Grid';
import Dropzone from '@/components/organisms/DropZone';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import Spacer from '@/components/layout/Spacer';

function UploadContainer() {
  return (
    <div>
      <Grid container>
        <Grid item lg={12}>
          <Heading size="sm">Subir documentos a la plataforma</Heading>
          <Paragraph color="muted" size="sm">
            En esta sección puedes subir los documentos que entran al proceso de
            firma electrónica con la aplicación
            {' '}
            <strong>FÍRMESE</strong>
          </Paragraph>
          <Spacer.Vertical size="sm" />
        </Grid>
        <Dropzone />

      </Grid>
    </div>
  );
}

export default UploadContainer;

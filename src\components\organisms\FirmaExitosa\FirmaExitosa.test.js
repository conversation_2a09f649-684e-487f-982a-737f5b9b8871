import React from 'react';
import { render, screen } from '@testing-library/react';
import FirmaExitosa from './FirmaExitosa';

jest.mock('@/components/organisms/TableNotifyDocumentSign', () => ({
  __esModule: true,
  default: jest.fn(() => 'TableNotifyDocumentSign mock'),
}));

describe('Tests FirmaExitosa', () => {
  beforeEach(() => {
    render(<FirmaExitosa />);
  });

  it('should call TableNotifyDocumentSign', () => {
    expect(screen.getByText('TableNotifyDocumentSign mock')).toBeInTheDocument();
  });
});

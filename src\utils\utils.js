/* eslint-disable no-mixed-operators */
import { isObject } from 'formik';

export const saveStorage = (key, value) => {
  try {
    if (key !== undefined && value !== undefined) {
      const serializedState = JSON.stringify(value);
      localStorage.setItem(key, serializedState);
    }
  } catch (error) {
    console.error(error);
  }
};

export const loadItemStorage = (key) => {
  try {
    const serializedState = localStorage.getItem(key);
    if (serializedState === null) {
      return undefined;
    }
    return JSON.parse(serializedState);
  } catch (error) {
    return undefined;
  }
};

export const saveState = (state) => {
  try {
    if (state !== undefined) {
      const serializedState = JSON.stringify(state);
      const timeToken = JSON.stringify(state.expires_in);
      localStorage.setItem('state', serializedState);
      localStorage.setItem('minutes', timeToken);
    }
  } catch (error) {
    console.error(error);
  }
};

export const saveHourLogin = (date) => {
  try {
    if (date !== undefined) {
      const serializedHourLogin = JSON.stringify(date);
      localStorage.setItem('login', serializedHourLogin);
    }
  } catch (error) {
    console.error(error);
  }
};

export const loadHourLogin = () => {
  try {
    const serializedHourLogin = localStorage.getItem('login');
    if (serializedHourLogin === null) {
      return undefined;
    }
    return JSON.parse(serializedHourLogin);
  } catch (error) {
    return undefined;
  }
};

export const loadTime = () => {
  try {
    const serializedTime = localStorage.getItem('minutes');
    if (serializedTime === null) {
      return undefined;
    }
    return JSON.parse(serializedTime);
  } catch (error) {
    return undefined;
  }
};

export const deleteState = () => {
  localStorage.clear();
};

export const isObjectEquals = (object1, object2) => {
  const keys1 = Object.keys(object1);
  const keys2 = Object.keys(object2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  // eslint-disable-next-line no-restricted-syntax
  for (const key of keys1) {
    const val1 = object1[key];
    const val2 = object2[key];
    const areObjects = isObject(val1) && isObject(val2);
    if (
      areObjects && !isObjectEquals(val1, val2) || !areObjects && val1 !== val2
    ) {
      return false;
    }
  }

  return true;
};

import React from 'react';
import { render } from '@testing-library/react';
import DataPolicyModal from './DataPolicyModal';

jest.mock('@/components/atoms/Modal', () => ({
  __esModule: true,
  default: jest.fn(({ title }) => <span>{title}</span>),
}));

describe('Tests DataPolicyModal', () => {
  it('should render correctly', () => {
    const { container } = render(<DataPolicyModal onClose={jest.fn()} />);
    expect(container).toHaveTextContent('POLÍTICA DE TRATAMIENTO DE DATOS PERSONALES');
  });
});

import React, { useRef, useState, useMemo } from 'react';
import { useForm } from 'react-hook-form';
import Input from '@/components/atoms/Input';
import isValidEmail from '@/utils/isValidEmail';
import Dropdown from '@/components/molecules/Dropdown';
import Grid from '@mui/material/Grid';
import Tooltip from '@mui/material/Tooltip';
import Button from '@/components/atoms/Button';
import Heading from '@/components/atoms/Heading';
import Spacer from '@/components/layout/Spacer';
import Divider from '@/components/atoms/Divider';
import PropTypes from 'prop-types';
import Error from '@/components/molecules/Error';

function OrisAddSignerForm({ handleCreate, onOrdenFirmaChange }) {
  const searchInput = useRef(null);

  const {
    register, handleSubmit, formState: {
      errors,
    },
    setValue,
    reset,
    watch,
  } = useForm({
    defaultValues: {
      tipoDocumento: '',
      numeroDocumento: '',
      fechaExpedicion: '',
      email: '',
      confirmEmail: '',
      numeroCelular: '',
      nombreCompleto: '',
      rol: 'Firmante',
    },
  });

  const [hasError, setHasError] = useState(false);

  const [userExists, setUserExists] = useState(false);
  const [hasSigns, setHasSigns] = useState(false);
  const [selectedOrdenFirma, setSelectedOrdenFirma] = useState('PARALELO');

  const canEditEmail = useMemo(() => !(userExists && hasSigns), [userExists, hasSigns]);

  const onSubmit = async (values) => {
    try {
      const { confirmEmail, ...signer } = values;

      handleCreate(signer);

      setUserExists(false);
    } catch (e) {
      setHasError(e.message);
      setTimeout(() => {
        setHasError(false);
      }, 1500);
    }
    searchInput.current.value = '';
    reset();
  };

  const getUser = async () => {
    setUserExists(false);
    setHasSigns(false);
    reset();
    if (!searchInput.current) {
      return false;
    }

    const { value } = searchInput.current;

    if (!value) {
      return false;
    }
    const { getUserByDocumentNumberOrEmail } = await import('@/services/user');

    const response = await getUserByDocumentNumberOrEmail(value);

    if (response.status === 200) {
      setUserExists(true);
      const { data } = response.data;

      const { firmas, ...user } = data;

      setHasSigns(firmas > 0);

      const replacers = {
        correoElectronico: 'email',
        fechaExpedicionDocumento: 'fechaExpedicion',
      };

      Object.entries(user).forEach(([key, val]) => {
        setValue(replacers[key] || key, val);
      });

      setValue('confirmEmail', user.correoElectronico);
    } else if (isValidEmail(value)) {
      setValue('email', value);
    } else {
      setValue('numeroDocumento', value);
    }
  };

  const handleOrdenFirmaChange = (value) => {
    setSelectedOrdenFirma(value);
    // Notificar al componente padre sobre el cambio
    if (onOrdenFirmaChange) {
      onOrdenFirmaChange(value);
    }

    // Si se selecciona Paralelo, establecer rol como Firmante
    if (value === 'PARALELO') {
      setValue('rol', 'Firmante');
    } else if (value === 'SECUENCIAL') {
      // Si se selecciona Secuencial, limpiar el rol para que el usuario elija
      setValue('rol', '');
    }
  };

  return (
    <>
      <Heading size="lg">1. Agregar firmantes</Heading>
      <Spacer.Vertical size="md" />

      <Grid container>
        <Grid item xs={12}>
          <label style={{
            fontSize: '16px',
            fontWeight: '500',
            color: 'rgba(0, 0, 0, 0.7)',
            marginBottom: '8px',
            display: 'block',
            fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
          }}
          >
            Orden de Firma:
          </label>
          <div style={{ marginTop: '8px' }}>
            <div style={{
              display: 'flex',
              flexDirection: 'row',
              gap: '20px',
              flexWrap: 'wrap',
              alignItems: 'center',
            }}
            >
              <Tooltip
                title="Paralelo: Todos los firmantes pueden firmar al mismo tiempo, sin orden específico. El documento se completa cuando todos han firmado."
                arrow
                placement="top"
              >
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  fontSize: '16px',
                  color: 'rgba(0, 0, 0, 0.6)',
                  fontWeight: '400',
                  fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                }}
                >
                  <input
                    type="radio"
                    name="ordenFirma"
                    value="PARALELO"
                    defaultChecked
                    onChange={(e) => handleOrdenFirmaChange(e.target.value)}
                    style={{
                      marginRight: '8px',
                      cursor: 'pointer',
                    }}
                  />
                  Paralelo (Cualquier orden)
                </label>
              </Tooltip>

              <Tooltip
                title="Secuencial: Los firmantes deben firmar en un orden específico. Cada firmante solo puede firmar después de que el anterior haya completado su firma."
                arrow
                placement="top"
              >
                <label style={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  fontSize: '16px',
                  color: 'rgba(0, 0, 0, 0.6)',
                  fontWeight: '400',
                  fontFamily: '"Roboto", "Helvetica", "Arial", sans-serif',
                }}
                >
                  <input
                    type="radio"
                    name="ordenFirma"
                    value="SECUENCIAL"
                    onChange={(e) => handleOrdenFirmaChange(e.target.value)}
                    style={{
                      marginRight: '8px',
                      cursor: 'pointer',
                    }}
                  />
                  Secuencial (Orden específico)
                </label>
              </Tooltip>
            </div>

            <p style={{
              fontSize: '12px',
              color: '#666',
              marginTop: '8px',
              fontStyle: 'italic',
            }}
            >
              Selecciona si los firmantes deben firmar en un orden específico
              o pueden hacerlo en cualquier momento.
            </p>
          </div>
        </Grid>
      </Grid>
      <Spacer.Vertical size="sm" />

      <Grid container>
        <Grid item xs={12} md={6}>
          <Input
            id="search"
            label="Buscar por número de documento o Email"
            onBlur={getUser}
            ref={searchInput}
          />
        </Grid>
      </Grid>
      <Spacer.Vertical size="sm" />
      <Divider />
      <Spacer.Vertical size="sm" />

      <form onSubmit={handleSubmit(onSubmit)}>
        <Grid container spacing={1}>
          <Grid
            item
            xs={12}
            md={6}
          >
            <Input
              id="numeroDocumento"
              label="Número de documento"
              type="number"
              disabled={userExists}
              hasError={Object.keys(errors).includes('numeroDocumento')}
              helperText={errors.numeroDocumento && errors.numeroDocumento.message}
              {
                ...register('numeroDocumento', {
                  required: 'El número de documento es obligatorio',
                })
              }
            />

            <Dropdown
              disabled={userExists}
              id="tipoDocumento"
              label="Tipo de documento"
              data-testid="tipoDocumento"
              hasError={Object.keys(errors).includes('tipoDocumento')}
              helperText={errors.tipoDocumento && errors.tipoDocumento.message}
              options={[
                {
                  text: 'Selecciona tipo de documento',
                  value: '',
                },
                {
                  text: 'Cédula de ciudadanía',
                  value: 'CC',
                },
                {
                  text: 'Cédula de extranjería',
                  value: 'CE',
                },
              ]}
              {
                ...register('tipoDocumento', {
                  required: 'El Tipo de Documento es obligatorio',
                })
              }
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <Input
              disabled={userExists}
              type="date"
              label="Fecha de expedición"
              {
                ...register('fechaExpedicion', {
                  required: 'La fecha de expedición es obligatoria',
                })
              }
              hasError={Object.keys(errors).includes('fechaExpedicion')}
              helperText={
                errors.fechaExpedicion && errors.fechaExpedicion.message
              }
              id="fechaExpedicion"
            />

            <Input
              disabled={userExists}
              label="Número de celular"
              type="number"
              {
                ...register('numeroCelular', {
                  required: 'El número de celular es obligatorio',
                  pattern: {
                    value: /^\d*$/,
                    message: 'El número de celular debe ser numérico',
                  },
                })
              }
              id="numeroCelular"
              hasError={Object.keys(errors).includes('numeroCelular')}
              helperText={errors.numeroCelular && errors.numeroCelular.message}
            />
          </Grid>
          <Grid item xs={12}>
            <Input
              disabled={userExists}
              label="Nombre completo"
              {
                ...register('nombreCompleto', {
                  required: 'El nombre completo es obligatorio',
                })
              }
              id="nombreCompleto"
              hasError={Object.keys(errors).includes('nombreCompleto')}
              helperText={errors.nombreCompleto && errors.nombreCompleto.message}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Dropdown
              id="rol"
              label="Rol del firmante"
              data-testid="rol"
              disabled={selectedOrdenFirma === 'PARALELO'}
              hasError={Object.keys(errors).includes('rol')}
              helperText={errors.rol && errors.rol.message}
              value={watch('rol')}
              options={[
                {
                  text: 'Selecciona un rol',
                  value: '',
                },
                {
                  text: 'Proyectó',
                  value: 'Proyectó',
                },
                {
                  text: 'Revisó',
                  value: 'Revisó',
                },
                {
                  text: 'Aprobó',
                  value: 'Aprobó',
                },
                {
                  text: 'Firmante',
                  value: 'Firmante',
                },
              ]}
              {
                ...register('rol', {
                  required: 'El rol es obligatorio',
                })
              }
            />
          </Grid>

          <Grid item xs={12} md={canEditEmail ? 6 : 12}>
            <Input
              disabled={!canEditEmail}
              label="Correo electrónico"
              type="email"
              {
                ...register('email', {
                  required: 'El correo electrónico es obligatorio',
                  pattern: {
                    value: /[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+/,
                    message: 'El correo electrónico no es válido',
                  },
                })
              }
              id="email"
              hasError={Object.keys(errors).includes('email')}
              helperText={errors.email && errors.email.message}
            />
          </Grid>
          {canEditEmail && (
            <Grid item xs={12} md={6}>
              <Input
                disabled={!canEditEmail}
                label="Confirmar correo electrónico"
                type="email"
                {
                  ...register('confirmEmail', {
                    required: 'La confirmación del correo electrónico es obligatoria',
                    pattern: {
                      value: /[^@ \t\r\n]+@[^@ \t\r\n]+\.[^@ \t\r\n]+/,
                      message: 'El correo electrónico no es válido',
                    },
                    validate: (value) => value === watch('email') || 'Los correos electrónicos no coinciden',
                  })
                }
                id="confirmEmail"
                hasError={Object.keys(errors).includes('confirmEmail')}
                helperText={errors.confirmEmail && errors.confirmEmail.message}
                disablePaste
              />
            </Grid>
          )}
          {hasError && (
          <Grid item xs={12}>
            <Error>
              {hasError}
            </Error>
          </Grid>
          )}
          <Grid container item xs={12} justifyContent="end">
            <Grid item xs={12} md={6}>
              <Button
                buttonType="submit"
                type="primary"
              >
                Agregar firmante a la tabla
              </Button>
            </Grid>
          </Grid>
        </Grid>
      </form>
      <Spacer.Vertical size="md" />
    </>
  );
}

OrisAddSignerForm.propTypes = {
  handleCreate: PropTypes.func.isRequired,
  onOrdenFirmaChange: PropTypes.func,
};

OrisAddSignerForm.defaultProps = {
  onOrdenFirmaChange: () => {},
};

export default OrisAddSignerForm;

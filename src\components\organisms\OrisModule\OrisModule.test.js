import {
  render, waitFor,
} from '@testing-library/react';
import * as Recoil from 'recoil';
import * as userService from '@/services/user';
import React from 'react';
import OrisModule, * as exportedFunctions from '@/components/organisms/OrisModule/OrisModule';

jest.mock('@/components/organisms/OrisModule/OrisModule', () => ({
  __esModule: true,
  ...jest.requireActual('@/components/organisms/OrisModule/OrisModule'),
}));

const signerMock = {
  numeroDocumento: 1234567890,
};

jest.mock('@/components/organisms/OrisAddSignerForm', () => ({
  __esModule: true,
  default: jest.fn(({ handleCreate }) => <button type="button" onClick={handleCreate}>OrisAddSignerForm mock</button>),
}));

jest.mock('@/components/organisms/OrisAddDocument', () => ({
  __esModule: true,
  default: jest.fn(({ handleSubmit }) => <button type="button" onClick={handleSubmit}>OrisAddDocument mock</button>),
}));
jest.mock('@/components/organisms/OrisSignersTable', () => ({
  __esModule: true,
  default: jest.fn(({ handleDelete }) => <button type="button" onClick={handleDelete}>OrisSignersTable mock</button>),
}));

jest.mock('@/components/organisms/StateHandler', () => ({
  __esModule: true,
  default: jest.fn(({ children }) => <div>{children}</div>),
}));

describe('Tests OrisModule', () => {
  const userMock = {
    idUsuarioC: 'idUsuarioC',
    access_token: 'access_token',
  };
  jest.spyOn(Recoil, 'useRecoilState').mockReturnValue([userMock, jest.fn()]);

  it('should render children', () => {
    const { getByText } = render(<OrisModule />);

    expect(getByText(/OrisAddSignerForm mock/i)).toBeInTheDocument();
    expect(getByText(/OrisAddDocument mock/i)).toBeInTheDocument();
    expect(getByText(/OrisSignersTable mock/i)).toBeInTheDocument();
  });

  describe('addNewSigner', () => {
    it('should add new signer if not exist', () => {
      let signers = [];
      const setSignersMock = jest.fn().mockImplementation((cb) => {
        signers = cb(signers);
      });
      exportedFunctions.addNewSigner({ signers: [], setSigners: setSignersMock })(signerMock);

      expect(setSignersMock).toHaveBeenCalled();
      expect(signers).toEqual([signerMock]);
    });

    it('should not add new signer if exist', () => {
      let signers = [signerMock];
      const setSignersMock = jest.fn().mockImplementation((cb) => {
        signers = cb(signers);
      });
      expect(() => {
        exportedFunctions.addNewSigner({ signers, setSigners: setSignersMock })(signerMock);
      }).toThrowError();

      expect(setSignersMock).not.toHaveBeenCalled();
      expect(signers).toEqual([signerMock]);
    });
  });

  describe('onDelete', () => {
    it('should delete signer', () => {
      let signers = [signerMock];
      const setSignersMock = jest.fn().mockImplementation((cb) => {
        signers = cb(signers);
      });
      exportedFunctions.onDelete({ setSigners: setSignersMock })(signerMock.numeroDocumento);

      expect(setSignersMock).toHaveBeenCalled();
      expect(signers).toEqual([]);
    });
  });

  describe('handleSubmit', () => {
    let sendOrisRequestMock;

    beforeEach(() => {
      sendOrisRequestMock = jest.spyOn(userService, 'sendOrisRequest');
    });

    it('should change isLoading to true when start and false when finish', async () => {
      let state = exportedFunctions.initialState;

      const setStateMock = jest.fn().mockImplementation((cb) => {
        state = cb(state);
      });

      const signersMock = [signerMock];
      const setSignersMock = jest.fn();

      const formValues = {
        tmpDocs: 'tmpDoc',
        tipoFirma: 'tipoFirma',
        fechaVigencia: 'fechaVigencia',
      };

      sendOrisRequestMock.mockResolvedValue({ status: 500, data: { mensaje: 'Error' } });

      exportedFunctions.handleSubmit({
        setState: setStateMock, signers: signersMock, setSigners: setSignersMock, user: userMock,
      })(formValues);

      expect(state.isLoading).toBeTruthy();
      await waitFor(() => expect(state.isLoading).toBeFalsy());
    });

    it('should set error message when signers is empty', async () => {
      let state = exportedFunctions.initialState;

      const setStateMock = jest.fn().mockImplementation((cb) => {
        state = cb(state);
      });

      const signersMock = [];
      const setSignersMock = jest.fn();

      const formValues = {
        tmpDocs: 'tmpDoc',
        tipoFirma: 'tipoFirma',
        fechaVigencia: 'fechaVigencia',
      };

      exportedFunctions.handleSubmit({
        setState: setStateMock, signers: signersMock, setSigners: setSignersMock, user: userMock,
      })(formValues);

      expect(state.errorMessage.content).toEqual('Debe agregar al menos 1 firmante');

      expect(sendOrisRequestMock).not.toHaveBeenCalled();
    });

    it('should set error message when sendOrisRequest return error', async () => {
      let state = exportedFunctions.initialState;

      const setStateMock = jest.fn().mockImplementation((cb) => {
        state = cb(state);
      });

      const signersMock = [signerMock];
      const setSignersMock = jest.fn();

      const formValues = {
        tmpDocs: 'tmpDoc',
        tipoFirma: 'tipoFirma',
        fechaVigencia: 'fechaVigencia',
      };

      const errorMessage = 'Error';

      sendOrisRequestMock.mockResolvedValue({ status: 500, data: { mensaje: errorMessage } });

      exportedFunctions.handleSubmit({
        setState: setStateMock, signers: signersMock, setSigners: setSignersMock, user: userMock,
      })(formValues);

      await waitFor(() => expect(state.errorMessage.content).toEqual(errorMessage));

      expect(sendOrisRequestMock).toHaveBeenCalled();
    });

    it('should call sendOrisRequest with correct params', async () => {
      let state = exportedFunctions.initialState;

      const setStateMock = jest.fn().mockImplementation((cb) => {
        state = cb(state);
      });

      const signersMock = [signerMock];
      const setSignersMock = jest.fn();

      const formValues = {
        tmpDoc: {
          nombreArchivo: 'a.pdf',
          archivo64: 'archivo64',
        },
        tipoFirma: 'tipoFirma',
        fechaVigencia: 'fechaVigencia',
      };

      const responseMock = {
        status: 200,
      };

      sendOrisRequestMock.mockResolvedValue(responseMock);

      exportedFunctions.handleSubmit({
        setState: setStateMock, signers: signersMock, setSigners: setSignersMock, user: userMock,
      })(formValues);

      const documentMock = {
        ...formValues.tmpDoc,
        cantidadFirmas: signersMock.length,
        idUsuario: userMock.idUsuarioC,
      };

      await waitFor(() => expect(sendOrisRequestMock).toHaveBeenCalledWith({
        firmantes: signersMock,
        documentos: [documentMock],
        tipoFirma: formValues.tipoFirma,
        fechaVigencia: formValues.fechaVigencia,
      }, userMock.access_token));
    });

    it('should set success message when sendOrisRequest return success', async () => {
      let state = exportedFunctions.initialState;

      const setStateMock = jest.fn().mockImplementation((cb) => {
        state = cb(state);
      });

      const signersMock = [signerMock];
      const setSignersMock = jest.fn();

      const formValues = {
        tmpDoc: {
          nombreArchivo: 'a.pdf',
          archivo64: 'archivo64',
        },
        tipoFirma: 'tipoFirma',
        fechaVigencia: 'fechaVigencia',
      };

      const responseMock = {
        status: 200,
        data: {
          mensaje: 'OK',
        },
      };

      sendOrisRequestMock.mockResolvedValue(responseMock);

      exportedFunctions.handleSubmit({
        setState: setStateMock, signers: signersMock, setSigners: setSignersMock, user: userMock,
      })(formValues);

      await waitFor(() => {
        expect(state.errorMessage.content).toEqual('Documento subido correctamente');
        expect(setSignersMock).toHaveBeenLastCalledWith([]);
      });
    });

    afterEach(() => {
      jest.clearAllMocks();
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });
});

import React, { useEffect } from 'react';
import InformationDocToSign from '@/components/organisms/InformationDocToSign';
import withAuthentication from '@/lib/withAuthentication';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';

export const getServerSideProps = withAuthentication;

export default function Informacion({ user }) {
  const [, setUser] = useRecoilState(userState);

  useEffect(() => {
    setUser(user);
  }, [user]);

  return (
    <InformationDocToSign />
  );
}

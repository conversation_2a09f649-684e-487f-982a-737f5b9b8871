import React from 'react';
import PropTypes from 'prop-types';

import withStyles from '@/hocs/withStyles';
import styles from './Paragraph.module.css';
import { options } from './constants';

export function Paragraph({
  getStyles,
  children,
  className,
  isStriked,
  isInline,
  isMonospace,
  isCentered,
  isTruncate,
  isJustify,
  id,
}) {
  return (
    <p
      id={id}
      className={getStyles(
        className,
        'paragraph',
        ['color', 'size', 'weight'],
        {
          'is-striked': isStriked,
          'is-inline': isInline,
          'is-monospace': isMonospace,
          'is-centered': isCentered,
          'is-truncate': isTruncate,
          'is-justify': isJustify,
        },
      )}
    >
      {children}
    </p>
  );
}

Paragraph.propTypes = {
  children: PropTypes.node.isRequired,
  getStyles: PropTypes.func.isRequired,
  color: PropTypes.oneOf(options.colors),
  size: PropTypes.oneOf(options.sizes),
  weight: PropTypes.oneOf(options.weights),
  className: PropTypes.string,
  isStriked: PropTypes.bool,
  isInline: PropTypes.bool,
  isCentered: PropTypes.bool,
  isMonospace: PropTypes.bool,
  isTruncate: PropTypes.bool,
  isJustify: PropTypes.bool,
  id: PropTypes.string,
};

Paragraph.defaultProps = {
  color: 'base',
  size: 'md',
  weight: 'normal',
  getStyles: () => ({}),
  isTruncate: false,
  isJustify: false,
};

export default withStyles(styles)(Paragraph);

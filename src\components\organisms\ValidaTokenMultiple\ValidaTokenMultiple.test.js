import React from 'react';
import { render, waitFor } from '@testing-library/react';
import * as Recoil from 'recoil';
import { checkAllValuesInDom } from '@/utils/forTesting';
import * as userService from '@/services/user';
import ValidateTokenMultiple from './index';

jest.mock('@/components/organisms/ValidateCode', () => ({
  __esModule: true,
  default: jest.fn((...props) => `ValidateCode mock ${props.map((prop) => (typeof prop === 'object' ? JSON.stringify(prop) : prop)).join(' ')}`),
}));

jest.mock('@/components/organisms/DocPreview', () => ({
  __esModule: true,
  default: jest.fn((...props) => `DocPreview mock ${props.map((prop) => (typeof prop === 'object' ? JSON.stringify(prop) : prop)).join(' ')}`),
}));

const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';
const docs = {
  idUsuario: '123',
  solicitante: 'John Doe',
  email: '<EMAIL>',
  archivos: {
    file1: {
      nombreArchivo: 'chucknorris.pdf',
      ip: '***********',
      cantidadFirmas: '9999',
      fechaRegistroStr: '2020-01-01',
    },
  },
};
describe('Tests ValidateTokenMultiple', () => {
  const userMock = {
    idUsuarioC: '123',
  };

  jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call verificarTokenMultipleFirma when component is mounted', async () => {
    const verificarTokenMultipleFirmaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: docs
        ,
      },
    }));
    jest.spyOn(userService, 'verificarTokenMultipleFirma').mockImplementation(verificarTokenMultipleFirmaMock);

    render(<Recoil.RecoilRoot><ValidateTokenMultiple token={token} /></Recoil.RecoilRoot>);

    await waitFor(() => {
      const excludedKeys = ['idUsuario', 'fechaRegistroStr'];
      checkAllValuesInDom(docs, excludedKeys, { insensitive: true });
    });
  });

  it('should error message if verificarTokenMultiple response status is 500', async () => {
    const error = 'error 500';
    const message = 'Error al verificar token multiple';
    const verificarTokenMultipleFirmaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 500,
      data: {
        error,
        mensaje: message
        ,
      },
    }));
    jest.spyOn(userService, 'verificarTokenMultipleFirma').mockImplementation(verificarTokenMultipleFirmaMock);

    const {
      findByText,
    } = render(<Recoil.RecoilRoot><ValidateTokenMultiple token={token} /></Recoil.RecoilRoot>);

    expect(await findByText(message)).toBeInTheDocument();
  });

  it('should error message if verificarTokenMultiple response status is not 200', async () => {
    const message = 'Error al verificar token multiple';
    const verificarTokenMultipleFirmaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 400,
      data: {
        mensaje: message
        ,
      },
    }));
    jest.spyOn(userService, 'verificarTokenMultipleFirma').mockImplementation(verificarTokenMultipleFirmaMock);

    const {
      findByText,
    } = render(<Recoil.RecoilRoot><ValidateTokenMultiple token={token} /></Recoil.RecoilRoot>);

    expect(await findByText(message)).toBeInTheDocument();
  });

  it('should catch verificarTokenMultiple request exception', async () => {
    const error = new Error('error');
    const verificarTokenMultipleFirmaMock = jest.fn()
      .mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'verificarTokenMultipleFirma').mockImplementation(verificarTokenMultipleFirmaMock);

    const errorMock = jest.fn();
    jest.spyOn(console, 'error').mockImplementation(errorMock);

    render(<Recoil.RecoilRoot><ValidateTokenMultiple token={token} /></Recoil.RecoilRoot>);

    await waitFor(() => {
      expect(errorMock).toHaveBeenCalled();
      expect(errorMock).toHaveBeenCalledWith(error);
    });
  });

  it('should call aceptarTokenMultipleFirma when click on "Aceptar firma de documento"', async () => {
    const verificarTokenMultipleFirmaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: docs
        ,
      },
    }));
    jest.spyOn(userService, 'verificarTokenMultipleFirma').mockImplementation(verificarTokenMultipleFirmaMock);

    const message = 'Success';
    const aceptarTokenMultipleFirmaMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: userMock.idUsuarioC,
        content: message,
      },
    }));
    jest.spyOn(userService, 'aceptarTokenMultipleFirma').mockImplementation(aceptarTokenMultipleFirmaMock);

    const {
      findByText,
      container,
    } = render(<Recoil.RecoilRoot><ValidateTokenMultiple token={token} /></Recoil.RecoilRoot>);

    const btn = await findByText('Aceptar firma de documento');
    btn.click();

    await waitFor(() => {
      expect(aceptarTokenMultipleFirmaMock).toHaveBeenCalled();
      expect(aceptarTokenMultipleFirmaMock).toHaveBeenCalledWith(token);

      expect(container).toHaveTextContent('ValidateCode mock');
      expect(container).toHaveTextContent(JSON.stringify(userMock));
      expect(container).toHaveTextContent(userMock.idUsuarioC);
      expect(container).toHaveTextContent(token);
    });
  });

  it('should render correctly if not has token', async () => {
    const verificarTokenMultipleFirmaMock = jest.fn();
    jest.spyOn(userService, 'verificarTokenMultipleFirma').mockImplementation(verificarTokenMultipleFirmaMock);

    const {
      container,
    } = render(<Recoil.RecoilRoot><ValidateTokenMultiple /></Recoil.RecoilRoot>);

    await waitFor(() => {
      expect(verificarTokenMultipleFirmaMock).not.toHaveBeenCalled();

      expect(container).toHaveTextContent('ValidateCode mock');
      expect(container).toHaveTextContent(JSON.stringify(userMock));
    });
  });
});

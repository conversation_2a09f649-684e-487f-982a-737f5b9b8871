.card {
  display: block;
  width: 100%;
  align-items: center;
  padding: 12px 10px;
  border: var(--border-width-thin) solid var(--color-base-transparent);
  border-radius: var(--card-border-radius);
  box-shadow: var(--box-shadow-sm);
  color: var(--color-font-base);
  transition: box-shadow 0.2s ease-in;
}

.size-sm {
  min-height: 45px;
}

.size-md {
  min-height: 80px;
}

.size-lg {
  min-height: 115px;
}

.color-primary {
  background: var(--color-primary);
  color: var(--color-primary-inverted);
}

.color-secondary {
  background: var(--color-secondary);
  color: var(--color-secondary-inverted);
}

.color-danger {
  background: var(--color-red-400);
  color: var(--color-primary-inverted);
}

.color-warning {
  background: var(--color-amber-400);
  color: var(--color-primary-inverted);
}

.color-base {
  border: var(--border-width-thin) solid var(--color-gray-400);
  background: var(--background-color-primary-highlight);
}

.is-clickable {
  cursor: pointer;
}

.is-draggable {
  cursor: grab;
  user-select: none;
}

.card.is-clickable:active,
.card.is-draggable:active {
  box-shadow: var(--box-shadow-xs);
}

.border-primary {
  border: var(--border-width-thin) solid var(--color-primary);
}

.border-secondary {
  border: var(--border-width-thin) solid var(--color-secondary);
}

.border-warning {
  border: var(--border-width-thin) solid var(--color-amber-500);
}

.border-danger {
  border: var(--border-width-thin) solid var(--color-red-500);
}

.is-inline {
  /* width: max-content; */
  max-width: 100%;
  height: fit-content;
}
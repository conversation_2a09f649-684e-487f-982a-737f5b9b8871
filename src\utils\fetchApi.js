import Axios from 'axios';
import getIp from './getIp';

const timeout = parseInt(process.env.API_TIMEOUT, 10) || 10000;

const fetchApi = Axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  timeout,
  maxContentLength: Infinity,
});

export function getDefaultHeaders(req) {
  if (!req) {
    return {};
  }

  try {
    let headers = {
      'x-forwarded-for': getIp(req),
      'user-agent': req.headers['user-agent'],
    };

    if (req.headers['x-coords']) {
      headers = {
        ...headers,
        'x-coords': req.headers['x-coords'],
      };
    }

    return headers;
  } catch (e) {
    return {};
  }
}

export default fetchApi;

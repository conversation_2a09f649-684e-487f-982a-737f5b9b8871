import React from 'react';
import { getClasses } from '@/helpers/styles';

const withStyles = (styles) => (WrappedComponent) => {
  const WithStylesComponent = React.forwardRef((props, ref) => {
    const allProps = { ...WrappedComponent.defaultProps, ...props };
    return (
      <WrappedComponent getStyles={getClasses(styles)(allProps)} {...props} ref={ref} />
    );
  });

  WithStylesComponent.displayName = WrappedComponent.displayName;

  return WithStylesComponent;
};

export default withStyles;

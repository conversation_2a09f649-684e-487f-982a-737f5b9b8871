import fetchApi, { getDefaultHeaders } from '@/utils/fetchApi';
import { withSessionRoute } from '@/lib/withSession';
import catchApiError from '@/utils/catchApiError';

async function enviarNotificacionRoute(req, res) {
  try {
    const { user } = req.session;
    if (!user?.access_token) {
      return res.status(401).send();
    }
    const { data } = await fetchApi.post(
      '/firma/manager/enviar-notificacion',
      req.body,
      {
        headers: {
          Authorization: `Bearer ${user.access_token}`,
          ...getDefaultHeaders(req),
        },
      },
    );
    return res.send(data);
  } catch (error) {
    return catchApiError(error, res);
  }
}

export default withSessionRoute(enviarNotificacionRoute);

import Modal from '@/components/atoms/Modal';
import Paragraph from '@/components/atoms/Paragraph';
import Spacer from '@/components/layout/Spacer';
import Image from 'next/image';
import gif from '@/assets/images/GIF-TUTORIAL-FIRMESE.gif';
import Icon from '@/components/atoms/Icon';

export function UserLocationErrorModal() {
  return (
    <Modal
      title={(
        <Icon
          name="information"
          size="lg"
          color="inverted"
          background="muted"
          isCentered
        />
      )}
      type="tertiary"
      isPermanent
    >
      <Spacer.Vertical size="xs" />
      <Paragraph size="sm" isJustify>
        El permiso de la ubicación es obligatorio para el correcto funcionamiento de la aplicación.
      </Paragraph>
      <Spacer.Vertical size="sm" />

      <Paragraph size="sm" isJustify>
        Para restablecer el permiso de ubicación sigue el instructivo
      </Paragraph>
      <Spacer.Vertical size="lg" />
      <div style={{
        width: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center',
      }}
      >
        <Image src={gif} alt="Instrucciones" style={{ borderRadius: 10 }} width="500" />
      </div>
    </Modal>
  );
}

export default UserLocationErrorModal;

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Image from 'next/image';
import Container from '@/components/layout/Container';
import Grid from '@mui/material/Grid';
import { getTokenCode, newTokenToValidateAccount } from '@/services/user';
import icon from '@/assets/images/Logo3.png';
import { useSnackbar } from 'notistack';
import StateHandler from '@/components/organisms/StateHandler';
import Button from '@/components/atoms/Button';
import Paragraph from '@/components/atoms/Paragraph';
import Error from '@/components/molecules/Error';
import Heading from '@/components/atoms/Heading';
import Spacer from '@/components/layout/Spacer';

const capturarCodigo = () => {
  const query = window.location.search;
  return query.substring(5, query.length);
};

export default function ValidarCuenta() {
  const router = useRouter();
  const [state, setState] = useState({
    haveError: false,
    isLoading: true,
    loadMessage: 'Estamos generando tu código',
    code: '',
    errorMessage: {
      header: 'Ha ocurrido un error en la obtención del código',
      content: 'Este mensaje es automático',
      icon,
    },
    emailMessage: '',
    textButton: 'Enviar nuevo link de verificación por correo',
  });
  const [botonActivo, setBotonActivo] = useState(false);

  const { enqueueSnackbar } = useSnackbar();

  useEffect(() => {
    const cod = capturarCodigo();

    getTokenCode(cod)
      .then((response) => {
        if (response.status === 200) {
          setState({
            ...state,
            isLoading: false,
            code: response.data.data.codigoTransaccion,
          });
        } else if (response.status !== 200) {
          setState({
            ...state,
            isLoading: false,
            haveError: true,
            content: response.data.mensaje,
          });
        }
      })
      .catch((err) => console.error(err));
  }, []);

  const handleErrorButton = async () => {
    enqueueSnackbar('Contacta con nuestro servicio técnico', { variant: 'error' });
    await router.replace('/login');
  };

  // redirigir a home (botón cuando ocurre un error)
  const GoHome = async () => {
    await router.push('/');
  };

  // redirigir a registro (cuando el token que se tiene no es válido)
  const GoRegister = async () => {
    await router.push('/registrar');
  };

  // Volver a enviar el token por correo
  const sendLinkToken = () => {
    setState({
      ...state,
      isLoading: true,
    });
    const codigo = capturarCodigo();
    // Se deshabilita el botón
    setBotonActivo(true);

    const body = {
      codigo,
    };

    newTokenToValidateAccount(body)
      .then((response) => {
        if (response.status === 200) {
          setState({
            ...state,
            isLoading: false,
            emailMessage: response.data.mensaje,
          });
        } else if (response.status !== 200) {
          setState({
            ...state,
            isLoading: false,
            emailMessage: 'El link de verificación no es valido. Intenta registrarte nuevamente.',
            textButton: 'Ir al Registrar',
          });

          // Habilitar el botón
          setBotonActivo(false);
        }
      })
      .catch((err) => console.error(err));
  };

  return (
    <div>
      {' '}
      {state.haveError ? (

        <Grid container justifyContent="center" style={{ textAlign: 'center' }}>
          <Grid item lg={12}>
            <Image alt="Logo" className="img-responsive" width={185} height={185} src={icon} id="iconFirmeseValidarCuenta" />
            {' '}
            <br />
            <Heading color="secondary" isCentered>
              Lo sentimos
            </Heading>
            <Paragraph size="sm" color="secondary">
              {state.content}
            </Paragraph>
            <Spacer.Vertical size="sm" />

            {router?.query?.view !== 'app' && state.content.includes('Token supera la fecha de vencimiento') ? (
              <div>
                <Button
                  isInline
                  disabled={botonActivo}
                  onClick={state.textButton.includes('Ir al Registrar') ? GoRegister : sendLinkToken}
                >
                  {state.textButton}
                </Button>
                <Spacer.Vertical size="sm" />
                {/* texto de retroalimentación de link por correo */}
                {state.emailMessage.includes('OK') ? (
                  <Paragraph size="sm" color="primary">
                    Se envió el link nuevamente con éxito. Revisa tu correo.
                  </Paragraph>
                ) : (
                  <Error>
                    {state.emailMessage}
                  </Error>
                )}

              </div>
            ) : router?.query?.view !== 'app' && (
              <Button isInline onClick={GoHome}>
                Ir al inicio
              </Button>
            )}

          </Grid>
        </Grid>

      ) : (
        <Container>
          <StateHandler handleErrorButton={handleErrorButton} state={state}>
            <Grid container justifyContent="center" style={{ textAlign: 'center' }}>
              <Grid item lg={10}>
                <Heading color="primary">
                  Validación de correo electrónico exitoso
                </Heading>
                <Spacer.Vertical size="lg" />

                <Paragraph isJustify size="sm" color="muted">
                  <strong>¡Felicitaciones!</strong>
                  {' '}
                  Ya completaste una parte del
                  Paso 1, ahora debes ir a la aplicación móvil
                  {' '}
                  <strong>Fírmese</strong>
                  {' '}
                  para completar el proceso de registro verificando tu identidad
                </Paragraph>
              </Grid>
            </Grid>
          </StateHandler>
        </Container>
      )}
    </div>
  );
}

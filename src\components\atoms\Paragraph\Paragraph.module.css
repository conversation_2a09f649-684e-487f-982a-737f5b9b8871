.paragraph {
  width: 100%;
  margin: 0;
}

.color-base {
  color: var(--color-font-base);
}

.color-inverted {
  color: var(--color-font-inverted);
}

.color-muted {
  color: var(--color-font-muted);
}

.color-primary {
  color: var(--color-primary);
}

.color-tertiary {
  color: var(--color-tertiary);
}

.size-xxs {
  font-size: var(--paragraph-font-size-xxs);
}

.size-xs {
  font-size: var(--paragraph-font-size-xs);
}

.size-sm {
  font-size: var(--paragraph-font-size-sm);
}

.size-md {
  font-size: var(--paragraph-font-size-md);
}

.size-lg {
  font-size: var(--paragraph-font-size-lg);
}

.weight-normal {
  font-weight: var(--font-weight-normal);
}

.weight-medium {
  font-weight: var(--font-weight-medium);
}

.weight-semibold {
  font-weight: var(--font-weight-semibold);
}

.is-striked {
  text-decoration: line-through;
}

.is-inline {
  display: inline-block;
  max-width: max-content;
}

.is-monospace {
  font-family: var(--font-family-mono);
}

.is-centered {
  text-align: center;
}

.is-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.is-truncate:hover {
  overflow: visible;
  background: var(--color-base-white);
  width: max-content;
  position: relative;
  box-shadow: 0 2px 4px 0 rgba(0,0,0,.28);
  border: 1px solid #eaebec;
  padding: 0 .5rem;
}

.is-justify {
  text-align: justify;
}
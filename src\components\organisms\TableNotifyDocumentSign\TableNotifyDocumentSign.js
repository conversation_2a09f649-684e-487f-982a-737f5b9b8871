import React, { useEffect, useState } from 'react';
import { loadItemStorage } from '@/utils/utils';
import icon from '@/assets/images/document-error-flat.png';
import dayjs from 'dayjs';
import Spacer from '@/components/layout/Spacer';
import Paragraph from '@/components/atoms/Paragraph';
import Heading from '@/components/atoms/Heading';
import { useRouter } from 'next/router';
import StatesHandler from '@/components/organisms/StateHandler';
import Document from '@/components/molecules/Document';
import Link from 'next/link';
import styles from './TableNotifyDocumentSign.module.css';

export default function TableNotifyDocumentSign() {
  const [state, setState] = useState({
    signedDocs: [],
  });
  const router = useRouter();

  const handleError = async () => {
    await router.push('/');
    setState((prev) => ({
      ...prev,
      haveError: false,
    }));
  };

  useEffect(() => {
    const loadedState = loadItemStorage('x-dta');
    if (!loadedState) {
      return setState({
        haveError: true,
        errorMessage: {
          header: 'Error en lista de documentos firmados',
          content: 'Por favor intenta nuevamente',
          title:
                        'No se pudo retornar la lista de documentos firmados',
          icon,
        },
      });
    }
    return setState({
      signedDocs: loadedState?.data || [],
    });
  }, []);

  return (
    <StatesHandler handleErrorButton={handleError} state={state}>
      <Heading size="xl" isCentered color="primary">
        ¡Proceso de firma terminado!
      </Heading>
      {router?.query?.view !== 'app' && (
        <>
          <Spacer.Vertical size="lg" />
          <Paragraph size="sm" isJustify>
            Para ver la lista de
            {' '}
            <strong>Documentos firmados</strong>
            {' '}
            por favor haz
            {' '}
            <Link href="/firmados?tab=firmados">click aquí</Link>
          </Paragraph>
          <Spacer.Vertical size="md" />
          <Paragraph size="sm" isJustify>
            En caso de que no puedas acceder a la plataforma debe hacer click en
            {' '}
            <Link href="/olvide-contrasena">Olvidé mi contraseña</Link>
            {' '}
            para que puedas asignar una nueva
          </Paragraph>
        </>
      )}
      <Spacer.Vertical size="lg" />
      <Heading>
        Estado de documentos en el proceso de firma
      </Heading>
      <Spacer.Vertical size="xs" />
      <div className={styles['grid-card']}>
        {state?.signedDocs?.map((doc) => {
          const statusColors = {
            Firmado: 'primary',
            'Pendiente de firma': 'warning',
          };

          const statusColor = statusColors[doc?.estado] || 'danger';
          return (
            <Document
              key={doc.idArchivo}
              nombreArchivo={doc.nombreArchivo}
              fechaRegistro={doc.fechaRegistro}
              hasCheck={false}
              border={statusColor}
              color={statusColor}
              moreInfo={{
                hour: dayjs(doc.fechaRegistro).format('hh:mm a'),
                state: (
                  <>
                    {doc?.resultadoFirma !== 'OK' ? (
                      <Paragraph size="xs" color="inverted">{doc?.resultadoFirma}</Paragraph>
                    ) : (
                      <Paragraph size="xs" color="inverted">&nbsp;</Paragraph>
                    )}
                    <Spacer.Vertical size="xs" />
                    <Paragraph size="xs" color="inverted">{doc.estado}</Paragraph>
                  </>
                ),
              }}
            />
          );
        })}
      </div>
      <Spacer.Vertical size="lg" />
    </StatesHandler>
  );
}

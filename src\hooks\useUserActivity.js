import { useCallback, useRef } from 'react';

export const useUserActivity = () => {
  const lastActivityRef = useRef(Date.now());
  const isUserActiveRef = useRef(false);

  const updateActivity = useCallback(() => {
    lastActivityRef.current = Date.now();
    isUserActiveRef.current = true;
    // Resetear la actividad después de 5 segundos
    setTimeout(() => {
      isUserActiveRef.current = false;
    }, 5000);
  }, []);

  const isUserActive = useCallback(() => {
    const timeSinceActivity = Date.now() - lastActivityRef.current;
    const recentActivity = timeSinceActivity < 10000; // 10 segundos
    return isUserActiveRef.current || recentActivity;
  }, []);

  const getLastActivityTime = useCallback(() => lastActivityRef.current, []);

  return {
    updateActivity,
    isUserActive,
    getLastActivityTime,
  };
};

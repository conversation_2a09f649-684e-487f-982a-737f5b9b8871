import React, { useState, useEffect } from 'react';
import Heading from '@/components/atoms/Heading';
import Paragraph from '@/components/atoms/Paragraph';
import ButtonGroup from '@/components/atoms/ButtonGroup';
import Button from '@/components/atoms/Button';
import Spacer from '@/components/layout/Spacer';
import Input from '@/components/atoms/Input';
import { fileToBase64String } from '@/utils/fileAdmin';
import PropTypes from 'prop-types';
import Textarea from '@/components/atoms/Textarea';
import clsx from 'clsx';
import { useRecoilState } from 'recoil';
import { userState } from '@/recoil/atoms';
import { obtenerPlantillas } from '@/services/user';
import { useSnackbar } from 'notistack';
import Loading from '@/components/atoms/Loading';
import { addNewSigner } from '@/components/organisms/OrisModule/OrisModule';

import styles from './OrisAddDocument.module.css';

export const initialErrorsState = {
  documento: false,
  tipoFirma: false,
  fechaVigencia: false,
  descripcion: false,
  tipoDocumento: false,
};

// Tipos de documento
const TIPO_DOCUMENTO = {
  SOLO_ARCHIVOS: 'SOLO_ARCHIVOS',
  SOLO_PLANTILLAS: 'SOLO_PLANTILLAS',
  ARCHIVOS_Y_PLANTILLAS: 'ARCHIVOS_Y_PLANTILLAS',
};

export const onSubmit = ({
  setErrors, form, documentos, plantillasSeleccionadas, tipoDocumento, handleSubmit, signers,
}) => async (e) => {
  e.preventDefault();
  setErrors(initialErrorsState);
  let hasErrors = false;

  // Validar según el tipo de documento seleccionado
  if (tipoDocumento === TIPO_DOCUMENTO.SOLO_ARCHIVOS) {
    if (!documentos || !documentos.length) {
      await setErrors((prev) => ({
        ...prev,
        documento: 'Al menos un documento es requerido',
      }));
      hasErrors = true;
    }
  } else if (tipoDocumento === TIPO_DOCUMENTO.SOLO_PLANTILLAS) {
    if (!plantillasSeleccionadas || !plantillasSeleccionadas.length) {
      await setErrors((prev) => ({
        ...prev,
        documento: 'Al menos una plantilla es requerida',
      }));
      hasErrors = true;
    }
  } else if (tipoDocumento === TIPO_DOCUMENTO.ARCHIVOS_Y_PLANTILLAS) {
    if ((!documentos || !documentos.length) && (!plantillasSeleccionadas || !plantillasSeleccionadas.length)) {
      await setErrors((prev) => ({
        ...prev,
        documento: 'Debe seleccionar al menos una plantilla o subir un documento',
      }));
      hasErrors = true;
    }
  }

  // Validaciones comunes
  if (!form.tipoFirma) {
    await setErrors((prev) => ({
      ...prev,
      tipoFirma: 'El tipo de firma es requerido',
    }));
    hasErrors = true;
  }

  if (!form.fechaVigencia) {
    await setErrors((prev) => ({
      ...prev,
      fechaVigencia: 'La fecha de vigencia es requerida',
    }));
    hasErrors = true;
  }

  if (!form.descripcion) {
    await setErrors((prev) => ({
      ...prev,
      descripcion: 'La descripción es requerida',
    }));
    hasErrors = true;
  }

  if (hasErrors) return false;

  // Procesar documentos de forma segura
  const documentosArchivos = documentos && documentos.length > 0 ? await Promise.all(
    documentos.map(async (doc) => {
      const uri = await fileToBase64String(doc);
      const b64 = uri.substring(uri.indexOf(',') + 1);
      return {
        nombreArchivo: doc.name,
        archivo64: b64,
        descripcion: form.descripcion,
        cantidadFirmas: signers.length,
      };
    }),
  ) : undefined;

  // Procesar plantillas de forma segura
  const plantillasData = plantillasSeleccionadas && plantillasSeleccionadas.length > 0 
    ? plantillasSeleccionadas.map((plantilla) => ({
      idPlantilla: plantilla.id,
      descripcion: plantilla.descripcion || form.descripcion,
    }))
    : undefined;

  // CORREGIR: Construir el objeto incluyendo TODOS los campos del form
  const payload = {
    ...form, // Esto debería incluir tipoOrden
    ...(plantillasData && { plantillas: plantillasData }),
    ...(documentosArchivos && { documentos: documentosArchivos }),
  };

  // Validar que hay al menos plantillas o documentos
  if (!payload.plantillas && !payload.documentos) {
    await setErrors((prev) => ({
      ...prev,
      documento: 'Debe seleccionar al menos una plantilla o subir un documento',
    }));
    return false;
  }

  handleSubmit(payload);
  return true;
};

function OrisAddDocument({
  handleSubmit,
  signers,
  setSigners,
  tipoOrdenFirma,
  isTableConfirmed, // Nueva prop
}) {
  const [user] = useRecoilState(userState);
  const { enqueueSnackbar } = useSnackbar();

  // LOG TEMPORAL: Ver qué datos tiene el usuario
  useEffect(() => {
    console.log('=== DATOS DEL USUARIO EN ORIS ===');
    console.log('User completo:', user);
    console.log('Propiedades de nombre disponibles:', {
      nombreCompleto: user?.nombreCompleto,
      name: user?.name,
      nombres: user?.nombres,
      apellidos: user?.apellidos,
      primerNombre: user?.primerNombre,
      primerApellido: user?.primerApellido,
      firstName: user?.firstName,
      lastName: user?.lastName,
      email: user?.email,
    });
  }, [user]);

  const [form, setForm] = useState({
    tipoFirma: 'OTHERS',
    tipoOrden: 'PARALELO',
    fechaVigencia: '',
    descripcion: '',
  });

  const [errors, setErrors] = useState(initialErrorsState);
  const [documentos, setDocumentos] = useState([]);
  const [tipoDocumento, setTipoDocumento] = useState(TIPO_DOCUMENTO.SOLO_ARCHIVOS);
  const [plantillas, setPlantillas] = useState([]);
  const [plantillasSeleccionadas, setPlantillasSeleccionadas] = useState([]);
  const [cargandoPlantillas, setCargandoPlantillas] = useState(false);

  // Estado para controlar si ya se estableció el rol inicial
  const [rolInicialEstablecido, setRolInicialEstablecido] = useState(false);

  // Definir cargarPlantillas antes de usarla en useEffect
  const cargarPlantillas = async () => {
    if (!user?.access_token) {
      console.log('No access token disponible');
      return;
    }

    console.log('Iniciando carga de plantillas...');
    setCargandoPlantillas(true);

    try {
      const response = await obtenerPlantillas(user.access_token);
      console.log('Respuesta plantillas:', response);

      if (response.status === 200 && Array.isArray(response.data)) {
        // Adaptar la estructura de plantillas reales y filtrar por usuario
        const plantillasAdaptadas = response.data
          .filter((plantilla) => plantilla.activa) // Solo plantillas activas
          .filter((plantilla) => plantilla.idUsuarioCreador === user.idUsuarioC) // NUEVO: Filtrar por usuario creador
          .map((plantilla) => ({
            id: plantilla.idPlantilla,
            nombre: plantilla.nombrePlantilla || plantilla.nombreArchivo,
            descripcion: plantilla.descripcion,
            nombreArchivo: plantilla.nombreArchivo,
            hashArchivo: plantilla.hashArchivo,
            tipoFirma: plantilla.tipoFirma,
            fechaCreacion: plantilla.fechaCreacion,
            idUsuarioCreador: plantilla.idUsuarioCreador,
            tipoDocumento: plantilla.tipoDocumento,
          }));

        console.log('Plantillas del usuario actual:', plantillasAdaptadas);
        console.log('Usuario actual ID:', user.idUsuarioC);
        setPlantillas(plantillasAdaptadas);
      } else {
        console.log('Error en respuesta o datos vacíos:', response);
        enqueueSnackbar('Error al cargar las plantillas', { variant: 'error' });
        setPlantillas([]);
      }
    } catch (error) {
      console.error('Error completo al cargar plantillas:', error);
      enqueueSnackbar('Error al cargar las plantillas', { variant: 'error' });
      setPlantillas([]);
    } finally {
      setCargandoPlantillas(false);
    }
  };

  // Cargar plantillas cuando se selecciona tipo plantillas
  useEffect(() => {
    if (
      tipoDocumento === TIPO_DOCUMENTO.SOLO_PLANTILLAS
      || tipoDocumento === TIPO_DOCUMENTO.ARCHIVOS_Y_PLANTILLAS
    ) {
      cargarPlantillas();
    }
  }, [tipoDocumento]);

  // NUEVA FUNCIÓN: Agregar usuario logueado a la tabla
  const agregarUsuarioLogueado = () => {
    if (!user?.email || !user?.idUsuarioC || !signers || !setSigners) {
      return;
    }

    // Verificar si el usuario ya está en la tabla
    const usuarioExiste = signers.some(
      (signer) => signer.email === user.email,
    );

    if (usuarioExiste) {
      return;
    }

    // Determinar el rol basado en el tipo de orden
    const rol = tipoOrdenFirma === 'SECUENCIAL' ? 'Proyectó' : 'Firmante';

    // Usar nombreUsuario que viene del token JWT
    const nombreCompleto = user.nombreUsuario || user.email.split('@')[0];

    console.log('Datos del usuario para nombre:', {
      user,
      nombreCompleto,
      email: user.email,
    });

    // Crear objeto del usuario solicitante
    const usuarioSolicitante = {
      email: user.email,
      nombreCompleto,
      rol,
      tipoDocumento: user.tipoDocumento || 'CC',
      numeroDocumento: user.numeroDocumento || user.idUsuarioC?.toString(),
      fechaExpedicion: user.fechaExpedicion || '2000-01-01',
      numeroCelular: user.numeroCelular || user.telefono || '0000000000',
      esUsuarioSolicitante: true,
    };

    console.log('Usuario solicitante creado:', usuarioSolicitante);

    // Agregar usando la función existente
    const agregarFirmante = addNewSigner({ signers, setSigners });
    agregarFirmante(usuarioSolicitante);

    enqueueSnackbar(`Te has agregado como firmante con rol: ${rol}`, { variant: 'success' });
  };

  // NUEVA FUNCIÓN: Remover usuario logueado de la tabla
  const removerUsuarioLogueado = () => {
    if (!user?.email || !signers || !setSigners) return;

    const usuarioExiste = signers.some(
      (signer) => signer.email === user.email && signer.esUsuarioSolicitante,
    );

    if (usuarioExiste) {
      const nuevosSigners = signers.filter(
        (signer) => !(signer.email === user.email && signer.esUsuarioSolicitante),
      );

      setSigners(nuevosSigners);
      enqueueSnackbar('Te has removido de la tabla de firmantes', { variant: 'info' });
    }
  };

  // NUEVA FUNCIÓN: Manejar el cambio de tipo de firma
  const handleTipoFirmaChange = (tipoFirma) => {
    if (!signers || !setSigners) return; // Validar que las props existan

    if (tipoFirma === 'MULTIPLE') {
      agregarUsuarioLogueado();
    } else if (tipoFirma === 'OTHERS') {
      removerUsuarioLogueado();
    }
  };

  const handleChangeInput = (e) => {
    if (e.target.name === 'documento') {
      const nuevosArchivos = Array.from(e.target.files);
      const archivosFiltrados = nuevosArchivos.filter(
        (nuevoArchivo) => !documentos.some(
          (archivoExistente) => archivoExistente.name === nuevoArchivo.name,
        ),
      );

      if (archivosFiltrados.length > 0) {
        setDocumentos((prevDocumentos) => [...prevDocumentos, ...archivosFiltrados]);
      }
    } else if (e.target.name === 'tipoFirma') {
      const nuevoTipoFirma = e.target.value;
      setForm((prev) => ({ ...prev, [e.target.name]: nuevoTipoFirma }));

      // NUEVA FUNCIONALIDAD: Agregar/remover usuario logueado
      handleTipoFirmaChange(nuevoTipoFirma);
    } else {
      setForm((prev) => ({ ...prev, [e.target.name]: e.target.value }));
    }
  };

  // NUEVO EFFECT: Actualizar rol cuando cambia el tipo de orden
  useEffect(() => {
    if (form.tipoFirma === 'MULTIPLE' && signers && setSigners && !rolInicialEstablecido) {
      const usuarioEnTabla = signers.find(
        (signer) => signer.email === user?.email && signer.esUsuarioSolicitante,
      );

      if (usuarioEnTabla) {
        const nuevoRol = tipoOrdenFirma === 'SECUENCIAL' ? 'Proyectó' : 'Firmante';

        if (usuarioEnTabla.rol !== nuevoRol) {
          const signersActualizados = signers.map((signer) => {
            if (signer.email === user?.email && signer.esUsuarioSolicitante) {
              return { ...signer, rol: nuevoRol };
            }
            return signer;
          });

          setSigners(signersActualizados);
          setRolInicialEstablecido(true); // Marcar que ya se estableció el rol inicial
          enqueueSnackbar(`Tu rol se ha establecido como: ${nuevoRol}`, { variant: 'info' });
        }
      }
    }
  }, [form.tipoFirma, user, signers, setSigners, rolInicialEstablecido, tipoOrdenFirma, enqueueSnackbar]);

  // Sincronizar tipoOrden con la prop tipoOrdenFirma
  useEffect(() => {
    if (tipoOrdenFirma && tipoOrdenFirma !== form.tipoOrden) {
      setForm((prev) => ({
        ...prev,
        tipoOrden: tipoOrdenFirma,
      }));
    }
  }, [tipoOrdenFirma]);

  const handleTipoDocumentoChange = (tipo) => {
    setTipoDocumento(tipo);
    // Limpiar selecciones al cambiar tipo
    if (tipo === TIPO_DOCUMENTO.SOLO_PLANTILLAS) {
      setDocumentos([]);
    } else if (tipo === TIPO_DOCUMENTO.SOLO_ARCHIVOS) {
      setPlantillasSeleccionadas([]);
    }
  };

  const handlePlantillaToggle = (plantilla) => {
    setPlantillasSeleccionadas((prev) => {
      const existe = prev.find((p) => p.id === plantilla.id);
      if (existe) {
        return prev.filter((p) => p.id !== plantilla.id);
      }
      return [...prev, plantilla];
    });
  };

  const removeFile = (index) => {
    setDocumentos((prevDocumentos) => prevDocumentos.filter((_, i) => i !== index));
  };

  const removePlantilla = (plantillaId) => {
    setPlantillasSeleccionadas((prev) => prev.filter((p) => p.id !== plantillaId));
  };

  const mostrarCargaArchivos = tipoDocumento === TIPO_DOCUMENTO.SOLO_ARCHIVOS
    || tipoDocumento === TIPO_DOCUMENTO.ARCHIVOS_Y_PLANTILLAS;
  const mostrarPlantillas = tipoDocumento === TIPO_DOCUMENTO.SOLO_PLANTILLAS
    || tipoDocumento === TIPO_DOCUMENTO.ARCHIVOS_Y_PLANTILLAS;

  return (
    <>
      <Heading>2. Agregar Documentos</Heading>
      <Spacer.Vertical size="md" />

      {/* Selector de tipo de documento */}
      <Paragraph size="sm" weight="semibold">Tipo de documento a incluir:</Paragraph>
      <Spacer.Vertical size="xs" />

      <div style={{
        display: 'flex', flexDirection: 'column', gap: '8px', marginBottom: '16px',
      }}
      >
        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
          <input
            type="radio"
            name="tipoDocumento"
            value={TIPO_DOCUMENTO.SOLO_ARCHIVOS}
            checked={tipoDocumento === TIPO_DOCUMENTO.SOLO_ARCHIVOS}
            onChange={() => handleTipoDocumentoChange(TIPO_DOCUMENTO.SOLO_ARCHIVOS)}
            style={{ marginRight: '8px' }}
          />
          <Paragraph size="sm">Solo archivos</Paragraph>
        </label>

        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
          <input
            type="radio"
            name="tipoDocumento"
            value={TIPO_DOCUMENTO.SOLO_PLANTILLAS}
            checked={tipoDocumento === TIPO_DOCUMENTO.SOLO_PLANTILLAS}
            onChange={() => handleTipoDocumentoChange(TIPO_DOCUMENTO.SOLO_PLANTILLAS)}
            style={{ marginRight: '8px' }}
          />
          <Paragraph size="sm">Solo plantillas del sistema</Paragraph>
        </label>

        <label style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
          <input
            type="radio"
            name="tipoDocumento"
            value={TIPO_DOCUMENTO.ARCHIVOS_Y_PLANTILLAS}
            checked={tipoDocumento === TIPO_DOCUMENTO.ARCHIVOS_Y_PLANTILLAS}
            onChange={() => handleTipoDocumentoChange(TIPO_DOCUMENTO.ARCHIVOS_Y_PLANTILLAS)}
            style={{ marginRight: '8px' }}
          />
          <Paragraph size="sm">Archivos y plantillas</Paragraph>
        </label>
      </div>

      <p className={styles.hasError}>{errors.tipoDocumento || ''}</p>

      {/* Carga de archivos */}
      {mostrarCargaArchivos && (
        <div>
          <Paragraph size="sm">
            Selecciona uno o más archivos en formato
            {' '}
            <b>PDF</b>
          </Paragraph>
          <Spacer.Vertical size="xs" />

          {documentos.length > 0 && (
            <div style={{ marginTop: '10px', marginBottom: '10px' }}>
              <Paragraph size="xs"><strong>Archivos seleccionados:</strong></Paragraph>
              <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                {documentos.map((doc, index) => (
                  <li key={index} style={{ display: 'flex', alignItems: 'center', marginBottom: '5px' }}>
                    <Paragraph size="xs" style={{ flex: 1 }}>{doc.name}</Paragraph>
                    <Button
                      type="danger-outlined"
                      size="xs"
                      isInline
                      onClick={() => removeFile(index)}
                      style={{ marginLeft: '10px' }}
                    >
                      ✕
                    </Button>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Selección de plantillas */}
      {mostrarPlantillas && (
        <div style={{ marginBottom: '16px' }}>
          <Paragraph size="sm" weight="semibold">Plantillas disponibles:</Paragraph>
          <Spacer.Vertical size="xs" />

          {cargandoPlantillas ? (
            <Loading isShown />
          ) : (
            <>
              {plantillas.length === 0 ? (
                <div style={{
                  padding: '16px',
                  textAlign: 'center',
                  backgroundColor: '#f8f9fa',
                  borderRadius: '4px',
                  border: '1px solid #e9ecef',
                }}
                >
                  <Paragraph size="xs" color="muted">
                    No tienes plantillas creadas.
                  </Paragraph>
                  <Paragraph size="xs" color="muted">
                    Solo se muestran las plantillas que hayas creado tú.
                  </Paragraph>
                </div>
              ) : (
                <div style={{
                  maxHeight: '300px',
                  overflowY: 'auto',
                  border: '1px solid #e0e0e0',
                  borderRadius: '4px',
                  padding: '8px',
                }}
                >
                  {plantillas.map((plantilla) => (
                    <div
                      key={plantilla.id}
                      style={{
                        display: 'flex',
                        alignItems: 'center',
                        padding: '8px',
                        borderBottom: '1px solid #f0f0f0',
                      }}
                    >
                      <input
                        type="checkbox"
                        checked={plantillasSeleccionadas.some((p) => p.id === plantilla.id)}
                        onChange={() => handlePlantillaToggle(plantilla)}
                        style={{ marginRight: '8px' }}
                      />
                      <div style={{ flex: 1 }}>
                        <Paragraph size="xs" weight="semibold">{plantilla.nombre}</Paragraph>
                        <Paragraph size="xs" color="muted">{plantilla.nombreArchivo}</Paragraph>
                        {plantilla.descripcion && (
                          <Paragraph size="xs" color="muted">{plantilla.descripcion}</Paragraph>
                        )}
                        <Paragraph size="xxs" color="muted">
                          Tipo:
                          {' '}
                          {plantilla.tipoFirma}
                          {' '}
                          | Documento:
                          {' '}
                          {plantilla.tipoDocumento}
                        </Paragraph>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              {plantillasSeleccionadas.length > 0 && (
                <div style={{ marginTop: '10px' }}>
                  <Paragraph size="xs"><strong>Plantillas seleccionadas:</strong></Paragraph>
                  <ul style={{ margin: '5px 0', paddingLeft: '20px' }}>
                    {plantillasSeleccionadas.map((plantilla) => (
                      <li key={plantilla.id} style={{ display: 'flex', alignItems: 'center', marginBottom: '5px' }}>
                        <Paragraph size="xs" style={{ flex: 1 }}>{plantilla.nombre}</Paragraph>
                        <Button
                          type="danger-outlined"
                          size="xs"
                          isInline
                          onClick={() => removePlantilla(plantilla.id)}
                          style={{ marginLeft: '10px' }}
                        >
                          ✕
                        </Button>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </>
          )}
        </div>
      )}

      <form onSubmit={onSubmit({
        documentos,
        plantillasSeleccionadas,
        tipoDocumento,
        handleSubmit,
        form,
        errors,
        setErrors,
        signers,
      })}
      >
        {mostrarCargaArchivos && (
          <>
            <input
              type="file"
              className={styles.docSelector}
              accept="application/pdf"
              name="documento"
              onChange={handleChangeInput}
              required={tipoDocumento === TIPO_DOCUMENTO.SOLO_ARCHIVOS}
              multiple
              data-testid="upload-doc"
            />
            <p className={styles.hasError}>{errors.documento || ''}</p>
          </>
        )}

        <Paragraph size="xs">Tipo de solicitud de firma</Paragraph>
        <Spacer.Vertical size="xs" />
        <ButtonGroup>
          <Button
            component="label"
            type="secondary"
            isInline
          >
            <input
              type="radio"
              name="tipoFirma"
              value="MULTIPLE"
              onChange={handleChangeInput}
              checked={form.tipoFirma === 'MULTIPLE'}
              required
            />
            &nbsp;
            Otros y yo
          </Button>

          <Button
            component="label"
            type="secondary"
            isInline
          >
            <input
              type="radio"
              name="tipoFirma"
              value="OTHERS"
              onChange={handleChangeInput}
              checked={form.tipoFirma === 'OTHERS'}
              required
            />
            &nbsp;
            Solo otros
          </Button>
        </ButtonGroup>

        {/* NUEVO: Mostrar información cuando está en modo "Otros y yo" */}
        {form.tipoFirma === 'MULTIPLE' && (
          <div style={{
            backgroundColor: '#e3f2fd',
            border: '1px solid #2196f3',
            borderRadius: '4px',
            padding: '12px',
            marginTop: '8px',
          }}
          >
            <Paragraph size="xs" style={{ margin: 0, color: '#1976d2' }}>
              ℹ️ Te has agregado automáticamente como firmante con rol:
              {' '}
              {tipoOrdenFirma === 'SECUENCIAL' ? 'Proyectó' : 'Firmante'}
            </Paragraph>
          </div>
        )}

        <p className={styles.hasError}>{errors.tipoFirma || ''}</p>

        <Spacer.Vertical size="xs" />
        <Input
          id="fechaVigencia"
          name="fechaVigencia"
          type="date"
          label="Fecha de vigencia del documento"
          onChange={handleChangeInput}
          value={form.fechaVigencia}
          required
        />
        <p className={styles.hasError}>{errors.fechaVigencia || ''}</p>

        <label
          htmlFor="descripcion"
          className={clsx(errors?.descripcion && styles.hasError)}
        >
          Descripción
        </label>
        <Textarea
          id="descripcion"
          name="descripcion"
          onChange={handleChangeInput}
          value={form.descripcion} // AGREGAR esta línea
        />
        <p className={styles.hasError}>{errors.descripcion || ''}</p>

        <Spacer.Vertical size="sm" />
        {/* Indicador de estado de confirmación de tabla */}
        {signers.length > 0 && (
          <div style={{
            padding: '12px 16px',
            borderRadius: '6px',
            border: '1px solid',
            marginBottom: '16px',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            backgroundColor: isTableConfirmed ? '#e6f7f0' : '#fff3cd',
            borderColor: isTableConfirmed ? '#33CC99' : '#ffc107',
          }}
          >
            <div style={{
              fontSize: '16px',
              color: isTableConfirmed ? '#33CC99' : '#856404',
            }}
            >
              {isTableConfirmed ? '✓' : '⚠️'}
            </div>
            <p style={{
              margin: '0',
              fontSize: '14px',
              fontWeight: '500',
              color: isTableConfirmed ? '#2a6d5d' : '#856404',
            }}
            >
              {isTableConfirmed
                ? 'Tabla de firmantes confirmada - Lista para solicitar firma'
                : 'Debes confirmar la tabla de firmantes antes de solicitar la firma'}
            </p>
          </div>
        )}

        <div style={{ display: 'flex', textAlign: 'right', justifyContent: 'end' }}>
          <Button
            isInline
            buttonType="submit"
            disabled={signers.length > 0 && !isTableConfirmed}
            style={{
              opacity: (signers.length > 0 && !isTableConfirmed) ? 0.6 : 1,
              cursor: (signers.length > 0 && !isTableConfirmed) ? 'not-allowed' : 'pointer',
            }}
          >
            Solicitar Firma
          </Button>
        </div>
      </form>
    </>
  );
}

OrisAddDocument.propTypes = {
  handleSubmit: PropTypes.func.isRequired,
  signers: PropTypes.arrayOf(PropTypes.shape({
    email: PropTypes.string.isRequired,
    nombreCompleto: PropTypes.string.isRequired,
    rol: PropTypes.string.isRequired,
    tipoDocumento: PropTypes.string.isRequired,
    numeroDocumento: PropTypes.string.isRequired,
    fechaExpedicion: PropTypes.string.isRequired,
    numeroCelular: PropTypes.string.isRequired,
    esUsuarioSolicitante: PropTypes.bool,
  })),
  setSigners: PropTypes.func,
  tipoOrdenFirma: PropTypes.string,
  isTableConfirmed: PropTypes.bool, // Nueva prop
};

OrisAddDocument.defaultProps = {
  signers: [],
  setSigners: null,
  tipoOrdenFirma: 'PARALELO',
  isTableConfirmed: false,
};

export default OrisAddDocument;

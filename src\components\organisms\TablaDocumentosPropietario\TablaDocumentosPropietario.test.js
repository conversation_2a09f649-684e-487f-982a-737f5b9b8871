import React from 'react';
import { render, waitFor } from '@testing-library/react';
import { RecoilRoot } from 'recoil';
import * as Recoil from 'recoil';
import * as userService from '@/services/user';
import * as Notistack from 'notistack';
import { QueryClient, QueryClientProvider } from 'react-query';
import TablaDocumentosPropietario from './index';

describe('Tests SignedDocs', () => {
  const userMock = {
    idUsuarioC: '1',
  };
  jest.spyOn(Recoil, 'useRecoilState').mockImplementation(() => [userMock, jest.fn()]);
  const enqueueSnackbarMock = jest.fn();
  jest.spyOn(Notistack, 'useSnackbar').mockImplementation(() => ({
    enqueueSnackbar: enqueueSnackbarMock,
  }));

  const queryClient = new QueryClient();
  const component = (
    <QueryClientProvider client={queryClient}>
      <RecoilRoot>
        <TablaDocumentosPropietario />
      </RecoilRoot>
    </QueryClientProvider>
  );

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should call getDocsPropietario', async () => {
    const getDocsPropietarioMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        content: [],
        number: 1,
        totalElementes: 2,
      },
    }));
    jest.spyOn(userService, 'getDocsPropietario').mockImplementation(getDocsPropietarioMock);
    render(component);

    await waitFor(() => {
      expect(getDocsPropietarioMock).toHaveBeenCalled();
    });
  });

  it('should call getVerifyQR when click on row', async () => {
    const hashArchivo = 'QAJHAAS78798==';
    const fileName = 'chucknorris.pdf';
    const fileInfo = {
      nombreArchivo: fileName,
      fechaRegistroStr: '2020-05-05',
      tipoFirma: 'Firmo yo y otros',
      totalFirmas: '1',
      cantidadConsultas: '0',
      propietario: 'NO',
      hashArchivo,
      idArchivo: 1,
      ip: '***********',
    };
    const getDocsPropietarioMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        content: [fileInfo],
        empty: false,
        first: true,
        last: true,
        numberOfElements: 5,
        pageable: {
          offset: 0,
          pageNumber: 0,
          pageSize: 5,
          paged: true,
          sort: {
            sorted: true,
            unsorted: false,
            empty: false,
          },
          unpaged: false,
        },
        size: 5,
        sort: {
          sorted: true,
          unsorted: false,
          empty: false,
        },
        totalElements: 5,
        totalPages: 1,
      },
    }));
    jest.spyOn(userService, 'getDocsPropietario').mockImplementation(getDocsPropietarioMock);

    const getVerifyQRMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        data: [fileInfo],
      },
    }));
    jest.spyOn(userService, 'getVerifyQR').mockImplementation(getVerifyQRMock);
    const {
      findByText,
    } = render(component);

    const document = await findByText(fileName);
    document.click();

    await waitFor(() => {
      expect(getVerifyQRMock).toHaveBeenCalled();
      expect(getVerifyQRMock).toHaveBeenCalledWith(hashArchivo);
    });
  });

  it('should catch getVerifyQR request exception', async () => {
    const hashArchivo = 'QAJHAAS78798==';
    const fileName = 'chucknorris.pdf';
    const fileInfo = {
      nombreArchivo: fileName,
      fechaRegistroStr: '2020-05-05',
      tipoFirma: 'Firmo yo y otros',
      totalFirmas: '1',
      cantidadConsultas: '0',
      propietario: 'NO',
      hashArchivo,
      idArchivo: 1,
      ip: '***********',
    };
    const getDocsPropietarioMock = jest.fn().mockImplementation(() => Promise.resolve({
      status: 200,
      data: {
        content: [fileInfo],
        empty: false,
        first: true,
        last: true,
        numberOfElements: 5,
        pageable: {
          offset: 0,
          pageNumber: 0,
          pageSize: 5,
          paged: true,
          sort: {
            sorted: true,
            unsorted: false,
            empty: false,
          },
          unpaged: false,
        },
        size: 5,
        sort: {
          sorted: true,
          unsorted: false,
          empty: false,
        },
        totalElements: 5,
        totalPages: 1,
      },
    }));
    jest.spyOn(userService, 'getDocsPropietario').mockImplementation(getDocsPropietarioMock);

    const error = new Error('Error');
    const getVerifyQRMock = jest.fn().mockImplementation(() => Promise.reject(error));
    jest.spyOn(userService, 'getVerifyQR').mockImplementation(getVerifyQRMock);

    const errorLogMock = jest.fn();
    jest.spyOn(console, 'error').mockImplementation(errorLogMock);

    const {
      findByText,
    } = render(component);

    const document = await findByText(fileName);
    document.click();

    await waitFor(() => {
      expect(errorLogMock).toHaveBeenCalled();
      expect(errorLogMock).toHaveBeenCalledWith(error);
    });
  });
});

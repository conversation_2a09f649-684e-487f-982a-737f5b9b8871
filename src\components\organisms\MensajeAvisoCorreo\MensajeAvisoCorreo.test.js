import React from 'react';
import { render } from '@testing-library/react';
import MensajeAvisoCorreo from './MensajeAvisoCorreo';

const email = '<EMAIL>';
describe('Tests MensajeAvisoCorreo', () => {
  it('should show email', () => {
    const { container } = render(<MensajeAvisoCorreo mail={email} />);
    expect(container).toHaveTextContent(email);
  });

  it('should show 3 steps', () => {
    const { container } = render(<MensajeAvisoCorreo mail={email} />);
    expect(container).toHaveTextContent('Paso 1.');
    expect(container).toHaveTextContent('Paso 2.');
    expect(container).toHaveTextContent('Paso 3.');
  });
});

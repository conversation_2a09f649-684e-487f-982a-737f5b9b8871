.grid {
    display: flex;
    grid-gap: 1rem;
    flex-direction: row;
    justify-content: space-around;
    margin-bottom: 40px;
}

.info-container{
    width: 100%;
    margin-top: 20px;
}

.row-info{
    flex-direction: row;
    display: flex;
}

.text-bold{
    font-weight: bold;
    color: var(--color-font-base);
}

.text-normal{
    color: var(--color-font-base);
}

@media (max-width: 920px) {
    .grid{
        flex-direction: column;
    }
}

.preview {
    height: auto;
    width: 100%;
    object-fit: cover;
    object-position: top left;
    background: var(--color-base-white);
}

.preview-container {
    display: flex;
    flex: 1;
    flex-wrap: wrap;
}

.grid {
    display: flex;
    flex-direction: column-reverse;
    grid-gap: 1rem;
}

@media (min-width: 768px) {
    .grid {
        display: grid;
        grid-template-columns: 50% 50%;
    }

}

@media (max-width: 768px) {
    .preview-container > .preview-info-container {
        margin-top: 15px;
    }
}

.table{
    text-align: center !important;
    border: solid var(--border-width-thin) var(--color-primary);
    border-radius: var(--card-border-radius) !important;
    border-collapse: separate;
}

.tableHead{
    text-align: center !important;
    color: var(--color-shiny-shamrock);
}